{"abi": [{"type": "constructor", "inputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "isPluginAllowed", "inputs": [{"name": "plugin", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "owner", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "renounceOwnership", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferOwnership", "inputs": [{"name": "new<PERSON>wner", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "updatePlugin", "inputs": [{"name": "plugin", "type": "address", "internalType": "address"}, {"name": "allowed", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "OwnershipTransferred", "inputs": [{"name": "previousOwner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "error", "name": "OwnableInvalidOwner", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "OwnableUnauthorizedAccount", "inputs": [{"name": "account", "type": "address", "internalType": "address"}]}], "bytecode": {"object": "0x6080604052348015600e575f5ffd5b503380603357604051631e4fbdf760e01b81525f600482015260240160405180910390fd5b603a81603f565b50608e565b5f80546001600160a01b038381166001600160a01b0319831681178455604051919092169283917f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e09190a35050565b61029d8061009b5f395ff3fe608060405234801561000f575f5ffd5b5060043610610055575f3560e01c80631b9d032e14610059578063715018a61461006e5780638da5cb5b14610076578063a50833cd14610095578063f2fde38b146100b8575b5f5ffd5b61006c61006736600461020e565b6100cb565b005b61006c6100fd565b5f546040516001600160a01b0390911681526020015b60405180910390f35b6100a86100a3366004610247565b610110565b604051901515815260200161008c565b61006c6100c6366004610247565b61013b565b6100d361017d565b6001600160a01b03919091165f908152600160205260409020805460ff1916911515919091179055565b61010561017d565b61010e5f6101a9565b565b5f61011961017d565b506001600160a01b0381165f9081526001602052604090205460ff165b919050565b61014361017d565b6001600160a01b03811661017157604051631e4fbdf760e01b81525f60048201526024015b60405180910390fd5b61017a816101a9565b50565b5f546001600160a01b0316331461010e5760405163118cdaa760e01b8152336004820152602401610168565b5f80546001600160a01b038381166001600160a01b0319831681178455604051919092169283917f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e09190a35050565b80356001600160a01b0381168114610136575f5ffd5b5f5f6040838503121561021f575f5ffd5b610228836101f8565b91506020830135801515811461023c575f5ffd5b809150509250929050565b5f60208284031215610257575f5ffd5b610260826101f8565b939250505056fea2646970667358221220b056c2de5b867bf56f0ac85cdd5ae2a9f6ac8219cda34c28ccbd8a6e50905abc64736f6c634300081c0033", "sourceMap": "209:410:37:-:0;;;313:36;;;;;;;;;-1:-1:-1;335:10:37;;1269:95:53;;1322:31;;-1:-1:-1;;;1322:31:53;;1350:1;1322:31;;;160:51:193;133:18;;1322:31:53;;;;;;;1269:95;1373:32;1392:12;1373:18;:32::i;:::-;1225:187;209:410:37;;2912:187:53;2985:16;3004:6;;-1:-1:-1;;;;;3020:17:53;;;-1:-1:-1;;;;;;3020:17:53;;;;;;3052:40;;3004:6;;;;;;;3052:40;;2985:16;3052:40;2975:124;2912:187;:::o;14:203:193:-;209:410:37;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "0x608060405234801561000f575f5ffd5b5060043610610055575f3560e01c80631b9d032e14610059578063715018a61461006e5780638da5cb5b14610076578063a50833cd14610095578063f2fde38b146100b8575b5f5ffd5b61006c61006736600461020e565b6100cb565b005b61006c6100fd565b5f546040516001600160a01b0390911681526020015b60405180910390f35b6100a86100a3366004610247565b610110565b604051901515815260200161008c565b61006c6100c6366004610247565b61013b565b6100d361017d565b6001600160a01b03919091165f908152600160205260409020805460ff1916911515919091179055565b61010561017d565b61010e5f6101a9565b565b5f61011961017d565b506001600160a01b0381165f9081526001602052604090205460ff165b919050565b61014361017d565b6001600160a01b03811661017157604051631e4fbdf760e01b81525f60048201526024015b60405180910390fd5b61017a816101a9565b50565b5f546001600160a01b0316331461010e5760405163118cdaa760e01b8152336004820152602401610168565b5f80546001600160a01b038381166001600160a01b0319831681178455604051919092169283917f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e09190a35050565b80356001600160a01b0381168114610136575f5ffd5b5f5f6040838503121561021f575f5ffd5b610228836101f8565b91506020830135801515811461023c575f5ffd5b809150509250929050565b5f60208284031215610257575f5ffd5b610260826101f8565b939250505056fea2646970667358221220b056c2de5b867bf56f0ac85cdd5ae2a9f6ac8219cda34c28ccbd8a6e50905abc64736f6c634300081c0033", "sourceMap": "209:410:37:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;355:118;;;;;;:::i;:::-;;:::i;:::-;;2293:101:53;;;:::i;1638:85::-;1684:7;1710:6;1638:85;;-1:-1:-1;;;;;1710:6:53;;;690:51:193;;678:2;663:18;1638:85:53;;;;;;;;479:138:37;;;;;;:::i;:::-;;:::i;:::-;;;1108:14:193;;1101:22;1083:41;;1071:2;1056:18;479:138:37;943:187:193;2543:215:53;;;;;;:::i;:::-;;:::i;355:118:37:-;1531:13:53;:11;:13::i;:::-;-1:-1:-1;;;;;434:22:37;;;::::1;;::::0;;;:14:::1;:22;::::0;;;;:32;;-1:-1:-1;;434:32:37::1;::::0;::::1;;::::0;;;::::1;::::0;;355:118::o;2293:101:53:-;1531:13;:11;:13::i;:::-;2357:30:::1;2384:1;2357:18;:30::i;:::-;2293:101::o:0;479:138:37:-;565:4;1531:13:53;:11;:13::i;:::-;-1:-1:-1;;;;;;588:22:37;::::1;;::::0;;;:14:::1;:22;::::0;;;;;::::1;;1554:1:53;479:138:37::0;;;:::o;2543:215:53:-;1531:13;:11;:13::i;:::-;-1:-1:-1;;;;;2627:22:53;::::1;2623:91;;2672:31;::::0;-1:-1:-1;;;2672:31:53;;2700:1:::1;2672:31;::::0;::::1;690:51:193::0;663:18;;2672:31:53::1;;;;;;;;2623:91;2723:28;2742:8;2723:18;:28::i;:::-;2543:215:::0;:::o;1796:162::-;1684:7;1710:6;-1:-1:-1;;;;;1710:6:53;735:10:77;1855:23:53;1851:101;;1901:40;;-1:-1:-1;;;1901:40:53;;735:10:77;1901:40:53;;;690:51:193;663:18;;1901:40:53;544:203:193;2912:187:53;2985:16;3004:6;;-1:-1:-1;;;;;3020:17:53;;;-1:-1:-1;;;;;;3020:17:53;;;;;;3052:40;;3004:6;;;;;;;3052:40;;2985:16;3052:40;2975:124;2912:187;:::o;14:173:193:-;82:20;;-1:-1:-1;;;;;131:31:193;;121:42;;111:70;;177:1;174;167:12;192:347;257:6;265;318:2;306:9;297:7;293:23;289:32;286:52;;;334:1;331;324:12;286:52;357:29;376:9;357:29;:::i;:::-;347:39;;436:2;425:9;421:18;408:32;483:5;476:13;469:21;462:5;459:32;449:60;;505:1;502;495:12;449:60;528:5;518:15;;;192:347;;;;;:::o;752:186::-;811:6;864:2;852:9;843:7;839:23;835:32;832:52;;;880:1;877;870:12;832:52;903:29;922:9;903:29;:::i;:::-;893:39;752:186;-1:-1:-1;;;752:186:193:o", "linkReferences": {}}, "methodIdentifiers": {"isPluginAllowed(address)": "a50833cd", "owner()": "8da5cb5b", "renounceOwnership()": "715018a6", "transferOwnership(address)": "f2fde38b", "updatePlugin(address,bool)": "1b9d032e"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"OwnableInvalidOwner\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"OwnableUnauthorizedAccount\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousOwner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"OwnershipTransferred\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"plugin\",\"type\":\"address\"}],\"name\":\"isPluginAllowed\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"renounceOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"transferOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"plugin\",\"type\":\"address\"},{\"internalType\":\"bool\",\"name\":\"allowed\",\"type\":\"bool\"}],\"name\":\"updatePlugin\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"errors\":{\"OwnableInvalidOwner(address)\":[{\"details\":\"The owner is not a valid owner account. (eg. `address(0)`)\"}],\"OwnableUnauthorizedAccount(address)\":[{\"details\":\"The caller account is not authorized to perform an operation.\"}]},\"kind\":\"dev\",\"methods\":{\"isPluginAllowed(address)\":{\"details\":\"Checks if a plugin is allowed.This function is a view function and does not alter state.\",\"params\":{\"plugin\":\"The address of the plugin to check.\"},\"returns\":{\"_0\":\"A boolean value indicating whether the plugin is allowed (true) or disallowed (false).\"}},\"owner()\":{\"details\":\"Returns the address of the current owner.\"},\"renounceOwnership()\":{\"details\":\"Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner.\"},\"transferOwnership(address)\":{\"details\":\"Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner.\"},\"updatePlugin(address,bool)\":{\"details\":\"Updates the allowed status of a plugin.Emits no events.\",\"params\":{\"allowed\":\"A boolean value indicating whether the plugin should be allowed (true) or disallowed (false).\",\"plugin\":\"The address of the plugin to be updated.\"}}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"updatePlugin(address,bool)\":{\"notice\":\"This function is restricted to the owner of the contract.\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/tokens/PluginRegistry.sol\":\"PluginRegistry\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":1inch/=lib/1inch/\",\":@1inch/=lib/1inch/\",\":@mangrovedao/mangrove-core/=lib/mangrove-core/\",\":@mgv/forge-std/=lib/mangrove-core/lib/forge-std/src/\",\":@mgv/lib/=lib/mangrove-core/lib/\",\":@mgv/script/=lib/mangrove-core/script/\",\":@mgv/src/=lib/mangrove-core/src/\",\":@mgv/test/=lib/mangrove-core/test/\",\":@morpho-org/morpho-blue/=lib/morpho-blue/\",\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ExcessivelySafeCall/=lib/ExcessivelySafeCall/src/\",\":core/=lib/mangrove-core/lib/core/\",\":ds-test/=lib/ExcessivelySafeCall/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/openzeppelin-contracts/lib/forge-std/src/\",\":halmos-cheatcodes/=lib/morpho-blue/lib/halmos-cheatcodes/src/\",\":mangrove-core/=lib/mangrove-core/\",\":morpho-blue/=lib/morpho-blue/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":preprocessed/=lib/mangrove-core/lib/preprocessed/\"]},\"sources\":{\"contracts/interfaces/tokens/IPluginRegistry.sol\":{\"keccak256\":\"0x9a677620d88ac7dc42afb21d82a7b7a89bd934c1cada5450cf2b6200bf374ccf\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://304091e5d54e9ad7db24ba1022e84e39dd9305d9cc72fd87423c71b40de4ab3d\",\"dweb:/ipfs/QmZEF5MfmUcxzLF9fcGCLvGMTTLLhcWdCMCDK2WyXj6s7X\"]},\"contracts/tokens/PluginRegistry.sol\":{\"keccak256\":\"0x9263d71fc32da7d0ca4f8d272f8d75d565c1f06281952481322983bae9d7b488\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c9dcbc64172f4339547865b4f041826f0de5d464900f316edbe72e7d6bfb396d\",\"dweb:/ipfs/QmQykSWuY8xLJotWUPgG5JQDS5DmA2E5Hjb4c6Bz4YnbBQ\"]},\"lib/openzeppelin-contracts/contracts/access/Ownable.sol\":{\"keccak256\":\"0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6\",\"dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a\"]},\"lib/openzeppelin-contracts/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "type": "error", "name": "OwnableInvalidOwner"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "type": "error", "name": "OwnableUnauthorizedAccount"}, {"inputs": [{"internalType": "address", "name": "previousOwner", "type": "address", "indexed": true}, {"internalType": "address", "name": "new<PERSON>wner", "type": "address", "indexed": true}], "type": "event", "name": "OwnershipTransferred", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "plugin", "type": "address"}], "stateMutability": "view", "type": "function", "name": "isPluginAllowed", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "renounceOwnership"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "transferOwnership"}, {"inputs": [{"internalType": "address", "name": "plugin", "type": "address"}, {"internalType": "bool", "name": "allowed", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "updatePlugin"}], "devdoc": {"kind": "dev", "methods": {"isPluginAllowed(address)": {"details": "Checks if a plugin is allowed.This function is a view function and does not alter state.", "params": {"plugin": "The address of the plugin to check."}, "returns": {"_0": "A boolean value indicating whether the plugin is allowed (true) or disallowed (false)."}}, "owner()": {"details": "Returns the address of the current owner."}, "renounceOwnership()": {"details": "Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner."}, "transferOwnership(address)": {"details": "Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner."}, "updatePlugin(address,bool)": {"details": "Updates the allowed status of a plugin.Emits no events.", "params": {"allowed": "A boolean value indicating whether the plugin should be allowed (true) or disallowed (false).", "plugin": "The address of the plugin to be updated."}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"updatePlugin(address,bool)": {"notice": "This function is restricted to the owner of the contract."}}, "version": 1}}, "settings": {"remappings": ["1inch/=lib/1inch/", "@1inch/=lib/1inch/", "@mangrovedao/mangrove-core/=lib/mangrove-core/", "@mgv/forge-std/=lib/mangrove-core/lib/forge-std/src/", "@mgv/lib/=lib/mangrove-core/lib/", "@mgv/script/=lib/mangrove-core/script/", "@mgv/src/=lib/mangrove-core/src/", "@mgv/test/=lib/mangrove-core/test/", "@morpho-org/morpho-blue/=lib/morpho-blue/", "@openzeppelin/=lib/openzeppelin-contracts/", "ExcessivelySafeCall/=lib/ExcessivelySafeCall/src/", "core/=lib/mangrove-core/lib/core/", "ds-test/=lib/ExcessivelySafeCall/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/openzeppelin-contracts/lib/forge-std/src/", "halmos-cheatcodes/=lib/morpho-blue/lib/halmos-cheatcodes/src/", "mangrove-core/=lib/mangrove-core/", "morpho-blue/=lib/morpho-blue/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "preprocessed/=lib/mangrove-core/lib/preprocessed/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/tokens/PluginRegistry.sol": "PluginRegistry"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"contracts/interfaces/tokens/IPluginRegistry.sol": {"keccak256": "0x9a677620d88ac7dc42afb21d82a7b7a89bd934c1cada5450cf2b6200bf374ccf", "urls": ["bzz-raw://304091e5d54e9ad7db24ba1022e84e39dd9305d9cc72fd87423c71b40de4ab3d", "dweb:/ipfs/QmZEF5MfmUcxzLF9fcGCLvGMTTLLhcWdCMCDK2WyXj6s7X"], "license": "MIT"}, "contracts/tokens/PluginRegistry.sol": {"keccak256": "0x9263d71fc32da7d0ca4f8d272f8d75d565c1f06281952481322983bae9d7b488", "urls": ["bzz-raw://c9dcbc64172f4339547865b4f041826f0de5d464900f316edbe72e7d6bfb396d", "dweb:/ipfs/QmQykSWuY8xLJotWUPgG5JQDS5DmA2E5Hjb4c6Bz4YnbBQ"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/access/Ownable.sol": {"keccak256": "0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb", "urls": ["bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6", "dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Context.sol": {"keccak256": "0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2", "urls": ["bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12", "dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF"], "license": "MIT"}}, "version": 1}, "id": 37}