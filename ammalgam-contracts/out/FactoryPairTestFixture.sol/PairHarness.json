{"abi": [{"type": "function", "name": "IS_TEST", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "borrow", "inputs": [{"name": "to", "type": "address", "internalType": "address"}, {"name": "amountXAssets", "type": "uint256", "internalType": "uint256"}, {"name": "amountYAssets", "type": "uint256", "internalType": "uint256"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "borrowLiquidity", "inputs": [{"name": "to", "type": "address", "internalType": "address"}, {"name": "borrowAmountLAssets", "type": "uint256", "internalType": "uint256"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "burn", "inputs": [{"name": "to", "type": "address", "internalType": "address"}], "outputs": [{"name": "amountXAssets", "type": "uint256", "internalType": "uint256"}, {"name": "amountYAssets", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "deposit", "inputs": [{"name": "to", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "excludeArtifacts", "inputs": [], "outputs": [{"name": "excludedArtifacts_", "type": "string[]", "internalType": "string[]"}], "stateMutability": "view"}, {"type": "function", "name": "excludeContracts", "inputs": [], "outputs": [{"name": "excludedContracts_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "excludeSelectors", "inputs": [], "outputs": [{"name": "excludedSelectors_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzSelector[]", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "selectors", "type": "bytes4[]", "internalType": "bytes4[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "excludeSenders", "inputs": [], "outputs": [{"name": "excludedSenders_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "exposed_accrueInterestToToken", "inputs": [{"name": "tokenType", "type": "uint256", "internalType": "uint256"}, {"name": "amount", "type": "uint128", "internalType": "uint128"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "exposed_activeLiquidityAssets", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "exposed_externalLiquidity", "inputs": [], "outputs": [{"name": "", "type": "uint112", "internalType": "uint112"}], "stateMutability": "view"}, {"type": "function", "name": "exposed_getAssets", "inputs": [{"name": "tokenType", "type": "uint256", "internalType": "uint256"}, {"name": "user", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "exposed_getAssets", "inputs": [{"name": "user", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256[6]", "internalType": "uint256[6]"}], "stateMutability": "view"}, {"type": "function", "name": "exposed_getDepositAndBorrowAndActiveLiquidityAssets", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "exposed_getLendingStateTick", "inputs": [{"name": "lastLendingCumulativeSum", "type": "int56", "internalType": "int56"}, {"name": "duration", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "lendingStateTick", "type": "int16", "internalType": "int16"}], "stateMutability": "view"}, {"type": "function", "name": "exposed_getTickRange", "inputs": [{"name": "currentTick", "type": "int16", "internalType": "int16"}], "outputs": [{"name": "", "type": "int16", "internalType": "int16"}, {"name": "", "type": "int16", "internalType": "int16"}], "stateMutability": "view"}, {"type": "function", "name": "exposed_getTickRange", "inputs": [], "outputs": [{"name": "", "type": "int16", "internalType": "int16"}, {"name": "", "type": "int16", "internalType": "int16"}], "stateMutability": "view"}, {"type": "function", "name": "exposed_getTickRangeWithoutLongTerm", "inputs": [], "outputs": [{"name": "", "type": "int16", "internalType": "int16"}, {"name": "", "type": "int16", "internalType": "int16"}], "stateMutability": "view"}, {"type": "function", "name": "exposed_getTickRangeWithoutLongTerm", "inputs": [{"name": "currentTick", "type": "int16", "internalType": "int16"}], "outputs": [{"name": "", "type": "int16", "internalType": "int16"}, {"name": "", "type": "int16", "internalType": "int16"}], "stateMutability": "view"}, {"type": "function", "name": "exposed_getTotalAssetsCached", "inputs": [{"name": "tokenType", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "tokenTotalAssets", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "exposed_missingAssets", "inputs": [], "outputs": [{"name": "", "type": "uint112", "internalType": "uint112"}, {"name": "", "type": "uint112", "internalType": "uint112"}], "stateMutability": "view"}, {"type": "function", "name": "exposed_observations", "inputs": [], "outputs": [{"name": "", "type": "tuple", "internalType": "struct GeometricTWAP.Observations", "components": [{"name": "isMidTermBufferInitialized", "type": "bool", "internalType": "bool"}, {"name": "isLongTermBufferInitialized", "type": "bool", "internalType": "bool"}, {"name": "midTermIndex", "type": "uint8", "internalType": "uint8"}, {"name": "longTermIndex", "type": "uint8", "internalType": "uint8"}, {"name": "lastTick", "type": "int16", "internalType": "int16"}, {"name": "lastLendingStateTick", "type": "int16", "internalType": "int16"}, {"name": "midTermIntervalConfig", "type": "uint24", "internalType": "uint24"}, {"name": "longTermIntervalConfig", "type": "uint24", "internalType": "uint24"}, {"name": "lendingCumulativeSum", "type": "int56", "internalType": "int56"}, {"name": "midTermCumulativeSum", "type": "int56[51]", "internalType": "int56[51]"}, {"name": "longTermCumulativeSum", "type": "int56[9]", "internalType": "int56[9]"}, {"name": "midTermTimeInterval", "type": "uint32[51]", "internalType": "uint32[51]"}, {"name": "longTermTimeInterval", "type": "uint32[9]", "internalType": "uint32[9]"}]}], "stateMutability": "view"}, {"type": "function", "name": "exposed_resetTotalAssetsCached", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "exposed_saturationAndGeometricTWAPState", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract ISaturationAndGeometricTWAPState"}], "stateMutability": "view"}, {"type": "function", "name": "exposed_setReferenceReserves", "inputs": [{"name": "_referenceReserveX", "type": "uint256", "internalType": "uint256"}, {"name": "_referenceReserveY", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "exposed_sharesToAssetsScaler", "inputs": [{"name": "tokenType", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "exposed_totalSat", "inputs": [], "outputs": [{"name": "totalSatNetX", "type": "uint256", "internalType": "uint256"}, {"name": "totalSatNetY", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "externalLiquidity", "inputs": [], "outputs": [{"name": "", "type": "uint112", "internalType": "uint112"}], "stateMutability": "view"}, {"type": "function", "name": "failed", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "getReserves", "inputs": [], "outputs": [{"name": "_reserveXAssets", "type": "uint112", "internalType": "uint112"}, {"name": "_reserveYAssets", "type": "uint112", "internalType": "uint112"}, {"name": "_lastTimestamp", "type": "uint32", "internalType": "uint32"}], "stateMutability": "view"}, {"type": "function", "name": "getTickRange", "inputs": [], "outputs": [{"name": "minTick", "type": "int16", "internalType": "int16"}, {"name": "maxTick", "type": "int16", "internalType": "int16"}], "stateMutability": "view"}, {"type": "function", "name": "liquidate", "inputs": [{"name": "borrower", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "depositLToBeTransferredInLAssets", "type": "uint256", "internalType": "uint256"}, {"name": "depositXToBeTransferredInXAssets", "type": "uint256", "internalType": "uint256"}, {"name": "depositYToBeTransferredInYAssets", "type": "uint256", "internalType": "uint256"}, {"name": "repayLXInXAssets", "type": "uint256", "internalType": "uint256"}, {"name": "repayLYInYAssets", "type": "uint256", "internalType": "uint256"}, {"name": "repayXInXAssets", "type": "uint256", "internalType": "uint256"}, {"name": "repayYInYAssets", "type": "uint256", "internalType": "uint256"}, {"name": "liquidationType", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "mint", "inputs": [{"name": "to", "type": "address", "internalType": "address"}], "outputs": [{"name": "liquidityShares", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "referenceReserves", "inputs": [], "outputs": [{"name": "", "type": "uint112", "internalType": "uint112"}, {"name": "", "type": "uint112", "internalType": "uint112"}], "stateMutability": "view"}, {"type": "function", "name": "repay", "inputs": [{"name": "onBehalfOf", "type": "address", "internalType": "address"}], "outputs": [{"name": "repayXInXAssets", "type": "uint256", "internalType": "uint256"}, {"name": "repayYInYAssets", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "repayLiquidity", "inputs": [{"name": "onBehalfOf", "type": "address", "internalType": "address"}], "outputs": [{"name": "repaidLXInXAssets", "type": "uint256", "internalType": "uint256"}, {"name": "repaidLYInYAssets", "type": "uint256", "internalType": "uint256"}, {"name": "repayLiquidityAssets", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "skim", "inputs": [{"name": "to", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "swap", "inputs": [{"name": "amountXOut", "type": "uint256", "internalType": "uint256"}, {"name": "amountYOut", "type": "uint256", "internalType": "uint256"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "sync", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "targetArtifactSelectors", "inputs": [], "outputs": [{"name": "targetedArtifactSelectors_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzArtifactSelector[]", "components": [{"name": "artifact", "type": "string", "internalType": "string"}, {"name": "selectors", "type": "bytes4[]", "internalType": "bytes4[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "targetArtifacts", "inputs": [], "outputs": [{"name": "targetedArtifacts_", "type": "string[]", "internalType": "string[]"}], "stateMutability": "view"}, {"type": "function", "name": "targetContracts", "inputs": [], "outputs": [{"name": "targetedContracts_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "targetInterfaces", "inputs": [], "outputs": [{"name": "targetedInterfaces_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzInterface[]", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "artifacts", "type": "string[]", "internalType": "string[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "targetSelectors", "inputs": [], "outputs": [{"name": "targetedSelectors_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzSelector[]", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "selectors", "type": "bytes4[]", "internalType": "bytes4[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "targetSenders", "inputs": [], "outputs": [{"name": "targetedSenders_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "tokens", "inputs": [{"name": "tokenType", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "address", "internalType": "contract IAmmalgamERC20"}], "stateMutability": "view"}, {"type": "function", "name": "totalAssets", "inputs": [], "outputs": [{"name": "", "type": "uint128[6]", "internalType": "uint128[6]"}], "stateMutability": "view"}, {"type": "function", "name": "underlyingTokens", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IERC20"}, {"name": "", "type": "address", "internalType": "contract IERC20"}], "stateMutability": "view"}, {"type": "function", "name": "updateExternalLiquidity", "inputs": [{"name": "_externalLiquidity", "type": "uint112", "internalType": "uint112"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "validateOnUpdate", "inputs": [{"name": "validate", "type": "address", "internalType": "address"}, {"name": "update", "type": "address", "internalType": "address"}, {"name": "isBorrow", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "withdraw", "inputs": [{"name": "to", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "BurnBadDebt", "inputs": [{"name": "borrower", "type": "address", "indexed": true, "internalType": "address"}, {"name": "tokenType", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "badDebtAssets", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "badDebtShares", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "InterestAccrued", "inputs": [{"name": "depositLAssets", "type": "uint128", "indexed": false, "internalType": "uint128"}, {"name": "depositXAssets", "type": "uint128", "indexed": false, "internalType": "uint128"}, {"name": "depositYAssets", "type": "uint128", "indexed": false, "internalType": "uint128"}, {"name": "borrowLAssets", "type": "uint128", "indexed": false, "internalType": "uint128"}, {"name": "borrowXAssets", "type": "uint128", "indexed": false, "internalType": "uint128"}, {"name": "borrowYAssets", "type": "uint128", "indexed": false, "internalType": "uint128"}], "anonymous": false}, {"type": "event", "name": "Liquidate", "inputs": [{"name": "borrower", "type": "address", "indexed": true, "internalType": "address"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}, {"name": "depositL", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "depositX", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "depositY", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "repayLX", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "repayLY", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "repayX", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "repayY", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "liquidationType", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "<PERSON><PERSON><PERSON>", "inputs": [{"name": "sender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "amountXIn", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "amountYIn", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "amountXOut", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "amountYOut", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "Sync", "inputs": [{"name": "reserveXAssets", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "reserveYAssets", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "UpdateExternalLiquidity", "inputs": [{"name": "externalLiquidity", "type": "uint112", "indexed": false, "internalType": "uint112"}], "anonymous": false}, {"type": "event", "name": "log", "inputs": [{"name": "", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "log_address", "inputs": [{"name": "", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "log_array", "inputs": [{"name": "val", "type": "uint256[]", "indexed": false, "internalType": "uint256[]"}], "anonymous": false}, {"type": "event", "name": "log_array", "inputs": [{"name": "val", "type": "int256[]", "indexed": false, "internalType": "int256[]"}], "anonymous": false}, {"type": "event", "name": "log_array", "inputs": [{"name": "val", "type": "address[]", "indexed": false, "internalType": "address[]"}], "anonymous": false}, {"type": "event", "name": "log_bytes", "inputs": [{"name": "", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "event", "name": "log_bytes32", "inputs": [{"name": "", "type": "bytes32", "indexed": false, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "log_int", "inputs": [{"name": "", "type": "int256", "indexed": false, "internalType": "int256"}], "anonymous": false}, {"type": "event", "name": "log_named_address", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "log_named_array", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "uint256[]", "indexed": false, "internalType": "uint256[]"}], "anonymous": false}, {"type": "event", "name": "log_named_array", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "int256[]", "indexed": false, "internalType": "int256[]"}], "anonymous": false}, {"type": "event", "name": "log_named_array", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "address[]", "indexed": false, "internalType": "address[]"}], "anonymous": false}, {"type": "event", "name": "log_named_bytes", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "event", "name": "log_named_bytes32", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "bytes32", "indexed": false, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "log_named_decimal_int", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "int256", "indexed": false, "internalType": "int256"}, {"name": "decimals", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "log_named_decimal_uint", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "decimals", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "log_named_int", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "int256", "indexed": false, "internalType": "int256"}], "anonymous": false}, {"type": "event", "name": "log_named_string", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "log_named_uint", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "log_string", "inputs": [{"name": "", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "log_uint", "inputs": [{"name": "", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "logs", "inputs": [{"name": "", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "error", "name": "AmmalgamCannotBorrowAgainstSameCollateral", "inputs": []}, {"type": "error", "name": "AmmalgamDepositIsNotStrictlyBigger", "inputs": []}, {"type": "error", "name": "AmmalgamLTV", "inputs": []}, {"type": "error", "name": "AmmalgamMaxBorrowReached", "inputs": []}, {"type": "error", "name": "AmmalgamMaxSlippage", "inputs": []}, {"type": "error", "name": "AmmalgamTooMuchLeverage", "inputs": []}, {"type": "error", "name": "Forbidden", "inputs": []}, {"type": "error", "name": "InsufficientInputAmount", "inputs": []}, {"type": "error", "name": "InsufficientLiquidity", "inputs": []}, {"type": "error", "name": "InsufficientLiquidity", "inputs": []}, {"type": "error", "name": "InsufficientLiquidityBurned", "inputs": []}, {"type": "error", "name": "InsufficientLiquidityMinted", "inputs": []}, {"type": "error", "name": "InsufficientOutputAmount", "inputs": []}, {"type": "error", "name": "InsufficientRepayLiquidity", "inputs": []}, {"type": "error", "name": "InvalidToAddress", "inputs": []}, {"type": "error", "name": "K", "inputs": []}, {"type": "error", "name": "Locked", "inputs": []}, {"type": "error", "name": "NotEnoughRepaidForLiquidation", "inputs": []}, {"type": "error", "name": "Overflow", "inputs": []}, {"type": "error", "name": "PriceOutOfBounds", "inputs": []}, {"type": "error", "name": "SafeCastOverflowedUintDowncast", "inputs": [{"name": "bits", "type": "uint8", "internalType": "uint8"}, {"name": "value", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "SafeERC20FailedOperation", "inputs": [{"name": "token", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "TickOutOfBounds", "inputs": []}], "bytecode": {"object": "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__$0148cd7411c566e8e3abb1476dee2c2502$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$18b16c1511582993d32b10789a400df1a7$__90631eaa678390614838907f0000000000000000000000000000000000000000000000000000000000000000908a908a908990899089906004016178fd565b5f6040518083038186803b15801561484e575f5ffd5b505af4158015614860573d5f5f3e3d5ffd5b50505050610ebb8585858585615a05565b6040516325c084e360e11b81525f9073__$18b16c1511582993d32b10789a400df1a7$__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__$0148cd7411c566e8e3abb1476dee2c2502$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", "sourceMap": "5329:5380:180:-:0;;;2596:45:38;;;-1:-1:-1;;;;;;2596:45:38;;;;1651:28:0;;;;3126:44:97;;;-1:-1:-1;;3126:44:97;;;;;;;;1016:26:107;;;;;;;;;;;5329:5380:180;;;;;;;;;;2836:34:38;;:::i;:::-;2907:10;2880:38;;;;2958:37;;;-1:-1:-1;;;2958:37:38;;;;:35;;:37;;;;;;;;;;;;;;;;2907:10;2958:37;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;;;;;2928:67:38;;;;;;;;;;;;;;3023:20;;3006:37;;;;-1:-1:-1;3070:20:38;;;;3053:37;;;;3117:20;;;;;3100:37;;;;3163:19;;;;3147:35;;;;3208:19;;;;3192:35;;;;3253:19;;;;3237:35;;;;3350:7;;:41;;-1:-1:-1;;;3350:41:38;;;;2928:67;;-1:-1:-1;3350:39:38;;;;;;373:1:19;3350:41:38;;;;;;;;;;:39;:41;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;;;;;3283:109:38;;;-1:-1:-1;5329:5380:180;;;;;;;;;;;;;;;;;;;;-1:-1:-1;5329:5380:180;;;-1:-1:-1;;5329:5380:180:o;14:139:193:-;-1:-1:-1;;;;;97:31:193;;87:42;;77:70;;143:1;140;133:12;77:70;14:139;:::o;158:127::-;219:10;214:3;210:20;207:1;200:31;250:4;247:1;240:15;274:4;271:1;264:15;290:162;385:13;;407:39;385:13;407:39;:::i;:::-;290:162;;;:::o;457:1132::-;623:6;631;639;692:3;680:9;671:7;667:23;663:33;660:53;;;709:1;706;699:12;660:53;741:9;735:16;760:39;793:5;760:39;:::i;:::-;868:2;853:18;;847:25;818:5;;-1:-1:-1;881:41:193;847:25;881:41;:::i;:::-;941:7;-1:-1:-1;986:2:193;971:18;;967:32;-1:-1:-1;957:60:193;;1013:1;1010;1003:12;957:60;1046:2;1040:9;1088:3;1076:16;;-1:-1:-1;;;;;1107:34:193;;1143:22;;;1104:62;1101:88;;;1169:18;;:::i;:::-;1205:2;1198:22;1240:6;1284:3;1269:19;;1300;;;1297:39;;;1332:1;1329;1322:12;1297:39;1371:2;1360:9;1356:18;1383:175;1399:6;1394:3;1391:15;1383:175;;;1465:50;1511:3;1465:50;:::i;:::-;1453:63;;1545:2;1536:12;;;;1416;1383:175;;;1387:3;;;1577:6;1567:16;;;457:1132;;;;;:::o;1726:300::-;1837:6;1890:2;1878:9;1869:7;1865:23;1861:32;1858:52;;;1906:1;1903;1896:12;1858:52;1938:9;1932:16;1957:39;1990:5;1957:39;:::i;:::-;2015:5;1726:300;-1:-1:-1;;;1726:300:193:o;:::-;5329:5380:180;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "linkReferences": {"contracts/libraries/Interest.sol": {"Interest": [{"start": 3691, "length": 20}, {"start": 21932, "length": 20}]}, "contracts/libraries/Liquidation.sol": {"Liquidation": [{"start": 19361, "length": 20}, {"start": 19517, "length": 20}]}}}, "deployedBytecode": {"object": "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__$0148cd7411c566e8e3abb1476dee2c2502$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$18b16c1511582993d32b10789a400df1a7$__90631eaa678390614838907f0000000000000000000000000000000000000000000000000000000000000000908a908a908990899089906004016178fd565b5f6040518083038186803b15801561484e575f5ffd5b505af4158015614860573d5f5f3e3d5ffd5b50505050610ebb8585858585615a05565b6040516325c084e360e11b81525f9073__$18b16c1511582993d32b10789a400df1a7$__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__$0148cd7411c566e8e3abb1476dee2c2502$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", "sourceMap": "5329:5380:180:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8309:2094:38;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;7682:2760:0;;;;;;:::i;:::-;;:::i;:::-;;45835:752;;;;;;:::i;:::-;;:::i;6717:282:38:-;;;:::i;:::-;;;;-1:-1:-1;;;;;2679:43:193;;;2661:62;;2759:43;;;;2754:2;2739:18;;2732:71;2851:10;2839:23;2819:18;;;2812:51;2649:2;2634:18;6717:282:38;2461:408:193;8821:111:180;;;:::i;:::-;;;;-1:-1:-1;;;;;3066:43:193;;;3048:62;;3146:43;;;;3141:2;3126:18;;3119:71;3021:18;8821:111:180;2874:322:193;6629:166:180;;;:::i;:::-;;;;3492:1:193;3481:21;;;3463:40;;3539:21;;;;3534:2;3519:18;;3512:49;3436:18;6629:166:180;3297:270:193;5865:297:180;;;;;;:::i;:::-;;:::i;6801:110::-;6887:17;;-1:-1:-1;;;;;6887:17:180;6801:110;;;-1:-1:-1;;;;;4107:43:193;;;4089:62;;4077:2;4062:18;6801:110:180;3943:214:193;2907:134:100;;;:::i;:::-;;;;;;;:::i;3823:151::-;;;:::i;:::-;;;;;;;:::i;7438:130:38:-;7524:17;;-1:-1:-1;;;;;7524:17:38;;;;-1:-1:-1;;;7543:17:38;;;7438:130;;7005:427;;;:::i;7147:284:180:-;;;;;;:::i;:::-;;:::i;:::-;;;7099:25:193;;;7087:2;7072:18;7147:284:180;6953:177:193;3684:133:100;;;:::i;3385:141::-;;;:::i;5867:314:38:-;;;;;;:::i;:::-;;:::i;:::-;;;-1:-1:-1;;;;;7322:32:193;;;7304:51;;7292:2;7277:18;5867:314:38;7135:226:193;15539:631:0;;;;;;:::i;:::-;;:::i;5392:176:180:-;;;:::i;:::-;;;;;;;:::i;7625:710::-;;;;;;:::i;:::-;;:::i;:::-;;;11706:1:193;11695:21;;;;11677:40;;11665:2;11650:18;7625:710:180;11535:188:193;3193:186:100;;;:::i;:::-;;;;;;;:::i;4516:213:38:-;;;;;;:::i;:::-;;:::i;2321:2731:0:-;;;;;;:::i;:::-;;:::i;6917:224:180:-;;;;;;:::i;:::-;7030:17;:47;;-1:-1:-1;;;;;7087:47:180;;;-1:-1:-1;;;7087:47:180;-1:-1:-1;;;;;;7087:47:180;;;7030;;;;7087;;;;;;;;;;6917:224;10295:243;;;;;;:::i;:::-;;:::i;9258:251::-;;;:::i;3047:140:100:-;;;:::i;:::-;;;;;;;:::i;2596:45:38:-;;;;;-1:-1:-1;;;;;2596:45:38;;;19795:2670:0;;;;;;:::i;:::-;;:::i;:::-;;;;15730:25:193;;;15786:2;15771:18;;15764:34;;;;15703:18;19795:2670:0;15556:248:193;5161:2412:0;;;;;;:::i;:::-;;:::i;17371:1200::-;;;;;;:::i;:::-;;:::i;9515:774:180:-;;;;;;:::i;:::-;;:::i;3532:146:100:-;;;:::i;:::-;;;;;;;:::i;22471:164:0:-;;;;;;:::i;:::-;;:::i;10544:163:180:-;10669:31;10544:163;;2754:147:100;;;:::i;2459:141::-;;;:::i;1243:204:96:-;;;:::i;:::-;;;18051:14:193;;18044:22;18026:41;;18014:2;17999:18;1243:204:96;17886:187:193;41555:225:0;;;;;;:::i;:::-;;:::i;3631:114:38:-;;;:::i;:::-;;;;-1:-1:-1;;;;;18302:32:193;;;18284:51;;18371:32;;;;18366:2;18351:18;;18344:60;18257:18;3631:114:38;18078:332:193;6310:313:180;;;;;;:::i;:::-;;:::i;8341:156::-;;;:::i;8660:155::-;;;;;;:::i;:::-;;:::i;24716:216:0:-;;;;;;:::i;:::-;;:::i;:::-;;;;18989:25:193;;;19045:2;19030:18;;19023:34;;;;19073:18;;;19066:34;18977:2;18962:18;24716:216:0;18787:319:193;28422:2410:0;;;;;;:::i;:::-;;:::i;7437:182:180:-;;;:::i;2606:142:100:-;;;:::i;8957:295:180:-;;;:::i;8503:151::-;;;;;;:::i;:::-;;:::i;:::-;;;;;;;:::i;6168:136::-;;;:::i;13566:1346:0:-;;;;;;:::i;:::-;;:::i;1016:26:107:-;;;;;;;;;41889:54:0;;;:::i;8309:2094:38:-;8353:17;;:::i;:::-;8386:19;;:24;8382:349;;-1:-1:-1;8426:294:38;;;;;;;;-1:-1:-1;;;;;;8459:19:38;8426:294;;;;8505:19;;8426:294;;;;;;8551:19;;8426:294;;;;;;;;;8597:18;;8426:294;;;;;;8642:18;;8426:294;;;;;;8687:18;;8426:294;;;;;;;;8309:2094::o;8382:349::-;8957:19;;9033:20;;26839:21:21;-1:-1:-1;;;8957:19:38;;;;;;26839:15:21;:21;;8938:38:38;;;;9033:20;;;;;9014:39;;;;9133:26;;8741:27;9133:26;9129:73;;9175:16;;;;;;;;;;;9182:9;;9175:16;;9182:9;-1:-1:-1;9175:16:38;;;;;;;;;;;-1:-1:-1;;;;;9175:16:38;-1:-1:-1;;;;;9175:16:38;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8309:2094;:::o;9129:73::-;9213:23;9238;9266:13;:11;:13::i;:::-;9212:67;-1:-1:-1;;;;;9212:67:38;;;-1:-1:-1;;;;;9212:67:38;;;9289:13;9305:31;-1:-1:-1;;;;;9305:41:38;;9360:86;9384:61;9399:15;-1:-1:-1;;;9422:15:38;9439:5;9384:14;:61::i;:::-;9360:23;:86::i;:::-;9305:151;;-1:-1:-1;;;;;;9305:151:38;;;;;;;11706:1:193;11695:21;;;;9305:151:38;;;11677:40:193;11650:18;;9305:151:38;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;9692:135;;-1:-1:-1;;;9692:135:38;;21640:1:193;21629:21;;;9692:135:38;;;21611:40:193;9692:135:38;21687:23:193;;;21667:18;;;21660:51;21747:23;;21727:18;;;21720:51;9289:167:38;;-1:-1:-1;9467:22:38;;;;-1:-1:-1;;;;;9692:31:38;:51;;;;21584:18:193;;9692:135:38;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;9650:177;;;;;;;;9848:43;9894:318;;;;;;;;9948:21;9894:318;;;;;;10001:16;9894:318;;;;;;10056:60;10083:15;10100;10056:26;:60::i;:::-;9894:318;;;;;;;;;;;;;;;;;;-1:-1:-1;;9894:318:38;;-1:-1:-1;;9894:318:38;;;;;;;;;;;-1:-1:-1;;;;;9894:318:38;-1:-1:-1;;;;;9894:318:38;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;9894:318:38;;;-1:-1:-1;;;9894:318:38;;;;;10313:52;;-1:-1:-1;;;10313:52:38;;9848:364;;-1:-1:-1;;;10313:8:38;;:33;;:52;;10347:9;;9848:364;;10313:52;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;10274:91:38;;8309:2094;-1:-1:-1;;;;;;;;;;;;8309:2094:38:o;7682:2760:0:-;2143:7;:5;:7::i;:::-;2171:1;2160:8;:12;7797:15;;:34;::::1;;;-1:-1:-1::0;7816:15:0;;7797:34:::1;7793:73;;;7840:26;;-1:-1:-1::0;;;7840:26:0::1;;;;;;;;;;;7793:73;7878:23;7903;7931:13;:11;:13::i;:::-;7877:67;-1:-1:-1::0;;;;;7877:67:0::1;;;-1:-1:-1::0;;;;;7877:67:0::1;;;8068:51;8086:15;8103;8068:17;:51::i;:::-;8182:17;8209;8303:23;8328;8355:15;:13;:15::i;:::-;-1:-1:-1::0;;;;;8302:68:0;;::::1;::::0;-1:-1:-1;8302:68:0::1;::::0;-1:-1:-1;8403:33:0::1;8302:68:::0;8403:15;:33:::1;:::i;:::-;8389:10;:47;;:98;;;-1:-1:-1::0;8454:33:0::1;8472:15:::0;8454;:33:::1;:::i;:::-;8440:10;:47;;8389:98;8385:167;;;8514:23;;-1:-1:-1::0;;;8514:23:0::1;;;;;;;;;;;8385:167;8736:42;8751:2;8755:10;8767;8736:14;:42::i;:::-;8796:15:::0;;8792:128:::1;;8831:74;::::0;-1:-1:-1;;;8831:74:0;;-1:-1:-1;;;;;8831:32:0;::::1;::::0;::::1;::::0;:74:::1;::::0;8864:10:::1;::::0;8876;;8888;;8900:4;;;;8831:74:::1;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8792:128;9005:24;9031;9059:20;9074:1;9077;9059:14;:20::i;:::-;9004:75;;;;9106:64;9124:10;9136:16;9154:15;9106:17;:64::i;:::-;9094:76;;9196:64;9214:10;9226:16;9244:15;9196:17;:64::i;:::-;9184:76:::0;-1:-1:-1;9339:14:0;;:32;::::1;;;-1:-1:-1::0;9357:14:0;;9339:32:::1;9335:70;;;9380:25;;-1:-1:-1::0;;;9380:25:0::1;;;;;;;;;;;9335:70;9421:26;9449;9479:19;7524:17:38::0;;-1:-1:-1;;;;;7524:17:38;;;;-1:-1:-1;;;7543:17:38;;;;;7438:130;9479:19:0::1;-1:-1:-1::0;;;;;9420:78:0::1;;;-1:-1:-1::0;;;;;9420:78:0::1;;;9983:77;10027:15;10044;9983:43;:77::i;:::-;9879;9923:15;9940;9879:43;:77::i;:::-;:181;;;;:::i;:::-;9702:154;9753:9;9764:16;9782:15;9799:18;9819:15;9702:25;:154::i;:::-;9533:146;9580:9;9591:16;9609:15;9626:18;9646:15;9533:25;:146::i;:::-;:323;;;;:::i;:::-;:527;9512:606;;;10100:3;;-1:-1:-1::0;;;10100:3:0::1;;;;;;;;;;;9512:606;-1:-1:-1::0;;10260:66:0::1;::::0;;27773:25:193;;;27829:2;27814:18;;27807:34;;;27857:18;;;27850:34;;;27915:2;27900:18;;27893:34;;;-1:-1:-1;;;;;10260:66:0;::::1;::::0;-1:-1:-1;10265:10:0::1;::::0;-1:-1:-1;10260:66:0::1;::::0;-1:-1:-1;27760:3:193;27745:19;;-1:-1:-1;10260:66:0::1;;;;;;;10337:98;10382:10:::0;10352:27:::1;10370:9:::0;10352:15;:27:::1;:::i;:::-;:40;;;;:::i;:::-;10424:10:::0;10394:27:::1;10412:9:::0;10394:15;:27:::1;:::i;:::-;:40;;;;:::i;:::-;10337:14;:98::i;:::-;-1:-1:-1::0;;2204:1:0;2193:8;:12;-1:-1:-1;;;;;;;7682:2760:0:o;45835:752::-;46109:20;;26839:21:21;-1:-1:-1;;;46109:20:0;;;;;26839:15:21;:21;;;46090:39:0;46149:46;46163:8;46090:39;46149:13;:46::i;:::-;46205:36;46222:8;46232;46205:16;:36::i;:::-;46317:4;-1:-1:-1;;;;;46309:23:0;;;46305:276;;46349:41;46392:14;46410:28;46425:6;46433:4;46410:14;:28::i;:::-;46348:90;;;;46456:9;:21;;;;46469:8;46456:21;46452:119;;;46497:59;;-1:-1:-1;;;46497:59:0;;-1:-1:-1;;;;;46497:31:0;:38;;;;:59;;46536:11;;46549:6;;46497:59;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;46452:119;46334:247;;46305:276;45919:668;45835:752;;;:::o;6717:282:38:-;6890:14;;-1:-1:-1;;;;;6890:14:38;;;;-1:-1:-1;;;6932:14:38;;;;;;-1:-1:-1;;;6973:19:38;;;;;6717:282::o;8821:111:180:-;8875:7;8884;8910:15;:13;:15::i;:::-;8903:22;;;;8821:111;;:::o;6629:166::-;6699:5;;6730:4;:40;6771:16;:14;:16::i;:::-;6730:58;;-1:-1:-1;;;;;;6730:58:180;;;;;;;11706:1:193;11695:21;;;;6730:58:180;;;11677:40:193;11650:18;;6730:58:180;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;5865:297::-;5951:5;5958;6077:31;-1:-1:-1;;;;;6077:44:180;;6130:4;6137:11;6150:4;6077:78;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;6070:85;;;;5865:297;;;:::o;2907:134:100:-;2954:33;3018:16;2999:35;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;2999:35:100;;;;;;;;;;;;;;;;;;;;;;;2907:134;:::o;3823:151::-;3872:42;3948:19;3926:41;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3926:41:100;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3823:151;:::o;7005:427:38:-;7050:13;7065;7091:23;7116;7144:13;:11;:13::i;:::-;7090:67;;;;;7167:19;7189:61;7204:15;-1:-1:-1;;;;;7189:61:38;-1:-1:-1;;;7227:15:38;-1:-1:-1;;;;;7189:61:38;7244:5;7189:14;:61::i;:::-;7167:83;;7260:17;7280:36;7304:11;7280:23;:36::i;:::-;7347:78;;-1:-1:-1;;;7347:78:38;;7260:56;;-1:-1:-1;;;;;;7347:31:38;:44;;;;:78;;7400:4;;7260:56;;7420:4;;7347:78;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;7326:99;;;;-1:-1:-1;7005:427:38;-1:-1:-1;;;;;7005:427:38:o;7147:284:180:-;7241:7;7260:20;7283:22;7295:9;7283:11;:22::i;:::-;7260:45;-1:-1:-1;7322:17:180;;:102;;7348:76;7360:13;:11;:13::i;:::-;7374:9;7360:24;;;;;;;:::i;:::-;;;;;-1:-1:-1;;;;;7348:76:180;3395:8:125;7391:12:180;7405:18;7348:11;:76::i;:::-;7322:102;;;3395:8:125;7322:102:180;7315:109;7147:284;-1:-1:-1;;;7147:284:180:o;3684:133:100:-;3730:33;3794:16;3775:35;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3775:35:100;;;;;;;;;;;;;;;;;;;;;;3684:133;:::o;3385:141::-;3433:35;3501:18;3480:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3480:39:100;;;;;;;;;;;;;;;;;;;;;;3385:141;:::o;5867:314:38:-;6070:104;;;;;;;;-1:-1:-1;;;;;6071:14:38;6070:104;;;;6087:14;6070:104;;;;;;6103:14;6070:104;;;;;;;;;6119:13;6070:104;;;;;;6134:13;6070:104;;;;;;6149:13;6070:104;;;;;;;5946:14;;6164:9;6070:104;;;;;;;:::i;:::-;;;;;;5867:314;-1:-1:-1;;5867:314:38:o;15539:631:0:-;2143:7;:5;:7::i;:::-;2171:1;2160:8;:12;;;2171:1;15873:50:::1;2171:1:::0;15873:36:::1;:50::i;:::-;15818:105;;;;;;15934:15;15952:52;15973:2;279:1:19;15988:15:0;15952:20;:52::i;:::-;15934:70;;16014:15;16032:52;16053:2;311:1:19;16068:15:0;16032:20;:52::i;:::-;16014:70;;16095:36;16110:2;16114:7;16123;16095:14;:36::i;:::-;16142:21;:19;:21::i;:::-;-1:-1:-1::0;;2204:1:0;2193:8;:12;-1:-1:-1;;;15539:631:0:o;5392:176:180:-;5447:33;;:::i;:::-;5499:62;;-1:-1:-1;;;5499:62:180;;5555:4;5499:62;;;7304:51:193;5499:31:180;-1:-1:-1;;;;;5499:47:180;;;;7277:18:193;;5499:62:180;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;5492:69;;5392:176;:::o;7625:710::-;7757:22;;7811:39;7849:1;637:2:21;7811:39:180;:::i;:::-;7876:62;;-1:-1:-1;;;7876:62:180;;7932:4;7876:62;;;7304:51:193;7791:59:180;;-1:-1:-1;7860:13:180;;-1:-1:-1;;;;;7876:31:180;:47;;;;7277:18:193;;7876:62:180;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:75;;;:80;;;:198;;7995:62;;-1:-1:-1;;;7995:62:180;;8051:4;7995:62;;;7304:51:193;8073:1:180;;7995:31;-1:-1:-1;;;;;7995:47:180;;;;7277:18:193;;7995:62:180;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:75;;;:79;;;;:::i;:::-;7876:198;;;;;7971:9;7876:198;8132:62;;-1:-1:-1;;;8132:62:180;;8188:4;8132:62;;;7304:51:193;7860:214:180;;-1:-1:-1;8085:26:180;;-1:-1:-1;;;;;8132:31:180;:47;;;;7277:18:193;;8132:62:180;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:83;;;8216:5;8132:90;;;;;;;:::i;:::-;;;;;;-1:-1:-1;8318:8:180;8260:47;8283:24;8132:90;8260:47;:::i;:::-;8259:68;;;;;;:::i;:::-;8234:94;7625:710;-1:-1:-1;;;;;;7625:710:180:o;3193:186:100:-;3249:56;3346:26;3317:55;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3317:55:100;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4516:213:38;3442:18;:16;:18::i;:::-;4631:43:::1;::::0;-1:-1:-1;;;;;4107:43:193;;4089:62;;4631:43:38::1;::::0;4077:2:193;4062:18;4631:43:38::1;;;;;;;4684:17;:38:::0;;-1:-1:-1;;;;;;4684:38:38::1;-1:-1:-1::0;;;;;4684:38:38;;;::::1;::::0;;;::::1;::::0;;4516:213::o;2321:2731:0:-;2384:23;2143:7;:5;:7::i;:::-;2171:1;2160:8;:12;;;2784:20:::1;::::0;2171:1;;;;;;;;;;-1:-1:-1;;;;;2784:20:0::1;:25:::0;;2780:1704:::1;;2862:20;2877:1;2880;2862:14;:20::i;:::-;2825:57:::0;;-1:-1:-1;2825:57:0;-1:-1:-1;2896:29:0::1;3003:44;3013:33;2825:57:::0;;3013:33:::1;:::i;:::-;3003:9;:44::i;:::-;-1:-1:-1::0;;;;;2967:81:0::1;-1:-1:-1::0;;;2967:81:0;::::1;-1:-1:-1::0;;;;;;2944:104:0;;::::1;2967:25;2944:104:::0;2967:81;-1:-1:-1;3098:41:0::1;3108:4:30;2967:81:0::0;3098:41:::1;:::i;:::-;3080:59;;;3062:77;;3194:47;3208:15;3225;3194:13;:47::i;:::-;3154:17;3153:88:::0;;-1:-1:-1;;;;;;3153:88:0;-1:-1:-1;;;;;;;;3153:88:0;;::::1;;-1:-1:-1::0;;;;;;3153:88:0;;;;;::::1;::::0;;;::::1;::::0;;3256:85:::1;-1:-1:-1::0;3274:10:0::1;3294:7;3108:4:30;::::0;3256:6:0::1;:85::i;:::-;3410:15;3428:86;3452:61;3467:15;-1:-1:-1::0;;;3490:15:0::1;3507:5;3452:14;:61::i;3428:86::-;3528:47;::::0;-1:-1:-1;;;3528:47:0;;11706:1:193;11695:21;;;3528:47:0::1;::::0;::::1;11677:40:193::0;3410:104:0;;-1:-1:-1;3528:31:0::1;-1:-1:-1::0;;;;;3528:36:0::1;::::0;::::1;::::0;11650:18:193;;3528:47:0::1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3589:23;3615:35;26839:21:21::0;:15;:21;;26678:200;3615:35:0::1;3664:19;:38:::0;;::::1;::::0;;::::1;-1:-1:-1::0;;;3664:38:0::1;-1:-1:-1::0;;;;;3664:38:0;;::::1;::::0;::::1;::::0;;;3716:20:::1;:39:::0;;;::::1;::::0;::::1;::::0;;3769:20:::1;:39:::0;;;;::::1;;::::0;;-1:-1:-1;2780:1704:0::1;::::0;-1:-1:-1;;2780:1704:0::1;;3906:40;3943:2;3906:36;:40::i;:::-;3839:107:::0;;-1:-1:-1;3839:107:0;;-1:-1:-1;3839:107:0;-1:-1:-1;3839:107:0;-1:-1:-1;3961:28:0::1;4052:45;:43;:45::i;:::-;4003:94:::0;-1:-1:-1;4003:94:0;;-1:-1:-1;4148:325:0::1;::::0;-1:-1:-1;4202:13:0;4233;4264:15;4297;4003:94;;4407:22:::1;247:1:19;4407:11:0;:22::i;:::-;4447:12:::0;4148:36:::1;:325::i;:::-;4111:362:::0;-1:-1:-1;4111:362:0;-1:-1:-1;;2780:1704:0::1;4554:15;4573:1;4554:20:::0;4550:62:::1;;4583:29;;-1:-1:-1::0;;;4583:29:0::1;;;;;;;;;;;4550:62;4623:67;247:1:19;4641:10:0;4653:2;4657:15;4674;4623:6;:67::i;:::-;4782:148;4822:15:::0;4839;4856:31:::1;4874:13:::0;4822:15;4856:31:::1;:::i;:::-;4889;4907:13:::0;4889:15;:31:::1;:::i;:::-;4782:26;:148::i;:::-;5001:44;5035:2;5039:5;5001:33;:44::i;:::-;-1:-1:-1::0;;2204:1:0;2193:8;:12;-1:-1:-1;2321:2731:0;;;-1:-1:-1;;;;2321:2731:0:o;10295:243:180:-;443:1:19;10403:9:180;:28;10395:37;;;;;;10466:6;10442:9;10452;10442:20;;;;;;;:::i;:::-;;;;;;;;;;;;:30;;;;;;;;;;-1:-1:-1;;;;;10442:30:180;;;;;:::i;:::-;;;;;;;;-1:-1:-1;;;;;10442:30:180;;;;;-1:-1:-1;;;;;10442:30:180;;;;;;10525:6;10482:9;443:1:19;10492:9:180;:28;;;;:::i;:::-;10482:39;;;;;;;:::i;:::-;;;;;;;;;;;;:49;;;;;;;;;;-1:-1:-1;;;;;10482:49:180;;;;;:::i;:::-;;;;;;;;-1:-1:-1;;;;;10482:49:180;;;;;-1:-1:-1;;;;;10482:49:180;;;;;;10295:243;;:::o;9258:251::-;9339:1;;;9317:23;-1:-1:-1;9372:1:180;;9350:19;:23;-1:-1:-1;9405:1:180;;9383:19;:23;-1:-1:-1;9437:1:180;;9416:18;:22;-1:-1:-1;9469:1:180;;9448:18;:22;-1:-1:-1;9501:1:180;;9480:18;:22;;9258:251::o;3047:140:100:-;3095:34;3162:18;3141:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;19795:2670:0;19935:7;19944;2143;:5;:7::i;:::-;2171:1;2160:8;:12;;;2171:1;20018:40:::1;20055:2:::0;20018:36:::1;:40::i;:::-;19963:95;;;;;;20069:27;20106:24;20140;20188:27;20218:24;342:1:19;20218:14:0;:24::i;:::-;-1:-1:-1::0;;;;;20188:54:0::1;;;20257:634;20302:575;;;;;;;;;;;;;;;;20397:25;247:1:19;20397:14:0;:25::i;:::-;-1:-1:-1::0;;;;;20302:575:0::1;;;;;20448:25;279:1:19;20448:14:0;:25::i;:::-;-1:-1:-1::0;;;;;20302:575:0::1;;;;;20499:25;311:1:19;20499:14:0;:25::i;:::-;-1:-1:-1::0;;;;;20302:575:0::1;;;;;20550:19;20302:575;;;;20595:24;373:1:19;20595:14:0;:24::i;:::-;-1:-1:-1::0;;;;;20302:575:0::1;;;;;20645:24;404:1:19;20645:14:0;:24::i;:::-;-1:-1:-1::0;;;;;20302:575:0::1;;;::::0;::::1;;;;20733:19;20302:575;;;;20790:15;20302:575;;;;20843:15;20302:575;;::::0;20257:27:::1;:634::i;:::-;20980:31;21014:60;21041:15;21058;21014:26;:60::i;:::-;20980:94;;21111:84;21126:19;21147:15;21164:23;21189:5;21111:14;:84::i;:::-;21092:103;;21232:84;21247:19;21268:15;21285:23;21310:5;21232:14;:84::i;:::-;21213:103;;20906:425;21383:94;21400:19;21421;21442:21;342:1:19;21442:11:0;:21::i;:::-;475:4:19;21383:16:0;:94::i;:::-;21345:132;;21552:74;342:1:19;21569:10:0;21581:2;21585:19;21606;21552:6;:74::i;:::-;20174:1463;21647:54;21662:2;21666:16;21684;21647:14;:54::i;:::-;21948:154;21988:15:::0;22005;22022:34:::1;22040:16:::0;21988:15;22022:34:::1;:::i;:::-;22058;22076:16:::0;22058:15;:34:::1;:::i;21948:154::-;22117:15:::0;;22113:246:::1;;22159:1;22148:8;:12;;;;22184:2;-1:-1:-1::0;;;;;22174:43:0::1;;22235:10;22247:16;22265;22283:19;22304:4;;22174:148;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1::0;;22347:1:0::1;22336:8;:12:::0;-1:-1:-1;;22113:246:0::1;22369:35;22386:10;22398:5;22369:16;:35::i;:::-;2204:1:::0;2193:8;:12;22423:16;;;;-1:-1:-1;19795:2670:0;-1:-1:-1;;;;;;;;19795:2670:0:o;5161:2412::-;5224:21;5247;2143:7;:5;:7::i;:::-;2171:1;2160:8;:12;;;2171:1;5482:50:::1;2171:1:::0;5482:36:::1;:50::i;:::-;5427:105;;;;;;5543:23;5569:35;5587:4;247:1:19;5569:9:0;:35::i;:::-;5543:61;;5615:30;5648:29;5681:45;:43;:45::i;:::-;5614:112;;;;;5736:30;5769:22;247:1:19;5769:11:0;:22::i;:::-;5736:55:::0;-1:-1:-1;5802:29:0::1;5846:95;5863:15:::0;5880:22;5736:55;5802:29;5846:16:::1;:95::i;:::-;5802:139;;5968:152;6007:15;6024;6041:21;6064:22;6088;5968:25;:152::i;:::-;5952:168;;6146:152;6185:15;6202;6219:21;6242:22;6266;6146:25;:152::i;:::-;6130:168:::0;-1:-1:-1;6369:18:0;;;:40:::1;;-1:-1:-1::0;6391:18:0;;6369:40:::1;6365:107;;;6432:29;;-1:-1:-1::0;;;6432:29:0::1;;;;;;;;;;;6365:107;6522:19;6544:31;6562:13:::0;6544:15;:31:::1;:::i;:::-;6522:53:::0;-1:-1:-1;6585:19:0::1;6607:31;6625:13:::0;6607:15;:31:::1;:::i;:::-;6690:527;::::0;;;;;;;;6585:53;;-1:-1:-1;6649:578:0::1;::::0;6690:527;::::1;::::0;::::1;::::0;6777:46:::1;6802:21:::0;6777:22;:46:::1;:::i;:::-;6690:527;;;;6845:25;279:1:19;6845:14:0;:25::i;:::-;-1:-1:-1::0;;;;;6690:527:0::1;;;;;6892:25;311:1:19;6892:14:0;:25::i;:::-;-1:-1:-1::0;;;;;6690:527:0::1;;;;;6939:24;342:1:19;6939:14:0;:24::i;:::-;-1:-1:-1::0;;;;;6690:527:0::1;;;;;6985:24;373:1:19;6985:14:0;:24::i;:::-;-1:-1:-1::0;;;;;6690:527:0::1;;;;;7031:24;404:1:19;7031:14:0;:24::i;:::-;-1:-1:-1::0;;;;;6690:527:0::1;::::0;;;;7111:1:::1;6690:527;::::0;::::1;::::0;;;;;;;;;;;;6649:27:::1;:578::i;:::-;7238:73;247:1:19;7256:10:0;7268:2;7272:21;7295:15;7238:6;:73::i;:::-;7322:48;7337:2;7341:13;7356;7322:14;:48::i;:::-;7480:86;7507:15;7524;7541:11;7554;7480:26;:86::i;:::-;5270:2303;;;;;;;;;2204:1:::0;2193:8;:12;;;;5161:2412;;;:::o;17371:1200::-;2143:7;:5;:7::i;:::-;2171:1;2160:8;:12;;;2171:1;17545:40:::1;17582:2:::0;17545:36:::1;:40::i;:::-;17490:95;;;;;;17596:57;17656:280;;;;;;;;17713:1;17656:280;;;;17745:1;17656:280;;;;17776:1;17656:280;;;;17800:1;17656:280;;;;17837:25;247:1:19;17837:14:0;:25::i;:::-;-1:-1:-1::0;;;;;17656:280:0::1;;;;;17901:24;342:1:19;17901:14:0;:24::i;:::-;-1:-1:-1::0;;;;;17656:280:0::1;::::0;;17596:340;-1:-1:-1;17947:21:0::1;17971:86;17596:340:::0;18001:2;18005:13;18020:15;373:1:19::1;279;17971:12:0;:86::i;:::-;17947:110;;18067:21;18091:86;18104:15;18121:2;18125:13;18140:15;404:1:19;311;18091:12:0;:86::i;:::-;18067:110;;18188:48;18203:2;18207:13;18222;18188:14;:48::i;:::-;18247:21;:19;:21::i;:::-;18283:15:::0;;18279:240:::1;;18325:1;18314:8;:12:::0;18340:142:::1;::::0;-1:-1:-1;;;18340:142:0;;-1:-1:-1;;;;;18340:34:0;::::1;::::0;::::1;::::0;:142:::1;::::0;18392:10:::1;::::0;18404:13;;18419;;18434;;18449;;18464:4;;;;18340:142:::1;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1::0;;18507:1:0::1;18496:8;:12:::0;-1:-1:-1;;18279:240:0::1;18529:35;18546:10;18558:5;18529:16;:35::i;:::-;-1:-1:-1::0;;2204:1:0;2193:8;:12;-1:-1:-1;;;;;;;;17371:1200:0:o;9515:774:180:-;9607:24;9647:9;9643:640;;-1:-1:-1;;9704:19:180;;;9515:774::o;9643:640::-;279:1:19;9744:9:180;:22;9740:543;;-1:-1:-1;;9801:19:180;;;9515:774::o;9740:543::-;311:1:19;9841:9:180;:22;9837:446;;-1:-1:-1;;9898:19:180;;;9515:774::o;9837:446::-;342:1:19;9938:9:180;:21;9934:349;;-1:-1:-1;;9994:18:180;;;9515:774::o;9934:349::-;373:1:19;10033:9:180;:21;10029:254;;-1:-1:-1;;10089:18:180;;;9515:774::o;10029:254::-;404:1:19;10128:9:180;:21;10124:159;;-1:-1:-1;;10184:18:180;;;9515:774::o;10124:159::-;10233:39;;-1:-1:-1;;;10233:39:180;;37503:2:193;10233:39:180;;;37485:21:193;37542:2;37522:18;;;37515:30;-1:-1:-1;;;37561:18:193;;;37554:51;37622:18;;10233:39:180;;;;;;;;;9515:774;;;:::o;3532:146:100:-;3580:40;3653:18;3632:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3632:39:100;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3632:39:100;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;22471:164:0;22543:23;22568;2143:7;:5;:7::i;:::-;2171:1;2160:8;:12;22610:18:::1;22617:10:::0;22610:6:::1;:18::i;:::-;2204:1:::0;2193:8;:12;22603:25;;;;-1:-1:-1;22471:164:0;-1:-1:-1;;22471:164:0:o;2754:147:100:-;2803:40;2876:18;2855:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;2855:39:100;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;2855:39:100;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2459:141;2508:34;2575:18;2554:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1243:204:96;1302:7;;1282:4;;1302:7;;1298:143;;;-1:-1:-1;1332:7:96;;;;;1243:204::o;1298:143::-;1377:39;;-1:-1:-1;;;1377:39:96;;:7;:39;;;37825:51:193;;;-1:-1:-1;;;37892:18:193;;;37885:34;1428:1:96;;1377:7;;37798:18:193;;1377:39:96;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:53;;1370:60;;1243:204;:::o;41555:225:0:-;2143:7;:5;:7::i;:::-;2171:1;2160:8;:12;;;2171:1;41673:40:::1;41710:2:::0;41673:36:::1;:40::i;:::-;41619:94;;;;;;41723:50;41738:2;41742:14;41758;41723;:50::i;:::-;-1:-1:-1::0;;2204:1:0;2193:8;:12;-1:-1:-1;41555:225:0:o;3631:114:38:-;3723:6;;3731;;3631:114::o;6310:313:180:-;6411:5;6418;6537:31;-1:-1:-1;;;;;6537:44:180;;6590:4;6597:11;6610:5;6537:79;;;;;;;;;;;;;;;;;:::i;8341:156::-;8412:7;8466:24;342:1:19;8466:14:180;:24::i;:::-;8438:25;247:1:19;8438:14:180;:25::i;:::-;:52;;;;:::i;:::-;-1:-1:-1;;;;;8431:59:180;;;8341:156;:::o;8660:155::-;8741:7;8767:30;8777:13;:11;:13::i;:::-;8792:4;8767:9;:30::i;:::-;8798:9;8767:41;;;;;;;:::i;:::-;;;;;8760:48;;8660:155;;;;;:::o;24716:216:0:-;24797:25;24824;24851:28;2143:7;:5;:7::i;:::-;2171:1;2160:8;:12;24898:27:::1;24914:10:::0;24898:15:::1;:27::i;:::-;24891:34;;;;;;2204:1:::0;2193:8;:12;24716:216;;;;-1:-1:-1;24716:216:0:o;28422:2410::-;2143:7;:5;:7::i;:::-;2171:1;2160:8;:12;28834:46:::1;28871:8:::0;28834:36:::1;:46::i;:::-;;;;;28928:41;28971:14;28989:31;29004:8;29014:5;28989:14;:31::i;:::-;28927:93;;;;29034:9;29030:1796;;;29063:15:::0;29059:1331:::1;;29118:709;29153:8;29183:2;29207:11;29240:569;;;;;;;;29334:32;29240:569;;;;29426:32;29240:569;;;;29518:32;29240:569;;;;29594:16;29240:569;;;;29654:16;29240:569;;;;29713:15;29240:569;;;;29771:15;29240:569;;::::0;29118:13:::1;:709::i;:::-;29059:1331;;;1451:1:23;29852:15:0;:35:::0;29848:542:::1;;29907:280;29942:11;29975:8;30005:2;30029:32;30083;30137;29907:13;:280::i;29848:542::-;1495:1:23;30212:15:0;:39:::0;30208:182:::1;;30271:104;30289:11;30302:8;30312:2;30320:32;30316:1;:36;30358:16;30354:1;:20;30271:17;:104::i;:::-;30429:386;::::0;;38710:25:193;;;38766:2;38751:18;;38744:34;;;38794:18;;;38787:34;;;38852:2;38837:18;;38830:34;;;38895:3;38880:19;;38873:35;;;38939:3;38924:19;;38917:35;;;38983:3;38968:19;;38961:35;;;39027:3;39012:19;;39005:35;;;-1:-1:-1;;;;;30429:386:0;;::::1;::::0;;;::::1;::::0;::::1;::::0;38697:3:193;38682:19;30429:386:0::1;;;;;;;29030:1796;-1:-1:-1::0;;2204:1:0;2193:8;:12;-1:-1:-1;;;;;;;;;;28422:2410:0:o;7437:182:180:-;7523:7;7532;7541;7567:45;:43;:45::i;:::-;7560:52;;;;;;7437:182;;;:::o;2606:142:100:-;2655:35;2723:18;2702:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;2702:39:100;;;;;;;;;;;;;;;;;;;;;;2606:142;:::o;8957:295:180:-;9081:67;;-1:-1:-1;;;9081:67:180;;9136:4;9081:67;;;39219:51:193;9143:4:180;39286:18:193;;;39279:50;9008:20:180;;;;-1:-1:-1;;;;;9081:31:180;:46;;;;39192:18:193;;9081:67:180;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;9177:68;;-1:-1:-1;;;9177:68:180;;9232:4;9177:68;;;39219:51:193;9239:5:180;39286:18:193;;;39279:50;-1:-1:-1;;;;;9062:86:180;;;;;-1:-1:-1;9177:31:180;-1:-1:-1;;;;;9177:46:180;;-1:-1:-1;9177:46:180;;39192:18:193;;9177:68:180;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;8957:295;;-1:-1:-1;;;;;9158:87:180;;;;8957:295;-1:-1:-1;;8957:295:180:o;8503:151::-;8581:17;;:::i;:::-;8617:30;8627:13;:11;:13::i;6168:136::-;6223:5;;6254:4;:25;6280:16;:14;:16::i;13566:1346:0:-;2143:7;:5;:7::i;:::-;2171:1;2160:8;:12;;;2171:1;;;13744:40:::1;13781:2:::0;13744:36:::1;:40::i;:::-;13633:151;;;;;;;;-1:-1:-1::0;;;;;13799:33:0;::::1;:13;:33;:70;;;-1:-1:-1::0;;;;;;13836:33:0;::::1;13799:70;13795:118;;;13892:10;;-1:-1:-1::0;;;13892:10:0::1;;;;;;;;;;;13795:118;13974:21;13998:23;14008:2;373:1:19;13998:9:0;:23::i;:::-;13974:47;;14031:21;14055:23;14065:2;404:1:19;14055:9:0;:23::i;:::-;14031:47;;14089:109;14139:13;14154;14169;14184;14089:49;:109::i;:::-;14209:23;14234;14261:15;:13;:15::i;:::-;-1:-1:-1::0;;;;;14208:68:0::1;;;-1:-1:-1::0;;;;;14208:68:0::1;;;14286:23;14312:83;279:1:19;14343:13:0;14358:15;14375;14392:2;14312:19;:83::i;:::-;14286:109;;14405:23;14431:83;311:1:19;14462:13:0;14477:15;14494;14511:2;14431:19;:83::i;:::-;14405:109;;14546:1;14528:15;:19;:42;;;;14569:1;14551:15;:19;14528:42;14524:233;;;14586:160;14630:15:::0;14647;14664:33:::1;14682:15:::0;14630;14664:33:::1;:::i;:::-;14699;14717:15:::0;14699;:33:::1;:::i;14586:160::-;14766:21;:19;:21::i;:::-;14861:44;14895:2;14899:5;14861:33;:44::i;:::-;-1:-1:-1::0;;2204:1:0;2193:8;:12;-1:-1:-1;;;;;;;;;13566:1346:0:o;41889:54::-;2143:7;:5;:7::i;:::-;2171:1;2160:8;:12;41929:7:::1;:5;:7::i;:::-;2204:1:::0;2193:8;:12;41889:54::o;1908:204:20:-;1997:14;2032:5;2036:1;2032;:5;:::i;:::-;2023:14;;2056:10;:49;;2095:10;2104:1;2095:6;:10;:::i;:::-;2056:49;;;2069:23;2082:6;2090:1;2069:12;:23::i;:::-;2047:58;1908:204;-1:-1:-1;;;;;1908:204:20:o;1966:3501:26:-;2048:5;821:7;2069:11;:31;:66;;;;2124:11;-1:-1:-1;;;2104:31:26;2069:66;2065:97;;;2144:18;;-1:-1:-1;;;2144:18:26;;;;;;;;;;;2065:97;-1:-1:-1;;;;;2271:41:26;;2268:1;2264:49;2361:9;;;2434:18;2428:25;;2425:1;2421:33;2502:9;;;2575:10;2569:17;;2566:1;2562:25;2635:9;;;2708:6;2702:13;;2699:1;2695:21;2764:9;;;2837:4;2831:11;;2828:1;2824:19;;;2891:9;;;2964:3;2958:10;;2955:1;2951:18;3017:9;;;3084:10;;;3081:1;3077:18;;;3143:9;;;;3203:10;;;2474;;2607;;;2736;;;2863;2989;;;3115;3233;2173:9;3323:3;3316:10;;3312:95;;3354:3;3348;:9;3332:11;:26;;3328:30;;3312:95;;;3403:3;3397;:9;3381:11;:26;;3377:30;;3312:95;-1:-1:-1;3515:9:26;;;3510:3;3506:19;;;3547:11;;;;3625:9;;;;3690;;3681:19;;;3722:11;;;3800:9;3865;;3856:19;;;3897:11;;;3975:9;4040;;4031:19;;;4072:11;;;4150:9;4215;;4206:19;;;4247:11;;;4325:9;4390;;4381:19;;;4422:11;;;4500:9;4565;;4556:19;;;4597:11;;;4675:9;4740;;4731:19;;;4772:11;;;4850:9;4915;;4906:19;;;;4947:11;;;;5025:9;;;;;3515;-1:-1:-1;;3433:17:26;;3455:2;3432:25;3596:10;;;;;;;3583:24;3771:10;;;;;;;3758:24;;;;3946:10;;;;;;;3933:24;;;;4121:10;;;;;;;4108:24;;;;4296:10;;;;;;;4283:24;4471:10;;;;;;;4458:24;4646:10;;;;;;;4633:24;4821:10;;;;;;;4808:24;4996:10;;;;;;;4983:24;550:20;5092:39;;-1:-1:-1;;5168:40:26;;3447:3;5167:49;;;;734:34;5253:39;;5252:48;;5319:17;;;;;;;;;5315:37;;-1:-1:-1;5345:7:26;1966:3501;-1:-1:-1;;;;;;1966:3501:26:o;5315:37::-;5396:11;5370:22;5385:6;5370:14;:22::i;:::-;:37;5366:56;;5416:6;1966:3501;-1:-1:-1;;;;;;;1966:3501:26:o;5366:56::-;-1:-1:-1;5443:7:26;1966:3501;-1:-1:-1;;;;;;1966:3501:26:o;21409:281:38:-;21540:31;21613:70;21650:15;21667;21613:36;:70::i;:::-;21583:100;21409:281;-1:-1:-1;;;;21409:281:38:o;2006:105:0:-;2050:8;;2062:1;2050:13;2046:59;;2086:8;;-1:-1:-1;;;2086:8:0;;;;;;;;;;;2046:59;2006:105::o;44736:282::-;44991:19;;26839:21:21;:15;:21;;;44902:109:0;;44920:15;;44937;;26839:21:21;;44972:38:0;;-1:-1:-1;;;44991:19:0;;;;26839:21:21;44972:38:0;:::i;:::-;44902:17;:109::i;:::-;44821:197;44736:282;;:::o;18395:122:38:-;18479:14;;-1:-1:-1;;;;;18479:14:38;;;;-1:-1:-1;;;18495:14:38;;;;;18395:122::o;48578:439:0:-;48679:14;48695;48713:18;:16;:18::i;:::-;48678:53;;;;48759:7;-1:-1:-1;;;;;48745:22:0;:2;-1:-1:-1;;;;;48745:22:0;;:48;;;;48785:7;-1:-1:-1;;;;;48771:22:0;:2;-1:-1:-1;;;;;48771:22:0;;48745:48;48741:104;;;48816:18;;-1:-1:-1;;;48816:18:0;;;;;;;;;;;48741:104;48858:17;;48854:73;;48877:50;48900:7;48909:2;48913:13;48877:22;:50::i;:::-;48941:17;;48937:73;;48960:50;48983:7;48992:2;48996:13;48960:22;:50::i;:::-;48668:349;;48578:439;;;:::o;17916:473:38:-;18035:7;18044;18064:14;18080;18098:18;:16;:18::i;:::-;18063:53;;;;18238:15;18210:25;279:1:19;18210:14:38;:25::i;:::-;-1:-1:-1;;;;;18148:87:38;18183:24;373:1:19;18183:14:38;:24::i;:::-;18148:32;;-1:-1:-1;;;18148:32:38;;18174:4;18148:32;;;7304:51:193;-1:-1:-1;;;;;18148:59:38;;;;;-1:-1:-1;;;;;18148:17:38;;;;;7277:18:193;;18148:32:38;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:59;;;;:::i;:::-;:87;;;;:::i;:::-;:105;;;;:::i;:::-;18357:15;18329:25;311:1:19;18329:14:38;:25::i;:::-;-1:-1:-1;;;;;18267:87:38;18302:24;404:1:19;18302:14:38;:24::i;:::-;18267:32;;-1:-1:-1;;;18267:32:38;;18293:4;18267:32;;;7304:51:193;-1:-1:-1;;;;;18267:59:38;;;;;-1:-1:-1;;;;;18267:17:38;;;;;7277:18:193;;18267:32:38;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:59;;;;:::i;:::-;:87;;;;:::i;:::-;:105;;;;:::i;:::-;18127:255;;;;;;17916:473;;;;;;:::o;10683:268:0:-;10815:16;10857:19;10867:9;10857:7;:19;:::i;:::-;10847:7;:29;10843:102;;;10914:19;10924:9;10914:7;:19;:::i;:::-;10903:31;;:7;:31;:::i;13190:370::-;13321:25;13397:26;1641:3;13397:7;:26;:::i;:::-;13378:16;1481:2;13378:7;:16;:::i;:::-;:45;:154;;13508:24;1532:1;13508:7;:24;:::i;13378:154::-;1641:3;13439:17;13449:7;13439;:17;:::i;:::-;13438:38;;;;:::i;11792:871::-;11990:25;12027:11;12041:78;12083:8;12093:7;12102:16;12041:41;:78::i;:::-;12027:92;-1:-1:-1;12153:26:0;1641:3;12153:7;:26;:::i;:::-;12134:16;1481:2;12134:7;:16;:::i;:::-;:45;12130:527;;;12244:174;1641:3;12326:14;12337:3;12326:8;:14;:::i;:::-;-1:-1:-1;;;12276:17:0;12286:7;12276;:17;:::i;:::-;12275:48;;;;:::i;:::-;:65;;;;:::i;:::-;12274:86;;;;:::i;:::-;-1:-1:-1;;;12244:12:0;:174::i;:::-;12224:194;;12130:527;;;12502:144;1532:1;12572:14;12583:3;12572:8;:14;:::i;:::-;12533:36;-1:-1:-1;;;12533:7:0;:36;:::i;12502:144::-;12482:164;;12130:527;12017:646;11792:871;;;;;;;:::o;16687:340:38:-;16785:22;16809;16835:51;16849:17;16868;16835:13;:51::i;:::-;16896:14;:31;;-1:-1:-1;;;;;16896:31:38;;;-1:-1:-1;;;;;;16937:31:38;;;;;-1:-1:-1;;;16937:31:38;;;;;;;;;;16984:36;;;3048:62:193;;;3141:2;3126:18;;3119:71;;;;16896:31:38;;-1:-1:-1;16937:31:38;;-1:-1:-1;16984:36:38;;3021:18:193;16984:36:38;;;;;;;16774:253;;16687:340;;:::o;10409:2305::-;-1:-1:-1;;;;;10502:23:38;;;;;:52;;;10553:1;10529:21;:25;;;10502:52;10498:2210;;;10803:9;:20;10864:19;;;10775:25;10925:20;;10803;10986:19;11319:17;;11245:183;;-1:-1:-1;;;11245:183:38;;-1:-1:-1;;;;;40921:32:193;;;11245:183:38;;;40903:51:193;;;;-1:-1:-1;;;;;11319:17:38;;;40970:18:193;;;40963:71;41082:10;41070:23;;41050:18;;;41043:51;-1:-1:-1;;;;;10803:20:38;;;41110:18:193;;;41103:34;;;-1:-1:-1;;;10864:19:38;;;;;;41153::193;;;41146:35;;;-1:-1:-1;;;10986:19:38;;;;;41197::193;;;41190:35;;;10803:20:38;;10864:19;;10925:20;;;10986:19;10775:25;;;11245:31;:47;;;;;;40875:19:193;;11245:183:38;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;;;;;11154:274:38;;;-1:-1:-1;;;;;11154:274:38;;;11545:36;11600:96;11617:27;11646:17;11665;475:4:19;11600:16:38;:96::i;:::-;11545:151;;11847:105;342:1:19;11872:4:38;11887;11894:28;11924:27;11847:6;:105::i;:::-;11990:48;12009:28;11990:18;:48::i;:::-;11966:9;:72;;247:1:19;;11966:72:38;;;;-1:-1:-1;;;;;11966:72:38;;:::i;:::-;;;;;;;;-1:-1:-1;;;;;11966:72:38;;;;;-1:-1:-1;;;;;11966:72:38;;;;;;12251:29;12247:1;:33;12243:285;;;12300:16;342:1:19;12300:6:38;:16::i;:::-;12456:38;;-1:-1:-1;;;12456:38:38;;12360:4;12456:38;;;7304:51:193;;;-1:-1:-1;;;;;12300:30:38;;;;;;12387:7;;12416:79;;12425:29;;12456:13;:23;;;;7277:18:193;;12456:38:38;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;12416:8;:79::i;:::-;12300:213;;-1:-1:-1;;;;;;12300:213:38;;;;;;;-1:-1:-1;;;;;41846:32:193;;;12300:213:38;;;41828:51:193;41915:32;;;;41895:18;;;41888:60;41964:18;;;41957:34;41801:18;;12300:213:38;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;12243:285;-1:-1:-1;;12639:20:38;:58;;-1:-1:-1;;;;;12639:58:38;26839:15:21;:21;;-1:-1:-1;;;12639:58:38;;;;-1:-1:-1;;;;;10498:2210:38;10409:2305;;:::o;46593:536:0:-;46795:4;-1:-1:-1;;;;;46787:25:0;;;46783:340;;46829:41;46872:14;46890:30;46905:8;46915:4;46890:14;:30::i;:::-;46828:92;;;;46938:9;:21;;;;46951:8;46938:21;46934:179;;;46979:40;47007:11;46979:27;:40::i;:::-;47037:61;;-1:-1:-1;;;47037:61:0;;-1:-1:-1;;;;;47037:31:0;:38;;;;:61;;47076:11;;47089:8;;47037:61;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;46814:309;;46593:536;;:::o;47464:1108::-;47577:41;;:::i;:::-;47620:14;47646:31;47680:13;:11;:13::i;:::-;47646:47;;47703:28;47734:33;47744:13;47759:7;47734:9;:33::i;:::-;47846:20;;;;47703:64;;-1:-1:-1;47846:25:0;;;:54;;-1:-1:-1;47875:20:0;;;;:25;;47846:54;:83;;;-1:-1:-1;47904:20:0;;;;:25;;47846:83;47834:95;;47944:9;47939:72;;47969:31;;;;47939:72;48022:23;48047;48075:13;:11;:13::i;:::-;48021:67;-1:-1:-1;;;;;48021:67:0;;;-1:-1:-1;;;;;48021:67:0;;;48100:13;48115;48132:31;-1:-1:-1;;;;;48132:44:0;;48198:4;48217:86;48241:61;48256:15;-1:-1:-1;;;48279:15:0;48296:5;48241:14;:61::i;48217:86::-;48317:20;48132:215;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;48483:17;;48099:248;;-1:-1:-1;48099:248:0;-1:-1:-1;48379:153:0;;48422:13;;48437:10;;48449:15;;48466;;-1:-1:-1;;;;;48483:17:0;48099:248;;48379:25;:153::i;:::-;48358:207;;;;;;;;47464:1108;;;;;:::o;5574:285:180:-;5622:17;5652:23;5677;5705:13;:11;:13::i;:::-;-1:-1:-1;;;;;;5651:67:180;;;;-1:-1:-1;5651:67:180;;-1:-1:-1;5728:19:180;5651:67;5751:22;-1:-1:-1;;;5651:67:180;5751:22;:::i;:::-;5750:42;;;;:::i;:::-;5728:64;;5816:36;5840:11;5816:23;:36::i;:::-;5802:50;;5641:218;;;5574:285;:::o;6442:130:38:-;6519:7;6545:9;6555;6545:20;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;6545:20:38;;;-1:-1:-1;;6442:130:38:o;11054:238:89:-;11155:7;11209:76;11225:26;11242:8;11225:16;:26::i;:::-;:59;;;;;11283:1;11268:11;11255:25;;;;;:::i;:::-;11265:1;11262;11255:25;:29;11225:59;34914:9:90;34907:17;;34795:145;11209:76:89;11181:25;11188:1;11191;11194:11;11181:6;:25::i;:::-;:104;;;;:::i;43240:1439:0:-;43358:23;43383;43408:22;43432;43470:21;43554:13;:11;:13::i;:::-;43956:20;;44033;;-1:-1:-1;;;;;43501:66:0;;;;-1:-1:-1;43501:66:0;;;;;-1:-1:-1;43501:66:0;-1:-1:-1;26839:21:21;:15;:21;;;43866:33:0;;;;-1:-1:-1;;;43956:20:0;;;;;;43937:39;;;44033:20;;;;44014:39;;44096:53;44110:15;44014:39;44096:13;:53::i;:::-;44180:91;44198:15;44215;44232:16;44250:20;44180:17;:91::i;:::-;44317:48;44332:15;44349;44317:14;:48::i;:::-;44282:83;;-1:-1:-1;44282:83:0;-1:-1:-1;44436:25:0;;;;44432:241;;44514:148;44553:16;44571:20;44593:21;44616:15;44633;44514:21;:148::i;:::-;-1:-1:-1;;;;;44477:185:0;;;;-1:-1:-1;44477:185:0;;-1:-1:-1;44432:241:0;43460:1219;;;;;43240:1439;;;;;:::o;16176:1167::-;16311:23;16346;16372:44;16390:4;16397:18;16372:9;:44::i;:::-;16346:70;;16486:15;16505:1;16486:20;16482:34;;16515:1;16508:8;;;;;16482:34;16526:21;16550:34;16565:18;16550:14;:34::i;:::-;-1:-1:-1;;;;;16526:58:0;;;16594:20;16617:31;16629:18;16617:11;:31::i;:::-;16594:54;-1:-1:-1;16677:76:0;16694:15;16711:13;16594:54;16740:12;16677:16;:76::i;:::-;16659:94;;16764:57;16824:369;;;;;;;;16881:1;16824:369;;;;16929:15;16913:13;:31;;;;:::i;:::-;16824:369;;;;16974:53;16989:37;443:1:19;16989:18:0;:37;:::i;:::-;16974:14;:53::i;:::-;-1:-1:-1;;;;;16824:369:0;;;;;17050:8;16824:369;;;;17094:25;247:1:19;17094:14:0;:25::i;:::-;-1:-1:-1;;;;;16824:369:0;;;;;17158:24;342:1:19;17158:14:0;:24::i;:::-;-1:-1:-1;;;;;16824:369:0;;;16764:429;-1:-1:-1;17204:45:0;16764:429;17204:28;:45::i;:::-;17260:76;17267:18;17287:10;17299:2;17303:15;17320;17260:6;:76::i;:::-;16336:1007;;;;16176:1167;;;;;:::o;18523:699:38:-;18573:22;18598:25;279:1:19;18598:14:38;:25::i;:::-;-1:-1:-1;;;;;18573:50:38;;;18633:22;18658:25;311:1:19;18658:14:38;:25::i;:::-;-1:-1:-1;;;;;18633:50:38;;;18693:21;18717:24;373:1:19;18717:14:38;:24::i;:::-;-1:-1:-1;;;;;18693:48:38;;;18751:21;18775:24;404:1:19;18775:14:38;:24::i;:::-;-1:-1:-1;;;;;18751:48:38;;;19060:14;19044:13;:30;:67;;19110:1;19044:67;;;19077:30;19093:14;19077:13;:30;:::i;:::-;19019:14;:93;;-1:-1:-1;;;;;;19019:93:38;-1:-1:-1;;;;;19019:93:38;;;;;;;;;;19147:30;;;:67;;19213:1;19147:67;;;19180:30;19196:14;19180:13;:30;:::i;:::-;19122:14;;:93;;;;;-1:-1:-1;;;;;19122:93:38;;;;;-1:-1:-1;;;;;19122:93:38;;;;;;18563:659;;;;18523:699::o;3484:141::-;3553:7;-1:-1:-1;;;;;3553:19:38;;:21;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;;;;;3539:35:38;:10;-1:-1:-1;;;;;3539:35:38;;3535:84;;3597:11;;-1:-1:-1;;;3597:11:38;;;;;;;;;;;20567:5181:89;20615:7;20733:1;20728;:6;20724:53;;-1:-1:-1;20761:1:89;20567:5181::o;20724:53::-;21717:1;21745;-1:-1:-1;;;21765:16:89;;21761:92;;21808:3;21801:10;;;;;21836:2;21829:9;21761:92;21877:7;21870:2;:15;21866:90;;21912:2;21905:9;;;;;21939:2;21932:9;21866:90;21980:7;21973:2;:15;21969:90;;22015:2;22008:9;;;;;22042:2;22035:9;21969:90;22083:7;22076:2;:15;22072:89;;22118:2;22111:9;;;;;22145:1;22138:8;22072:89;22185:6;22178:2;:14;22174:87;;22219:1;22212:8;;;;;22245:1;22238:8;22174:87;22285:6;22278:2;:14;22274:87;;22319:1;22312:8;;;;;22345:1;22338:8;22274:87;22385:6;22378:2;:14;22374:61;;22419:1;22412:8;22374:61;22861:1;:6;22872:1;22860:13;;;;;24771:1;22860:13;24771:6;;;;:::i;:::-;;24766:2;:11;24765:18;;24760:23;;24891:1;24884:2;24880:1;:6;;;;;:::i;:::-;;24875:2;:11;24874:18;;24869:23;;25002:1;24995:2;24991:1;:6;;;;;:::i;:::-;;24986:2;:11;24985:18;;24980:23;;25111:1;25104:2;25100:1;:6;;;;;:::i;:::-;;25095:2;:11;25094:18;;25089:23;;25221:1;25214:2;25210:1;:6;;;;;:::i;:::-;;25205:2;:11;25204:18;;25199:23;;25331:1;25324:2;25320:1;:6;;;;;:::i;:::-;;25315:2;:11;25314:18;;25309:23;;25703:28;25728:2;25724:1;:6;;;;;:::i;:::-;;25719:11;;;34795:145:90;25703:28:89;25698:33;;;20567:5181;-1:-1:-1;;;20567:5181:89:o;17549:310:38:-;17645:7;17654;17673:22;17698:35;17717:15;17698:18;:35::i;:::-;17673:60;;17743:22;17768:35;17787:15;17768:18;:35::i;:::-;17821:14;;;;-1:-1:-1;17549:310:38;;-1:-1:-1;;;;17549:310:38:o;4735:507::-;4874:27;4893:7;4874:18;:27::i;:::-;4850:9;4860;4850:20;;;;;;;:::i;:::-;;;;;;;;;;;;:51;;;;;;;;;;-1:-1:-1;;;;;4850:51:38;;;;;:::i;:::-;;;;;;;;-1:-1:-1;;;;;4850:51:38;;;;;-1:-1:-1;;;;;4850:51:38;;;;;;4911:21;4958:26;4977:6;4958:18;:26::i;:::-;4935:9;4945;4935:20;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;4935:20:38;:49;;;;:::i;:::-;4911:73;;5017:13;4994:9;5004;4994:20;;;;;;;:::i;:::-;;;;;;;;;;;;:36;;;;;-1:-1:-1;;;;;4994:36:38;;;;;-1:-1:-1;;;;;4994:36:38;;;;;;5040:38;5053:9;5064:13;5040:12;:38::i;:::-;5179:17;5186:9;5179:6;:17::i;:::-;:56;;-1:-1:-1;;;5179:56:38;;-1:-1:-1;;;;;42740:32:193;;;5179:56:38;;;42722:51:193;42809:32;;;42789:18;;;42782:60;42858:18;;;42851:34;;;42901:18;;;42894:34;;;5179:27:38;;;;;;;42694:19:193;;5179:56:38;42491:443:193;19705:395:38;19807:30;19839:21;19862:36;19939:25;247:1:19;19939:14:38;:25::i;:::-;-1:-1:-1;;;;;19914:50:38;;;19990:24;342:1:19;19990:14:38;:24::i;:::-;-1:-1:-1;;;;;19974:40:38;;-1:-1:-1;20055:38:38;19974:40;20055:22;:38;:::i;:::-;20024:69;;19705:395;;;:::o;49023:1375:0:-;49371:23;49396;49432;49457;49484:15;:13;:15::i;:::-;-1:-1:-1;;;;;49431:68:0;;;-1:-1:-1;;;;;49431:68:0;;;49510:28;49540;49572:269;49627:13;49654:15;49683;49712:21;49747:22;49783;49819:12;49572:41;:269::i;:::-;49509:332;;;;49852:28;49882;49914:269;49969:13;49996:15;50025;50054:21;50089:22;50125;50161:12;49914:41;:269::i;:::-;49851:332;;;;50253:20;50230;:43;:161;;50348:20;50370;50230:161;;;50289:20;50311;50230:161;50193:198;;;;;;;;49421:977;;;;;;49023:1375;;;;;;;;;;;:::o;17033:510:38:-;17230:52;17245:17;17264;17230:14;:52::i;:::-;17375:17;;17333:203;;17360:76;;-1:-1:-1;;;;;17375:17:38;17394;17413:15;17375:17;17360:14;:76::i;:::-;17465:17;;17450:76;;-1:-1:-1;;;17465:17:38;;-1:-1:-1;;;;;17465:17:38;17484;17503:15;17520:5;17450:14;:76::i;:::-;17333:13;:203::i;:::-;17293:17;17292:244;;-1:-1:-1;;;;;;17292:244:38;-1:-1:-1;;;;;;;;17292:244:38;;;;-1:-1:-1;;;;;;17292:244:38;;;;;;;;;;;;-1:-1:-1;;;;17033:510:38:o;47135:323:0:-;47234:41;47277:14;47295:30;47310:8;47320:4;47295:14;:30::i;:::-;47233:92;;;;47339:12;:25;;;;47355:9;47339:25;47335:117;;;47380:61;;-1:-1:-1;;;47380:61:0;;-1:-1:-1;;;;;47380:31:0;:38;;;;:61;;47419:11;;47432:8;;47380:61;;;:::i;6578:133:38:-;6658:7;6684:9;6694;6684:20;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;6684:20:38;6677:27;;6578:133;;;:::o;6408:1682:29:-;6593:18;;:28;;;;;6565:25;;;;6670:29;;6858;;;;6827:28;;;;6959:29;;;;6928:28;;;;;6565:56;;;;6670:60;;;;;-1:-1:-1;;;;;7007:42:29;;;;;7030:19;7007:42;7003:902;;;7138:18;;:28;;;7194:21;;;;7093:270;;7123:100;;7168:24;;7217:5;7123:14;:100::i;:::-;7260:18;;7245:100;;404:1:19;7260:28:29;;;;7290:24;7316:6;:21;;;7339:5;7245:14;:100::i;:::-;7093:8;:270::i;:::-;7069:294;;;;7003:902;;;7406:19;7402:236;;;7513:18;;7473:146;;373:1:19;7513:28:29;;;;7543:24;7569:6;:21;;;7592:5;7473:14;:146::i;:::-;7449:170;;;;7402:236;7659:19;7655:236;;;7766:18;;7726:146;;404:1:19;7766:28:29;;;;7796:24;7822:6;:21;;;7845:5;7726:14;:146::i;:::-;7702:170;;;;7655:236;7923:18;;:29;4401:3:30;7979:27:29;;669:2;7923:53;;;:83;7919:155;;;8033:26;;-1:-1:-1;;;8033:26:29;;;;;;;;;;;1541:361:20;1695:15;1726:11;1741:1;1726:16;1722:105;;-1:-1:-1;1765:6:20;1758:13;;1722:105;1843:52;1850:6;1858:11;1871;1884:10;1843:6;:52::i;6187:249:38:-;6265:7;6393:17;6400:9;6393:6;:17::i;:::-;:36;;-1:-1:-1;;;6393:36:38;;-1:-1:-1;;;;;7322:32:193;;;6393:36:38;;;7304:51:193;6393:27:38;;;;;;;7277:18:193;;6393:36:38;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;1174:361:20:-;1328:15;1359:11;1374:1;1359:16;1355:105;;-1:-1:-1;1398:6:20;1391:13;;1355:105;1476:52;1483:6;1491:11;1504;1517:10;1476:6;:52::i;344:824::-;584:7;925:226;957:77;972:15;989:14;1005:21;1028:5;957:14;:77::i;:::-;1052:22;1092;1132:5;925:14;:226::i;5248:613:38:-;5558:17;5565:9;5558:6;:17::i;:::-;:58;;-1:-1:-1;;;5558:58:38;;-1:-1:-1;;;;;42740:32:193;;;5558:58:38;;;42722:51:193;42809:32;;;42789:18;;;42782:60;42858:18;;;42851:34;;;42901:18;;;42894:34;;;5558:27:38;;;;;;;42694:19:193;;5558:58:38;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;5650:27;5669:7;5650:18;:27::i;:::-;5626:9;5636;5626:20;;;;;;;:::i;:::-;;;;;;;;;;;;:51;;;;;;;;;;-1:-1:-1;;;;;5626:51:38;;;;;:::i;:::-;;;;;;;;-1:-1:-1;;;;;5626:51:38;;;;;-1:-1:-1;;;;;5626:51:38;;;;;;5687:21;5734:26;5753:6;5734:18;:26::i;:::-;5711:9;5721;5711:20;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;5711:20:38;:49;;;;:::i;:::-;5687:73;;5793:13;5770:9;5780;5770:20;;;;;;;:::i;:::-;;;;;;;;;;;;:36;;;;;-1:-1:-1;;;;;5770:36:38;;;;;-1:-1:-1;;;;;5770:36:38;;;;;;5816:38;5829:9;5840:13;5816:12;:38::i;18577:816:0:-;18835:20;18871:16;;18867:520;;18903:37;;;18954:23;;;:33;;;19034;19049:17;19034:14;:33::i;:::-;-1:-1:-1;;;;;19001:66:0;:30;;;:66;19115:34;19130:18;19115:14;:34::i;:::-;-1:-1:-1;;;;;19081:68:0;:31;;;:68;19164:45;19081:15;19164:28;:45::i;:::-;19293:83;19327:2;19331:17;19350:12;475:4:19;19293:33:0;:83::i;:::-;19278:98;18577:816;-1:-1:-1;;;;;;;18577:816:0:o;22735:1129::-;22802:23;22827;22862;22895;23011:48;23048:10;23011:36;:48::i;:::-;22928:131;;-1:-1:-1;22928:131:0;-1:-1:-1;22928:131:0;;-1:-1:-1;22928:131:0;-1:-1:-1;23071:23:0;;23123:15;:13;:15::i;:::-;-1:-1:-1;;;;;23070:68:0;;;-1:-1:-1;;;;;23070:68:0;;;23149:23;23182;23264:84;23276:10;23288:15;23305;23322;373:1:19;23264:11:0;:84::i;:::-;23215:133;-1:-1:-1;23215:133:0;-1:-1:-1;23407:84:0;23419:10;23431:15;23448;23465;404:1:19;23407:11:0;:84::i;:::-;23358:133;-1:-1:-1;23358:133:0;-1:-1:-1;23506:19:0;;;;:42;;;23533:15;23529:1;:19;23506:42;23502:233;;;23564:160;23608:15;23625;23642:33;23660:15;23608;23642:33;:::i;:::-;23677;23695:15;23677;:33;:::i;23564:160::-;23745:21;:19;:21::i;:::-;23806:51;23840:10;23852:4;23806:33;:51::i;:::-;22852:1012;;;;;;22735:1129;;;:::o;12720:520:38:-;12834:28;;:::i;:::-;12879:9;12874:360;12894:17;12890:1;:21;12874:360;;;12932:21;12956;12966:7;12975:1;12956:9;:21::i;:::-;12932:45;-1:-1:-1;12995:17:38;;12991:233;;13123:86;13140:13;13155;13169:1;13155:16;;;;;;;:::i;:::-;;;;;-1:-1:-1;;;;;13123:86:38;13173:9;13183:1;13173:12;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;13173:12:38;-1:-1:-1;;;;;13123:86:38;13207:1;443::19;13187:21:38;;13123:16;:86::i;:::-;13107:10;13118:1;13107:13;;;;;;;:::i;:::-;;;;:102;12991:233;-1:-1:-1;12913:3:38;;12874:360;;;;12720:520;;;;:::o;24938:2533:0:-;25014:25;25041;25068:28;25108:23;25141;25261:48;25298:10;25261:36;:48::i;:::-;25174:135;;-1:-1:-1;25174:135:0;-1:-1:-1;25174:135:0;;-1:-1:-1;25174:135:0;-1:-1:-1;25340:36:0;25379:21;342:1:19;25379:11:0;:21::i;:::-;25340:60;;25410:34;25447:24;342:1:19;25447:14:0;:24::i;:::-;-1:-1:-1;;;;;25410:61:0;;;25482:28;25567:347;25617:17;25648;25679:15;25708;25737:60;25764:15;25781;25737:26;:60::i;:::-;25811:26;25851:28;475:4:19;25567:36:0;:347::i;:::-;25520:394;;-1:-1:-1;25520:394:0;-1:-1:-1;26071:1:0;26047:25;;;26043:91;;26095:28;;-1:-1:-1;;;26095:28:0;;;;;;;;;;;26043:91;26143:30;26176:31;26186:10;342:1:19;26176:9:0;:31::i;:::-;26143:64;;26245:22;26222:20;:45;26218:347;;;26306:22;26283:45;;26393:147;26431:20;26453:26;26481:28;475:4:19;26393:16:0;:147::i;:::-;26370:170;;26218:347;26602:26;26579:20;:49;26575:342;;;26880:26;26857:49;;26575:342;27014:84;342:1:19;27031:10:0;27043;27055:20;27077;27014:6;:84::i;:::-;27217:156;27257:15;27274;27291:35;27309:17;27257:15;27291:35;:::i;:::-;27328;27346:17;27328:15;:35;:::i;27217:156::-;27413:51;27447:10;27459:4;27413:33;:51::i;:::-;25098:2373;;;;;;24938:2533;;;;;:::o;31292:3252::-;31565:35;31658:21;:37;;;31654:1;:41;:86;;;;31703:21;:37;;;31699:1;:41;31654:86;31650:597;;;31756:91;31770:21;:37;;;31809:21;:37;;;31756:13;:91::i;:::-;31862:30;31894;31928:16;31935:8;31928:6;:16::i;:::-;31861:83;;;;32021:215;32050:22;32090:21;:37;;;32145:22;32185:21;:37;;;32021:11;:215::i;:::-;31742:505;;31650:597;32336:21;:38;;;32332:1;:42;:88;;;;32382:21;:38;;;32378:1;:42;32332:88;32328:743;;;32436:93;32450:21;:38;;;32490:21;:38;;;32436:13;:93::i;:::-;32544:31;32577;32610:36;32666:25;32682:8;32666:15;:25::i;:::-;32543:148;;;;;;32768:219;32797:23;32838:21;:38;;;32894:23;32935:21;:38;;;32768:11;:219::i;:::-;33032:28;-1:-1:-1;;;32328:743:0;33147:157;;-1:-1:-1;;;33147:157:0;;33132:12;;-1:-1:-1;;;;;33147:31:0;:60;;;;:157;;33221:11;;33234:8;;33244:21;;33267:27;;33147:157;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;33132:172;;33319:7;33315:1223;;;33346:31;;33342:104;;33397:34;33409:8;342:1:19;33429::0;33397:11;:34::i;:::-;33467:21;:37;;;33463:1;:41;:86;;;;33512:21;:37;;;33508:1;:41;33463:86;33459:324;;;33570:23;33595;33623:13;:11;:13::i;:::-;33569:67;-1:-1:-1;;;;;33569:67:0;;;-1:-1:-1;;;;;33569:67:0;;;33654:48;33666:8;373:1:19;33686:15:0;33654:11;:48::i;:::-;33720;33732:8;404:1:19;33752:15:0;33720:11;:48::i;:::-;33551:232;;33459:324;33864:7;:5;:7::i;:::-;33972:22;;:33;;;34023;;;34074;;;;;33886:235;;33926:8;;33952:2;;33972:33;;34023;33886:22;:235::i;:::-;33315:1223;;;34229:298;34269:8;34295:2;34315:21;:54;;;34387:21;:54;;;34459:21;:54;;;34229:22;:298::i;35478:862::-;35772:269;;-1:-1:-1;;;35772:269:0;;:11;;:29;;:269;;35815:31;;35860:11;;35885:8;;35907:32;;35953;;35999;;35772:269;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;36125:208;36161:8;36183:2;36199:32;36245;36291;36125:22;:208::i;36714:2487::-;37083:79;;-1:-1:-1;;;37083:79:0;;36996:72;;37083:11;;:48;;:79;;37132:11;;37145:8;;37155:6;;37083:79;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;36996:166;;37216:6;37212:950;;;37242:28;37274:25;37290:8;37274:15;:25::i;:::-;37238:61;;;;37340:26;:41;;;37317:20;:64;37313:153;;;37408:43;;-1:-1:-1;;;37408:43:0;;;;;;;;;;;37313:153;37483:26;:34;;;37479:74;;;37519:34;37531:8;342:1:19;37551::0;37519:11;:34::i;:::-;37224:340;37212:950;;;37585:23;37610;37637:16;37644:8;37637:6;:16::i;:::-;37584:69;;;;37706:26;:41;;;37688:15;:59;:142;;;;37789:26;:41;;;37771:15;:59;37688:142;37667:261;;;37870:43;;-1:-1:-1;;;37870:43:0;;;;;;;;;;;37667:261;37945:26;:34;;;37941:211;;;37999:60;38011:8;373:1:19;38031:11:0;:27;;;37999:11;:60::i;:::-;38077;38089:8;404:1:19;38109:11:0;:27;;;38077:11;:60::i;:::-;37570:592;;37212:950;38176:8;38172:875;;;38200:263;38237:8;38263:2;38327:26;:43;;;38283:26;:41;;;:87;;;;:::i;:::-;247:1:19;38415:26:0;:34;;;38200:19;:263::i;:::-;38172:875;;;38494:264;38531:8;38557:2;38621:26;:44;;;38577:26;:41;;;:88;;;;:::i;:::-;279:1:19;38710:26:0;:34;;;38494:19;:264::i;:::-;38772;38809:8;38835:2;38899:26;:44;;;38855:26;:41;;;:88;;;;:::i;:::-;311:1:19;38988:26:0;:34;;;38772:19;:264::i;:::-;39060:26;:34;;;39056:139;;;39177:7;:5;:7::i;5278:393:29:-;5513:1;5495:15;:19;:43;;;;;5537:1;5518:16;:20;5495:43;5494:94;;;;5562:1;5544:15;:19;:43;;;;;5586:1;5567:16;:20;5544:43;5490:175;;;5611:43;;-1:-1:-1;;;5611:43:29;;;;;;;;;;;14918:572:0;15119:22;15157:16;;15153:331;;15206:87;15249:12;15263:13;15278:14;15206:42;:87::i;:::-;15189:104;-1:-1:-1;15307:23:0;15341:29;15189:104;15341:12;:29;:::i;:::-;15307:64;-1:-1:-1;15385:88:0;15419:2;15423:18;-1:-1:-1;;;;;15385:88:0;;15460:12;15385:33;:88::i;:::-;;15175:309;14918:572;;;;;;;:::o;42024:357::-;42060:23;42085;42110:20;42132;42168:50;2144:1:30;42168:36:0;:50::i;:::-;42059:159;;;;;;;;42228:146;42268:15;42285;42320:12;42302:15;:30;;;;:::i;:::-;42334;42352:12;42334:15;:30;:::i;6215:704:89:-;6277:7;6300:1;6305;6300:6;6296:150;;6400:35;1035:4:79;6400:11:89;:35::i;:::-;6896:1;6891;6887;:5;6886:11;;;;;:::i;:::-;;6900:1;6886:15;6876:5;;;6860:42;;6215:704;-1:-1:-1;;;6215:704:89:o;5473:602:26:-;5546:19;-1:-1:-1;;5581:15:26;;;;;;:34;;-1:-1:-1;5600:15:26;;;;1234:6;5600:15;5581:34;5577:64;;;5624:17;;-1:-1:-1;;;5624:17:26;;;;;;;;;;;5577:64;5651:21;;;;:14;5708:11;;;:32;;5733:7;5708:32;;;5722:8;5723:7;5722:8;:::i;:::-;5682:59;-1:-1:-1;5848:12:26;5859:1;5682:59;5848:12;:::i;:::-;;;5884:29;5905:7;5884:20;:29::i;:::-;5870:43;-1:-1:-1;5937:6:26;5927:16;;:21;5923:75;;5995:3;5965:25;:11;5979;5965:25;:::i;:::-;5964:34;;5950:48;;5923:75;6017:4;6013:8;;:1;:8;6009:59;;;6037:31;6057:11;-1:-1:-1;;6037:31:26;:::i;:::-;6023:45;;6009:59;5567:508;;5473:602;;;:::o;21696:453:38:-;21837:31;21870;21939:60;21966:15;21983;21939:26;:60::i;:::-;22062:25;;21913:86;;-1:-1:-1;22047:95:38;;-1:-1:-1;;;;;;;;22062:25:38;;;;;21913:86;;22114:20;;22047:14;:95::i;:::-;22009:133;;21696:453;;;;;:::o;45075:754:0:-;45270:20;45266:24;;:1;:24;:47;;;;;45298:15;45294:1;:19;45266:47;:70;;;;;45321:15;45317:1;:19;45266:70;45262:561;;;45352:13;45368:86;45392:61;45407:15;-1:-1:-1;;;45430:15:0;45447:5;45392:14;:61::i;45368:86::-;45614:80;;-1:-1:-1;;;45614:80:0;;47701:1:193;47690:21;;;45614:80:0;;;47672:40:193;47760:10;47748:23;;47728:18;;;47721:51;45352:102:0;;-1:-1:-1;45614:31:0;-1:-1:-1;;;;;45614:49:0;;;;47645:18:193;;45614:80:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;45610:157;;;45714:19;:38;;-1:-1:-1;;;;;45714:38:0;-1:-1:-1;;;45714:38:0;;;;;;;45610:157;45781:31;45804:7;45781:31;;:22;:31::i;1219:204:76:-;1306:37;1320:5;1327:2;1331:5;1338:4;1306:13;:37::i;:::-;1301:116;;1366:40;;-1:-1:-1;;;1366:40:76;;-1:-1:-1;;;;;7322:32:193;;1366:40:76;;;7304:51:193;7277:18;;1366:40:76;7135:226:193;2332:2247:24;2483:11;2510:5;2519:1;2510:10;2506:49;;-1:-1:-1;2543:1:24;2536:8;;2506:49;2585:16;2568:14;:33;2564:2009;;;2698:16;2673:22;2681:14;2673:5;:22;:::i;:::-;:41;2669:1097;;;2915:14;2957:16;2932:22;2940:14;2932:5;:22;:::i;:::-;:41;;;;:::i;:::-;2915:58;-1:-1:-1;3020:48:24;1619:1;3020:16;:48;:::i;:::-;2995:22;3003:14;2995:5;:22;:::i;:::-;:73;2991:535;;;3238:74;3253:16;-1:-1:-1;;;3294:10:24;3298:6;1364:2;3294:10;:::i;:::-;3306:5;3238:14;:74::i;:::-;3228:84;;-1:-1:-1;;;3228:84:24;:::i;:::-;3170:143;;1767:4;3170:143;:::i;:::-;3164:149;;2991:535;;;3430:77;-1:-1:-1;;;3475:6:24;3483:16;3501:5;3430:14;:77::i;:::-;3424:83;;2991:535;3549:41;3564:3;3569:6;3577:5;3584;3549:14;:41::i;:::-;3543:47;;2716:889;2564:2009;;2669:1097;-1:-1:-1;876:18:24;2564:2009;;;3811:48;1619:1;3811:16;:48;:::i;:::-;3786:22;3794:14;3786:5;:22;:::i;:::-;:73;3782:791;;;4047:255;4091:16;-1:-1:-1;;;4206:33:24;4091:16;4206:14;:33;:::i;:::-;4201:39;;:1;:39;:::i;:::-;4193:47;;:5;:47;:::i;:::-;4188:53;;1364:2;4188:53;:::i;4047:255::-;4013:289;;-1:-1:-1;;;4013:289:24;:::i;:::-;3942:378;;1767:4;3942:378;:::i;:::-;3936:384;;3782:791;;;4414:148;-1:-1:-1;;;4518:5:24;4481:33;4498:16;4481:14;:33;:::i;:::-;4476:39;;:1;:39;:::i;:::-;:47;;;;:::i;:::-;4525:16;4543:5;4414:14;:148::i;9264:218:90:-;9321:7;-1:-1:-1;;;;;9344:25:90;;9340:105;;;9392:42;;-1:-1:-1;;;9392:42:90;;9423:3;9392:42;;;47965:36:193;48017:18;;;48010:34;;;47938:18;;9392:42:90;47783:267:193;9340:105:90;-1:-1:-1;9469:5:90;9264:218::o;5617:111:89:-;5675:7;5312:5;;;5709;;;5311:36;5306:42;;5701:20;5071:294;4938:334:29;5034:69;5091:11;5034:56;:69::i;:::-;5113:36;5152:30;5170:11;5152:17;:30::i;:::-;5113:69;;5192:73;5215:14;5231:11;:33;;;5192:22;:73::i;2467:977::-;2744:41;;:::i;:::-;2856:23;;;;2829:24;;2797:29;;2829:50;;;:::i;:::-;-1:-1:-1;;;;;2797:82:29;;;2896:541;;;;;;;;2945:10;2896:541;;;;2988:36;3016:7;2988:27;:36::i;:::-;2896:541;;;;3057:36;3085:7;3057:27;:36::i;:::-;2896:541;;;;3135:123;3167:42;3177:31;3194:14;3177;:31;:::i;3167:42::-;-1:-1:-1;;;3216:21:29;3239:5;3135:14;:123::i;:::-;2896:541;;;;3295:41;3319:17;3295:21;:41;:::i;:::-;2896:541;;;;3367:14;2896:541;;;;3412:14;2896:541;;;2889:548;;;2467:977;;;;;;;;;:::o;32020:122:89:-;32088:4;32129:1;32117:8;32111:15;;;;;;;;:::i;:::-;:19;;;;:::i;:::-;:24;;32134:1;32111:24;32104:31;;32020:122;;;:::o;7242:3683::-;7324:14;7375:12;7389:11;7404:12;7411:1;7414;7404:6;:12::i;:::-;7374:42;;;;7498:4;7506:1;7498:9;7494:365;;7833:11;7827:3;:17;;;;;:::i;:::-;;7820:24;;;;;;7494:365;7984:4;7969:11;:19;7965:142;;8008:84;5312:5;8028:16;;5311:36;940:4:79;5306:42:89;8008:11;:84::i;:::-;8359:17;8510:11;8507:1;8504;8497:25;8902:12;8932:15;;;8917:31;;9067:22;;;;;9800:1;9781;:15;;9780:21;;10033;;;10029:25;;10018:36;10103:21;;;10099:25;;10088:36;10175:21;;;10171:25;;10160:36;10246:21;;;10242:25;;10231:36;10319:21;;;10315:25;;10304:36;10393:21;;;10389:25;;;10378:36;9309:12;;;;9305:23;;;9330:1;9301:31;8622:18;;;8612:29;;;9416:11;;;;8665:19;;;;9160:14;;;;9409:18;;;;10868:13;;-1:-1:-1;;7242:3683:89;;;;;:::o;13246:2409:38:-;13475:24;13501;13537:57;;:::i;:::-;13973:130;;-1:-1:-1;;;13973:130:38;;;48537:23:193;;;13973:130:38;;;48519:42:193;48597:23;;48577:18;;;48570:51;13618:22:38;;;;-1:-1:-1;;;;;13973:31:38;:85;;;;48492:18:193;;13973:130:38;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;14131:20;:39;;;;;;-1:-1:-1;;;14131:39:38;-1:-1:-1;;;;;14131:39:38;;;;;;;;;;14207:348;;;;;;;;;;;;;-1:-1:-1;14207:348:38;;;;;;;13931:172;;-1:-1:-1;13931:172:38;;-1:-1:-1;14207:348:38;;;14381:66;14414:15;14431;14381:32;:66::i;:::-;14207:348;;;;;;;;;;;;;;;;;;-1:-1:-1;;14207:348:38;;-1:-1:-1;;14207:348:38;;;;;;;;;;;-1:-1:-1;;;;;14207:348:38;-1:-1:-1;;;;;14207:348:38;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;14521:19;14207:348;;;14184:371;;13604:962;;14577:22;14601;14625:35;14676:8;:50;14727:9;14738:20;14676:83;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;14576:183;;-1:-1:-1;14576:183:38;-1:-1:-1;14576:183:38;-1:-1:-1;14770:82:38;14785:32;14576:183;14785:15;:32;:::i;:::-;14819;14837:14;14819:15;:32;:::i;14770:82::-;14922:32;14940:14;14922:15;:32;:::i;:::-;14965;14983:14;14965:15;:32;:::i;:::-;14862:137;;;;;;;;15010:13;15026:7;-1:-1:-1;;;;;15026:13:38;;:15;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;15010:31;-1:-1:-1;15052:64:38;247:1:19;15010:31:38;15087:17;247:1:19;15087:28:38;;;;;15052:16;:64::i;:::-;15126;279:1:19;15154:5:38;15161:17;279:1:19;15161:28:38;;15126:64;15200;311:1:19;15228:5:38;15235:17;311:1:19;15235:28:38;;15200:64;15275:43;;;;;;;;;;-1:-1:-1;;15309:9:38;;15275:43;;15309:9;-1:-1:-1;15275:43:38;;;;;;;;;;;-1:-1:-1;;;;;15275:43:38;-1:-1:-1;;;;;15275:43:38;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;15350:13;247:1:19;15350:24:38;;;;;;;:::i;:::-;;;;;-1:-1:-1;;;;;15328:46:38;;:19;:46;-1:-1:-1;15406:24:38;;;;-1:-1:-1;;;;;15384:46:38;;279:1:19;15384:46:38;-1:-1:-1;15462:24:38;;;;-1:-1:-1;;;;;15440:46:38;;311:1:19;15440:46:38;-1:-1:-1;15517:23:38;;;;-1:-1:-1;;;;;15496:44:38;;342:1:19;15496:44:38;-1:-1:-1;15571:23:38;;;;-1:-1:-1;;;;;15550:44:38;;373:1:19;15550:44:38;-1:-1:-1;15625:23:38;;;;-1:-1:-1;;;;;15604:44:38;;404:1:19;15604:44:38;;13527:2128;;;;;;13246:2409;;;;;;;;:::o;5677:725:29:-;5805:37;5845:209;5877:6;:14;;;5909:6;:30;;;5987:6;:30;;;5957:6;:27;;;:60;6035:5;5845:14;:209::i;:::-;5805:249;;6284:6;:21;;;6252:29;6236:6;:13;;;:45;:69;6211:6;:22;;;6090:98;6122:29;6105:6;:14;;;:46;669:2;4401:3:30;6182:5:29;6090:14;:98::i;:::-;:143;:215;6069:317;;;6345:26;;-1:-1:-1;;;6345:26:29;;;;;;;;;;;10282:218:90;10339:7;-1:-1:-1;;;;;10362:25:90;;10358:105;;;10410:42;;-1:-1:-1;;;10410:42:90;;10441:3;10410:42;;;47965:36:193;48017:18;;;48010:34;;;47938:18;;10410:42:90;47783:267:193;3751:759:38;3951:19;;3974:1;3951:24;3947:37;;3751:759;;:::o;3947:37::-;3998:9;3994:510;;-1:-1:-1;;;;;4036:28:38;;;:19;:28;;10409:2305;;:::o;3994:510::-;279:1:19;4085:9:38;:22;4081:423;;-1:-1:-1;;;;;4123:28:38;;;:19;:28;;10409:2305;;:::o;4081:423::-;311:1:19;4172:9:38;:22;4168:336;;-1:-1:-1;;;;;4210:28:38;;;:19;:28;;10409:2305;;:::o;4168:336::-;342:1:19;4259:9:38;:21;4255:249;;-1:-1:-1;;;;;4296:27:38;;;:18;:27;;10409:2305;;:::o;4255:249::-;373:1:19;4344:9:38;:21;4340:164;;-1:-1:-1;;;;;4381:27:38;;;:18;:27;;10409:2305;;:::o;4340:164::-;404:1:19;4429:9:38;:21;4425:79;;-1:-1:-1;;;;;4466:27:38;;;:18;:27;;3751:759;;:::o;2118:1299:20:-;2435:23;2460;2576:74;2583:14;2599:21;2622:13;2637:12;2576:6;:74::i;:::-;2558:92;-1:-1:-1;2724:21:20;4401:3:30;2724:14:20;:21;:::i;:::-;2698:22;335:2;2698:13;:22;:::i;:::-;:47;;:712;;3056:340;3124:14;3160:13;3195:14;3231:21;3274:24;3320;3366:12;3056:46;:340::i;:::-;2698:712;;;2778:15;2918:91;2778:15;2944:24;2970;2996:12;2918:8;:91::i;:::-;2661:749;;;;-1:-1:-1;2118:1299:20;-1:-1:-1;;;;;;;;2118:1299:20:o;5435:111:89:-;5493:7;5312:5;;;5527;;;5311:36;5306:42;;5519:20;5071:294;19399:390:0;19569:20;19616:95;19633:12;19647:25;19662:9;19647:14;:25::i;:::-;-1:-1:-1;;;;;19616:95:0;19674:22;19686:9;19674:11;:22::i;:::-;19698:12;19616:16;:95::i;:::-;19601:110;;19721:61;19728:9;19739:10;19751:2;19755:12;19769;19721:6;:61::i;23870:840::-;24072:32;24106:24;24202:13;24219:1;24202:18;24198:37;;-1:-1:-1;24230:1:0;;-1:-1:-1;24230:1:0;24222:13;;24198:37;24285:91;24328:13;24343:15;24360;24285:42;:91::i;:::-;24246:130;-1:-1:-1;24406:40:0;24246:130;24406:13;:40;:::i;:::-;24387:59;;24456:21;24480:133;24510:16;24528:31;24543:15;24528:14;:31::i;:::-;-1:-1:-1;;;;;24480:133:0;24561:28;24573:15;24561:11;:28::i;:::-;24591:12;24480:16;:133::i;:::-;24456:157;;24623:80;24630:15;24647:10;24659;24671:16;24689:13;24623:6;:80::i;:::-;24132:578;23870:840;;;;;;;;;:::o;34550:165::-;34635:73;;-1:-1:-1;;;34635:73:0;;;;;15730:25:193;;;15771:18;;;15764:34;;;34645:10:0;;34635:45;;15703:18:193;;34635:73:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;34721:245;34851:9;34841:7;:19;:42;;;;34874:9;34864:7;:19;34841:42;34837:123;;;34906:43;;-1:-1:-1;;;34906:43:0;;;;;;;;;;;22368:1332:38;22462:21;22486:17;22493:9;22486:6;:17::i;:::-;:37;;-1:-1:-1;;;22486:37:38;;-1:-1:-1;;;;;7322:32:193;;;22486:37:38;;;7304:51:193;22486:27:38;;;;;;;7277:18:193;;22486:37:38;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;22462:61;;22615:21;22651:96;22668:13;22683:25;22698:9;22683:14;:25::i;:::-;-1:-1:-1;;;;;22651:96:38;22710:22;22722:9;22710:11;:22::i;:::-;22734:12;22651:16;:96::i;:::-;22615:132;;22758:72;22765:9;22784:4;22791:8;22801:13;22816;22758:6;:72::i;:::-;342:1:19;22844:9:38;:21;22840:776;;22953:33;22972:13;22953:18;:33::i;:::-;22929:9;:57;;-1:-1:-1;;;;;22929:57:38;;;;;247:1:19;;22929:57:38;;;;-1:-1:-1;;;;;22929:57:38;;:::i;:::-;;;;;;;;-1:-1:-1;;;;;22929:57:38;;;;;-1:-1:-1;;;;;22929:57:38;;;;;;22840:776;;;23017:31;23098:7;23051:44;23066:28;443:1:19;23066:9:38;:28;:::i;23051:44::-;-1:-1:-1;;;;;23051:54:38;;;;;:::i;:::-;23017:88;;23119:20;23142:70;23157:13;23172:7;23181:23;23206:5;23142:14;:70::i;:::-;23119:93;;373:1:19;23231:9:38;:21;23227:379;;23290:32;23309:12;23290:18;:32::i;:::-;23272:14;:50;;:14;;:50;;;;-1:-1:-1;;;;;23272:50:38;;:::i;:::-;;;;;;;;-1:-1:-1;;;;;23272:50:38;;;;;-1:-1:-1;;;;;23272:50:38;;;;;;23364:48;23399:12;23383:13;:28;;;;:::i;:::-;23364:18;:48::i;:::-;23340:9;:72;;-1:-1:-1;;;;;23340:72:38;;;;;:20;;:72;;;;-1:-1:-1;;;23340:72:38;;-1:-1:-1;;;;;23340:72:38;;:::i;:::-;;;;;;;;-1:-1:-1;;;;;23340:72:38;;;;;-1:-1:-1;;;;;23340:72:38;;;;;;23227:379;;;23469:32;23488:12;23469:18;:32::i;:::-;23451:14;:50;;:14;;:50;;;;-1:-1:-1;;;23451:50:38;;-1:-1:-1;;;;;23451:50:38;;:::i;:::-;;;;;;;;-1:-1:-1;;;;;23451:50:38;;;;;-1:-1:-1;;;;;23451:50:38;;;;;;23543:48;23578:12;23562:13;:28;;;;:::i;23543:48::-;23519:20;:72;;-1:-1:-1;;;;;23519:72:38;;;;;:20;;:72;;;;-1:-1:-1;;;;;23519:72:38;;:::i;:::-;;;;;;;;-1:-1:-1;;;;;23519:72:38;;;;;-1:-1:-1;;;;;23519:72:38;;;;;;23227:379;23003:613;;22840:776;23631:62;;;15730:25:193;;;15786:2;15771:18;;15764:34;;;23653:9:38;;-1:-1:-1;;;;;23631:62:38;;;;;15703:18:193;23631:62:38;;;;;;;22452:1248;;22368:1332;;;:::o;39207:534:0:-;39459:85;39479:8;39489:2;39493:32;247:1:19;39538:5:0;39459:19;:85::i;:::-;39554;39574:8;39584:2;39588:32;279:1:19;39633:5:0;39554:19;:85::i;:::-;39649;39669:8;39679:2;39683:32;311:1:19;39728:5:0;40123:1321;40368:43;;40404:7;40368:43;40653:20;40676:17;40683:9;40676:6;:17::i;:::-;40729:21;;-1:-1:-1;;;40729:21:0;;-1:-1:-1;;;;;7322:32:193;;;40729:21:0;;;7304:51:193;40653:40:0;;-1:-1:-1;40703:23:0;;40729:15;;;;;7277:18:193;;40729:21:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;40703:47;;40760:20;40783:22;40795:9;40783:11;:22::i;:::-;40760:45;;40815:20;40838:25;40853:9;40838:14;:25::i;:::-;-1:-1:-1;;;;;40815:48:0;;;40873:22;40898:84;40915:25;40942:12;40956;475:4:19;40898:16:0;:84::i;:::-;40873:109;;40992:5;-1:-1:-1;;;;;40992:19:0;;41012:4;41018:2;41022:41;41031:15;41048:14;41022:8;:41::i;:::-;40992:72;;-1:-1:-1;;;;;;40992:72:0;;;;;;;-1:-1:-1;;;;;41846:32:193;;;40992:72:0;;;41828:51:193;41915:32;;;;41895:18;;;41888:60;41964:18;;;41957:34;41801:18;;40992:72:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;41079:9;:45;;;;;41110:14;41092:15;:32;41079:45;41075:363;;;41140:18;41161:32;41179:14;41161:15;:32;:::i;:::-;41140:53;;41208:17;41215:9;41208:6;:17::i;:::-;:64;;-1:-1:-1;;;41208:64:0;;-1:-1:-1;;;;;41846:32:193;;;41208:64:0;;;41828:51:193;41254:4:0;41895:18:193;;;41888:60;41964:18;;;41957:34;;;41208:31:0;;;;;;;41801:18:193;;41208:64:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;41286:141;41310:9;41321:2;41325:4;41331:70;41348:10;41360:12;41374;475:4:19;41388:12:0;41331:16;:70::i;:::-;41403:10;41286:6;:141::i;:::-;41126:312;40302:1142;;;;;40123:1321;;;;;:::o;42624:610::-;42797:23;42861:21;4401:3:30;42861:14:0;:21;:::i;:::-;42836:22;1481:2;42836:13;:22;:::i;:::-;:46;42832:396;;;42934:14;42919:12;:29;:134;;;-1:-1:-1;4401:3:30;43016:29:0;43033:12;43016:14;:29;:::i;:::-;43015:38;;;;:::i;:::-;1481:2;42973:28;42989:12;42973:13;:28;:::i;:::-;42972:39;;;;:::i;:::-;:81;;42919:134;42898:320;;;43104:30;43120:14;43104:13;:30;:::i;42898:320::-;-1:-1:-1;43191:12:0;;42624:610;-1:-1:-1;;42624:610:0:o;1776:194:79:-;1881:10;1875:4;1868:24;1918:4;1912;1905:18;1949:4;1943;1936:18;6081:2078:26;6164:19;6233:7;6243:3;6233:13;6250:1;6233:18;:93;;-1:-1:-1;;;6233:93:26;;;-1:-1:-1;;;6233:93:26;6219:107;;;-1:-1:-1;6354:3:26;6344:13;;:18;6340:95;;-1:-1:-1;;;6379:48:26;6432:3;6378:57;6340:95;6463:3;6453:13;;:18;6449:95;;-1:-1:-1;;;6488:48:26;6541:3;6487:57;6449:95;6572:3;6562:13;;:18;6558:95;;6611:34;6597:48;6650:3;6596:57;6558:95;6681:4;6671:14;;:19;6667:129;;6739:34;6725:48;6778:3;6724:57;6667:129;6823:4;6813:14;;:19;6809:129;;6881:34;6867:48;6920:3;6866:57;6809:129;6965:4;6955:14;;:19;6951:129;;7023:34;7009:48;7062:3;7008:57;6951:129;7107:4;7097:14;;:19;7093:129;;7165:34;7151:48;7204:3;7150:57;7093:129;7249:5;7239:15;;:20;7235:130;;7308:34;7294:48;7347:3;7293:57;7235:130;7392:5;7382:15;;:20;7378:130;;7451:34;7437:48;7490:3;7436:57;7378:130;7535:5;7525:15;;:20;7521:130;;7594:34;7580:48;7633:3;7579:57;7521:130;7678:5;7668:15;;:20;7664:129;;7737:33;7723:47;7775:3;7722:56;7664:129;7820:6;7810:16;;:21;7806:129;;7880:32;7866:46;7917:3;7865:55;7806:129;7962:6;7952:16;;:21;7948:93;;8004:29;7990:43;8038:3;7989:52;7948:93;8069:6;8059:16;;:21;8055:87;;8111:23;8097:37;8139:3;8096:46;6081:2078;;;:::o;22155:207:38:-;22285:7;22311:44;22321:33;22339:15;22321;:33;:::i;15661:621::-;15763:61;;-1:-1:-1;;;15763:61:38;;15742:18;15763:61;;;18026:41:193;;;15742:18:38;15763:31;-1:-1:-1;;;;;15763:54:38;;;;17999:18:193;;15763:61:38;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;15944:19;;:9;15921:20;15742:82;;;;;;-1:-1:-1;15835:20:38;;;;15881:302;;15921:42;;-1:-1:-1;;;;;;;;15944:19:38;;;;;;15921:20;:42;:::i;:::-;-1:-1:-1;;;;;15881:302:38;16093:7;16079:11;:21;:93;;16167:1;16162;16138:21;16152:7;16138:11;:21;:::i;:::-;:25;;;;:::i;:::-;16137:31;;;;:::i;:::-;:35;;16171:1;16137:35;:::i;:::-;15881:26;:302::i;16079:93::-;16133:1;16122:7;16104:15;:11;16118:1;16104:15;:::i;:::-;:25;;;;:::i;:::-;16103:31;;;;:::i;15881:302::-;15834:349;;;;16234:41;16248:12;16262;16234:13;:41::i;8368:1235:76:-;8595:4;8589:11;-1:-1:-1;;;8462:12:76;8613:22;;;-1:-1:-1;;;;;8661:24:76;;8655:4;8648:38;8706:4;8699:19;;;8462:12;8776:4;8462:12;8767:4;8462:12;;8754:5;8747;8742:39;8731:50;;8993:1;8986:4;8980:11;8977:18;8968:7;8964:32;8954:603;;9125:6;9115:7;9108:15;9104:28;9101:162;;;9178:16;9175:1;9170:3;9155:40;9228:16;9223:3;9216:29;9101:162;9539:1;9531:5;9519:18;9516:25;9497:16;9490:24;9486:56;9477:7;9473:70;9462:81;;8954:603;9577:4;9570:17;-1:-1:-1;8368:1235:76;;-1:-1:-1;;;;8368:1235:76:o;3908:540:29:-;4131:22;;:33;4094;;;;:70;4090:106;;4173:23;;-1:-1:-1;;;4173:23:29;;;;;;;;;;;4090:106;4259:22;;:33;;;;4306;;;;4353:32;;;;4399;;;;;4207:234;;4353:32;4207:38;:234::i;:::-;3908:540;:::o;3450:452::-;3546:36;-1:-1:-1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3546:36:29;3628:22;;:33;3594:31;;;:67;3750:27;3628:22;3750:14;:27::i;:::-;3710:36;;;3671:106;3672:36;;;3671:106;3868:27;3883:11;3868:14;:27::i;:::-;3827:37;;;3787:108;;;3788:14;3450:452;-1:-1:-1;3450:452:29:o;4454:240::-;4601:47;4610:14;4626:21;4601:8;:47::i;:::-;;;4658:29;4672:14;4658:13;:29::i;1452:464:26:-;1529:22;-1:-1:-1;;1567:15:26;;;;;;:34;;-1:-1:-1;1586:15:26;;;;1234:6;1586:15;1567:34;1563:64;;;1610:17;;-1:-1:-1;;;1610:17:26;;;;;;;;;;;1563:64;1655:12;;;;1638:14;1703:11;;;:32;;1728:7;1703:32;;;1717:8;1718:7;1717:8;:::i;:::-;1677:59;;1797:2;1764:29;1785:7;1764:20;:29::i;:::-;:35;;1747:52;;1835:4;1831:8;;:1;:8;1827:65;;;1858:34;1878:14;1858:17;:34;:::i;1027:550:89:-;1088:12;;-1:-1:-1;;1471:1:89;1468;1461:20;1501:9;;;;1549:11;;;1535:12;;;;1531:30;;;;;1027:550;-1:-1:-1;;1027:550:89:o;20404:635:38:-;20536:31;20579;20685:70;20722:15;20739;20685:36;:70::i;:::-;20765:20;:55;;-1:-1:-1;;;;;;20765:55:38;-1:-1:-1;;;;;20765:55:38;;;;;20620:135;-1:-1:-1;20765:55:38;-1:-1:-1;;20864:25:38;-1:-1:-1;20864:14:38;:25::i;:::-;-1:-1:-1;;;;;20831:58:38;;;20899:21;20923:24;342:1:19;20923:14:38;:24::i;:::-;-1:-1:-1;;;;;20899:48:38;;-1:-1:-1;20993:38:38;20899:48;20993:22;:38;:::i;:::-;20957:25;:75;;-1:-1:-1;;;;;20957:75:38;;;-1:-1:-1;;;20957:75:38;;;;;;;;;-1:-1:-1;20404:635:38;;;-1:-1:-1;;;;20404:635:38:o;16288:393::-;16391:15;;16387:288;;16422:242;16446:9;16481:4;16504:5;16527:11;16556:94;16573:11;16586:25;16601:9;16586:14;:25::i;:::-;-1:-1:-1;;;;;16556:94:38;16613:22;16625:9;16613:11;:22::i;16556:94::-;16422:6;:242::i;4103:1402:20:-;4924:28;;;4412:23;4401:3:30;4998:21:20;;335:2;4970:24;;:49;4966:256;;5057:59;5072:15;5089:13;4401:3:30;5110:5:20;5057:14;:59::i;:::-;5039:77;;4966:256;;;5192:14;5174:15;:32;5155:52;;4966:256;5362:21;5253:90;5260:15;5277:21;5316:14;5300:13;:30;5332:10;5253:6;:90::i;:::-;:130;5235:148;;5415:73;5424:15;5441:16;5459;5477:10;5415:8;:73::i;:::-;5397:91;;4103:1402;;;;;;;;;;:::o;4281:853:22:-;4408:22;4432;4518:43;4564:45;4592:16;4564:27;:45::i;:::-;4518:91;;4619:43;4677:72;4692:35;1283:21:30;-1:-1:-1;;;4744:4:22;4677:14;:72::i;:::-;4619:130;;4832:86;4847:21;4870:35;-1:-1:-1;;;4912:5:22;4832:14;:86::i;:::-;4815:103;;4981:86;4996:21;-1:-1:-1;;;5024:35:22;5061:5;4981:14;:86::i;:::-;4964:103;;5088:39;;4281:853;;;;;:::o;9114:996:29:-;9302:22;;:32;;;;9415;;;;;9302;;9415:36;9411:342;;9532:22;;9492:250;;373:1:19;9532:32:29;;;;9582:11;:29;;;9668:11;:38;;;9724:4;9492:22;:250::i;:::-;9467:275;;;;:::i;:::-;;;9411:342;9766:22;;:32;;;:36;9762:342;;9883:22;;9843:250;;404:1:19;9883:32:29;;;;9933:11;:29;;;10019:11;:38;;;10075:4;9843:22;:250::i;:::-;9818:275;;;;:::i;:::-;;;9762:342;9114:996;;;:::o;8096:1012::-;8287:22;;:33;;;8407;;;;8287;;8403:37;8399:347;;8522:22;;8482:253;;279:1:19;8522:33:29;;;;8573:11;:29;;;8660:11;:38;;;8716:5;8482:22;:253::i;:::-;8456:279;;;;:::i;:::-;;;8399:347;8763:22;;:33;;;8759:37;8755:347;;8878:22;;8838:253;;311:1:19;8878:33:29;;;;8929:11;:29;;;9016:11;:38;;;9072:5;8838:22;:253::i;14658:1090::-;14789:27;14818:33;14867:14;:36;;;14907:1;14867:41;:86;;;;-1:-1:-1;14912:36:29;;;;:41;14867:86;14863:105;;;-1:-1:-1;14963:1:29;;-1:-1:-1;14963:1:29;14955:13;;14863:105;15031:37;15053:14;15031:21;:37::i;:::-;-1:-1:-1;14979:89:29;;-1:-1:-1;14979:89:29;-1:-1:-1;15082:30:29;;:57;;;;;15138:1;15116:19;:23;15082:57;15078:131;;;15162:36;;-1:-1:-1;;;15162:36:29;;;;;;;;;;;15078:131;15522:97;15542:19;15587:14;:31;;;15563:21;:55;15522:19;:97::i;:::-;15484:135;;4401:3:30;15683:19:29;:26;3784:2:30;15638:25:29;:42;:71;15634:97;;;15718:13;;-1:-1:-1;;;15718:13:29;;;;;;;;;;;18782:684;18971:37;;;;18931;;19085:36;;;;19046;;;;18931:77;;;;;19046:75;19140:17;;19136:314;;19221:13;19202:16;:32;:142;;;;19328:16;4008:3:30;19282:13:29;19263:16;:32;19262:63;:82;19202:142;19177:259;;;19392:25;;-1:-1:-1;;;19392:25:29;;;;;;;;;;;10973:428;11157:21;11194:15;11213:1;11194:20;11190:34;;-1:-1:-1;11223:1:29;11216:8;;11190:34;11250:144;11278:64;11293:15;-1:-1:-1;;;11315:17:29;11334:7;11278:14;:64::i;:::-;-1:-1:-1;;;11349:26:29;11377:7;11250:14;:144::i;12472:393::-;12674:23;12713:15;12732:1;12713:20;12709:34;;-1:-1:-1;12742:1:29;12735:8;;13211:1441;13478:36;;;;13438:37;;13596:36;;;;13556:37;;;;13317:27;;;;;;13438:76;-1:-1:-1;13438:76:29;13556;13647:25;;13438:76;13647:54;;;13677:24;13676:25;13647:54;13643:1003;;;13906:14;:37;;;13867:14;:36;;;:76;13806:14;:37;;;13767:14;:36;;;:76;:177;13745:199;;13643:1003;;;13980:24;13975:671;;14109:14;:37;;;14070:14;:36;;;:76;14048:98;;14232:14;:36;;;14192:14;:37;;;:76;14164:104;;14307:4;14296:15;;13975:671;;;14333:24;14328:318;;14462:14;:37;;;14423:14;:36;;;:76;14401:98;;14585:14;:36;;;14545:14;:37;;;:76;14517:104;;14328:318;13396:1256;;13211:1441;;;;;:::o;18393:383::-;18527:7;18573:21;18550:19;:44;18546:103;;18617:21;;-1:-1:-1;;;18617:21:29;;;;;;;;;;;18546:103;18665:104;18678:43;18702:19;18678:21;:43;:::i;:::-;18724;18748:19;18724:21;:43;:::i;:::-;18665:12;:104::i;-1:-1:-1:-;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;:::o;:::-;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;;;;:::o;14:513:193:-;194:3;179:19;;183:9;275:6;152:4;309:212;323:4;320:1;317:11;309:212;;;386:13;;-1:-1:-1;;;;;382:54:193;370:67;;466:4;457:14;;;;494:17;;;;343:1;336:9;309:212;;;313:3;;;14:513;;;;:::o;532:131::-;-1:-1:-1;;;;;607:31:193;;597:42;;587:70;;653:1;650;643:12;668:347;719:8;729:6;783:3;776:4;768:6;764:17;760:27;750:55;;801:1;798;791:12;750:55;-1:-1:-1;824:20:193;;867:18;856:30;;853:50;;;899:1;896;889:12;853:50;936:4;928:6;924:17;912:29;;988:3;981:4;972:6;964;960:19;956:30;953:39;950:59;;;1005:1;1002;995:12;1020:785;1117:6;1125;1133;1141;1149;1202:3;1190:9;1181:7;1177:23;1173:33;1170:53;;;1219:1;1216;1209:12;1170:53;1264:23;;;-1:-1:-1;1384:2:193;1369:18;;1356:32;;-1:-1:-1;1466:2:193;1451:18;;1438:32;1479:33;1438:32;1479:33;:::i;:::-;1531:7;-1:-1:-1;1589:2:193;1574:18;;1561:32;1616:18;1605:30;;1602:50;;;1648:1;1645;1638:12;1602:50;1687:58;1737:7;1728:6;1717:9;1713:22;1687:58;:::i;:::-;1020:785;;;;-1:-1:-1;1020:785:193;;-1:-1:-1;1764:8:193;;1661:84;1020:785;-1:-1:-1;;;1020:785:193:o;1810:118::-;1896:5;1889:13;1882:21;1875:5;1872:32;1862:60;;1918:1;1915;1908:12;1933:523;2007:6;2015;2023;2076:2;2064:9;2055:7;2051:23;2047:32;2044:52;;;2092:1;2089;2082:12;2044:52;2131:9;2118:23;2150:31;2175:5;2150:31;:::i;:::-;2200:5;-1:-1:-1;2257:2:193;2242:18;;2229:32;2270:33;2229:32;2270:33;:::i;:::-;2322:7;-1:-1:-1;2381:2:193;2366:18;;2353:32;2394:30;2353:32;2394:30;:::i;:::-;2443:7;2433:17;;;1933:523;;;;;:::o;3572:118::-;3659:5;3656:1;3645:20;3638:5;3635:31;3625:59;;3680:1;3677;3670:12;3695:243;3752:6;3805:2;3793:9;3784:7;3780:23;3776:32;3773:52;;;3821:1;3818;3811:12;3773:52;3860:9;3847:23;3879:29;3902:5;3879:29;:::i;4162:637::-;4352:2;4364:21;;;4434:13;;4337:18;;;4456:22;;;4304:4;;4535:15;;;4509:2;4494:18;;;4304:4;4578:195;4592:6;4589:1;4586:13;4578:195;;;4657:13;;-1:-1:-1;;;;;4653:39:193;4641:52;;4722:2;4748:15;;;;4713:12;;;;4689:1;4607:9;4578:195;;;-1:-1:-1;4790:3:193;;4162:637;-1:-1:-1;;;;;4162:637:193:o;4804:289::-;4846:3;4884:5;4878:12;4911:6;4906:3;4899:19;4967:6;4960:4;4953:5;4949:16;4942:4;4937:3;4933:14;4927:47;5019:1;5012:4;5003:6;4998:3;4994:16;4990:27;4983:38;5082:4;5075:2;5071:7;5066:2;5058:6;5054:15;5050:29;5045:3;5041:39;5037:50;5030:57;;;4804:289;;;;:::o;5098:579::-;5150:3;5181;5213:5;5207:12;5240:6;5235:3;5228:19;5272:4;5267:3;5263:14;5256:21;;5330:4;5320:6;5317:1;5313:14;5306:5;5302:26;5298:37;5369:4;5362:5;5358:16;5392:1;5402:249;5416:6;5413:1;5410:13;5402:249;;;5503:2;5499:7;5491:5;5485:4;5481:16;5477:30;5472:3;5465:43;5529:38;5562:4;5553:6;5547:13;5529:38;:::i;:::-;5602:4;5627:14;;;;5521:46;;-1:-1:-1;5590:17:193;;;;;5438:1;5431:9;5402:249;;;-1:-1:-1;5667:4:193;;5098:579;-1:-1:-1;;;;;;5098:579:193:o;5682:1035::-;5888:4;5936:2;5925:9;5921:18;5966:2;5955:9;5948:21;5989:6;6024;6018:13;6055:6;6047;6040:22;6093:2;6082:9;6078:18;6071:25;;6155:2;6145:6;6142:1;6138:14;6127:9;6123:30;6119:39;6105:53;;6193:2;6185:6;6181:15;6214:1;6224:464;6238:6;6235:1;6232:13;6224:464;;;6303:22;;;-1:-1:-1;;6299:36:193;6287:49;;6359:13;;6404:9;;-1:-1:-1;;;;;6400:35:193;6385:51;;6483:2;6475:11;;;6469:18;6524:2;6507:15;;;6500:27;;;6469:18;6550:58;;6592:15;;6469:18;6550:58;:::i;:::-;6540:68;-1:-1:-1;;6643:2:193;6666:12;;;;6631:15;;;;;6260:1;6253:9;6224:464;;;-1:-1:-1;6705:6:193;;5682:1035;-1:-1:-1;;;;;;5682:1035:193:o;6722:226::-;6781:6;6834:2;6822:9;6813:7;6809:23;6805:32;6802:52;;;6850:1;6847;6840:12;6802:52;-1:-1:-1;6895:23:193;;6722:226;-1:-1:-1;6722:226:193:o;7366:247::-;7425:6;7478:2;7466:9;7457:7;7453:23;7449:32;7446:52;;;7494:1;7491;7484:12;7446:52;7533:9;7520:23;7552:31;7577:5;7552:31;:::i;7987:327::-;8089:5;8112:1;8122:186;8136:4;8133:1;8130:11;8122:186;;;8209:13;;8206:1;8195:28;8183:41;;8253:4;8244:14;;;;8281:17;;;;8156:1;8149:9;8122:186;;8319:316;8410:5;8433:1;8443:186;8457:4;8454:1;8451:11;8443:186;;;8530:13;;8527:1;8516:28;8504:41;;8574:4;8565:14;;;;8602:17;;;;8477:1;8470:9;8443:186;;8640:330;8743:5;8766:1;8776:188;8790:4;8787:1;8784:11;8776:188;;;8853:13;;8868:10;8849:30;8837:43;;8909:4;8900:14;;;;8937:17;;;;8810:1;8803:9;8776:188;;8975:319;9067:5;9090:1;9100:188;9114:4;9111:1;9108:11;9100:188;;;9177:13;;9192:10;9173:30;9161:43;;9233:4;9224:14;;;;9261:17;;;;9134:1;9127:9;9100:188;;9299:1740;9523:13;;7688;7681:21;7669:34;;9493:4;9478:20;;9595:4;9587:6;9583:17;9577:24;9610:51;9655:4;9644:9;9640:20;9626:12;7688:13;7681:21;7669:34;;7618:91;9610:51;;9710:4;9702:6;9698:17;9692:24;9725:54;9773:4;9762:9;9758:20;9742:14;7781:4;7770:16;7758:29;;7714:75;9725:54;;9828:4;9820:6;9816:17;9810:24;9843:54;9891:4;9880:9;9876:20;9860:14;7781:4;7770:16;7758:29;;7714:75;9843:54;;9946:4;9938:6;9934:17;9928:24;9961:54;10009:4;9998:9;9994:20;9978:14;3276:1;3265:20;3253:33;;3201:91;9961:54;;10064:4;10056:6;10052:17;10046:24;10079:54;10127:4;10116:9;10112:20;10096:14;3276:1;3265:20;3253:33;;3201:91;10079:54;;10182:4;10174:6;10170:17;10164:24;10197:55;10246:4;10235:9;10231:20;10215:14;7870:8;7859:20;7847:33;;7794:92;10197:55;;10301:4;10293:6;10289:17;10283:24;10316:55;10365:4;10354:9;10350:20;10334:14;7870:8;7859:20;7847:33;;7794:92;10316:55;;10420:6;10412;10408:19;10402:26;10437:56;10485:6;10474:9;10470:22;10454:14;7966:1;7955:20;7943:33;;7891:91;10437:56;;10542:6;10534;10530:19;10524:26;10559:73;10624:6;10613:9;10609:22;10593:14;10559:73;:::i;:::-;;10681:6;10673;10669:19;10663:26;10698:62;10752:6;10741:9;10737:22;10721:14;10698:62;:::i;:::-;;10810:6;10802;10798:19;10792:26;10827:75;10894:6;10883:9;10879:22;10862:15;10827:75;:::i;:::-;;10952:6;10944;10940:19;10934:26;10969:64;11025:6;11014:9;11010:22;10993:15;10969:64;:::i;11044:118::-;11131:5;11128:1;11117:20;11110:5;11107:31;11097:59;;11152:1;11149;11142:12;11167:363;11233:6;11241;11294:2;11282:9;11273:7;11269:23;11265:32;11262:52;;;11310:1;11307;11300:12;11262:52;11349:9;11336:23;11368:29;11391:5;11368:29;:::i;:::-;11416:5;11494:2;11479:18;;;;11466:32;;-1:-1:-1;;;11167:363:193:o;11728:446::-;11780:3;11818:5;11812:12;11845:6;11840:3;11833:19;11877:4;11872:3;11868:14;11861:21;;11916:4;11909:5;11905:16;11939:1;11949:200;11963:6;11960:1;11957:13;11949:200;;;12028:13;;-1:-1:-1;;;;;;12024:40:193;12012:53;;12094:4;12085:14;;;;12122:17;;;;11985:1;11978:9;11949:200;;;-1:-1:-1;12165:3:193;;11728:446;-1:-1:-1;;;;11728:446:193:o;12179:1145::-;12399:4;12447:2;12436:9;12432:18;12477:2;12466:9;12459:21;12500:6;12535;12529:13;12566:6;12558;12551:22;12604:2;12593:9;12589:18;12582:25;;12666:2;12656:6;12653:1;12649:14;12638:9;12634:30;12630:39;12616:53;;12704:2;12696:6;12692:15;12725:1;12735:560;12749:6;12746:1;12743:13;12735:560;;;12842:2;12838:7;12826:9;12818:6;12814:22;12810:36;12805:3;12798:49;12876:6;12870:13;12922:2;12916:9;12953:2;12945:6;12938:18;12983:48;13027:2;13019:6;13015:15;13001:12;12983:48;:::i;:::-;12969:62;;13080:2;13076;13072:11;13066:18;13044:40;;13133:6;13125;13121:19;13116:2;13108:6;13104:15;13097:44;13164:51;13208:6;13192:14;13164:51;:::i;:::-;13154:61;-1:-1:-1;;;13250:2:193;13273:12;;;;13238:15;;;;;12771:1;12764:9;12735:560;;13329:142;-1:-1:-1;;;;;13408:5:193;13404:42;13397:5;13394:53;13384:81;;13461:1;13458;13451:12;13476:247;13535:6;13588:2;13576:9;13567:7;13563:23;13559:32;13556:52;;;13604:1;13601;13594:12;13556:52;13643:9;13630:23;13662:31;13687:5;13662:31;:::i;13728:346::-;13796:6;13804;13857:2;13845:9;13836:7;13832:23;13828:32;13825:52;;;13873:1;13870;13863:12;13825:52;-1:-1:-1;;13918:23:193;;;14038:2;14023:18;;;14010:32;;-1:-1:-1;13728:346:193:o;14079:146::-;-1:-1:-1;;;;;14158:5:193;14154:46;14147:5;14144:57;14134:85;;14215:1;14212;14205:12;14230:367;14298:6;14306;14359:2;14347:9;14338:7;14334:23;14330:32;14327:52;;;14375:1;14372;14365:12;14327:52;14420:23;;;-1:-1:-1;14519:2:193;14504:18;;14491:32;14532:33;14491:32;14532:33;:::i;:::-;14584:7;14574:17;;;14230:367;;;;;:::o;14602:280::-;14801:2;14790:9;14783:21;14764:4;14821:55;14872:2;14861:9;14857:18;14849:6;14821:55;:::i;14887:664::-;14975:6;14983;14991;14999;15052:2;15040:9;15031:7;15027:23;15023:32;15020:52;;;15068:1;15065;15058:12;15020:52;15107:9;15094:23;15126:31;15151:5;15126:31;:::i;:::-;15176:5;-1:-1:-1;15254:2:193;15239:18;;15226:32;;-1:-1:-1;15335:2:193;15320:18;;15307:32;15362:18;15351:30;;15348:50;;;15394:1;15391;15384:12;15348:50;15433:58;15483:7;15474:6;15463:9;15459:22;15433:58;:::i;:::-;14887:664;;;;-1:-1:-1;15510:8:193;-1:-1:-1;;;;14887:664:193:o;15809:785::-;15906:6;15914;15922;15930;15938;15991:3;15979:9;15970:7;15966:23;15962:33;15959:53;;;16008:1;16005;15998:12;15959:53;16047:9;16034:23;16066:31;16091:5;16066:31;:::i;:::-;16116:5;-1:-1:-1;16194:2:193;16179:18;;16166:32;;-1:-1:-1;16297:2:193;16282:18;;16269:32;;-1:-1:-1;16378:2:193;16363:18;;16350:32;16405:18;16394:30;;16391:50;;;16437:1;16434;16427:12;16599:1033;16803:4;16851:2;16840:9;16836:18;16881:2;16870:9;16863:21;16904:6;16939;16933:13;16970:6;16962;16955:22;17008:2;16997:9;16993:18;16986:25;;17070:2;17060:6;17057:1;17053:14;17042:9;17038:30;17034:39;17020:53;;17108:2;17100:6;17096:15;17129:1;17139:464;17153:6;17150:1;17147:13;17139:464;;;17218:22;;;-1:-1:-1;;17214:36:193;17202:49;;17274:13;;17319:9;;-1:-1:-1;;;;;17315:35:193;17300:51;;17398:2;17390:11;;;17384:18;17439:2;17422:15;;;17415:27;;;17384:18;17465:58;;17507:15;;17384:18;17465:58;:::i;:::-;17455:68;-1:-1:-1;;17558:2:193;17581:12;;;;17546:15;;;;;17175:1;17168:9;17139:464;;18415:367;18483:6;18491;18544:2;18532:9;18523:7;18519:23;18515:32;18512:52;;;18560:1;18557;18550:12;18512:52;18605:23;;;-1:-1:-1;18704:2:193;18689:18;;18676:32;18717:33;18676:32;18717:33;:::i;19111:1355::-;19251:6;19259;19267;19275;19283;19291;19299;19307;19315;19323;19376:3;19364:9;19355:7;19351:23;19347:33;19344:53;;;19393:1;19390;19383:12;19344:53;19432:9;19419:23;19451:31;19476:5;19451:31;:::i;:::-;19501:5;-1:-1:-1;19558:2:193;19543:18;;19530:32;19571:33;19530:32;19571:33;:::i;:::-;19111:1355;;19623:7;;-1:-1:-1;;;;19703:2:193;19688:18;;19675:32;;19806:2;19791:18;;19778:32;;19909:3;19894:19;;19881:33;;-1:-1:-1;20013:3:193;19998:19;;19985:33;;-1:-1:-1;20117:3:193;20102:19;;20089:33;;-1:-1:-1;20221:3:193;20206:19;;20193:33;;-1:-1:-1;20325:3:193;20310:19;;20297:33;;-1:-1:-1;20429:3:193;20414:19;;;20401:33;;-1:-1:-1;19111:1355:193:o;20471:303::-;20564:5;20587:1;20597:171;20611:4;20608:1;20605:11;20597:171;;;20670:13;;20658:26;;20713:4;20704:14;;;;20741:17;;;;20631:1;20624:9;20597:171;;20779:242;20959:3;20944:19;;20972:43;20948:9;20997:6;20972:43;:::i;21026:134::-;21103:13;;21125:29;21103:13;21125:29;:::i;21165:247::-;21233:6;21286:2;21274:9;21265:7;21261:23;21257:32;21254:52;;;21302:1;21299;21292:12;21254:52;21334:9;21328:16;21353:29;21376:5;21353:29;:::i;21782:360::-;21859:6;21867;21920:2;21908:9;21899:7;21895:23;21891:32;21888:52;;;21936:1;21933;21926:12;21888:52;21968:9;21962:16;21987:29;22010:5;21987:29;:::i;:::-;22106:2;22091:18;;;;22085:25;22035:5;;22085:25;;-1:-1:-1;;;21782:360:193:o;22147:678::-;22239:5;22233:12;22228:3;22221:25;22309:4;22302:5;22298:16;22292:23;22289:1;22278:38;22271:4;22266:3;22262:14;22255:62;22366:4;22359:5;22355:16;22349:23;22342:4;22337:3;22333:14;22326:47;22419:4;22412:5;22408:16;22402:23;22456:4;22451:3;22447:14;22537:1;22547:214;22561:4;22558:1;22555:11;22547:214;;;22626:13;;-1:-1:-1;;;;;22622:50:193;22608:65;;22706:4;22734:17;;;;22695:16;;;;22581:1;22574:9;22547:214;;;-1:-1:-1;;;22812:4:193;22801:16;22795:23;22786:6;22777:16;;;;22770:49;22147:678::o;22830:857::-;23119:3;23104:19;;23108:9;23200:6;23077:4;23244:361;23284:4;23280:1;23267:11;23263:19;23260:29;23244:361;;;23391:13;;-1:-1:-1;;;;;23429:45:193;;23417:58;;23515:3;23511:14;23504:4;23495:14;;23488:38;23555:2;23546:12;;;;23593:1;23581:14;;;;23344:1;23327:19;23244:361;;;23248:3;;;23614:67;23676:3;23665:9;23661:19;23653:6;23614:67;:::i;23692:127::-;23753:10;23748:3;23744:20;23741:1;23734:31;23784:4;23781:1;23774:15;23808:4;23805:1;23798:15;23824:255;23896:2;23890:9;23938:6;23926:19;;23975:18;23960:34;;23996:22;;;23957:62;23954:88;;;24022:18;;:::i;:::-;24058:2;24051:22;23824:255;:::o;24084:252::-;24156:2;24150:9;24198:3;24186:16;;24232:18;24217:34;;24253:22;;;24214:62;24211:88;;;24279:18;;:::i;24341:275::-;24412:2;24406:9;24477:2;24458:13;;-1:-1:-1;;24454:27:193;24442:40;;24512:18;24497:34;;24533:22;;;24494:62;24491:88;;;24559:18;;:::i;:::-;24595:2;24588:22;24341:275;;-1:-1:-1;24341:275:193:o;24621:620::-;24682:5;24735:3;24728:4;24720:6;24716:17;24712:27;24702:55;;24753:1;24750;24743:12;24702:55;24854:19;24832:2;24854:19;:::i;:::-;24897:3;24935:2;24927:6;24923:15;24961:3;24953:6;24950:15;24947:35;;;24978:1;24975;24968:12;24947:35;25002:6;25017:193;25033:6;25028:3;25025:15;25017:193;;;25125:10;;25148:18;;25195:4;25186:14;;;;25050;25017:193;;25246:1092;25389:6;25397;25405;25413;25466:3;25454:9;25445:7;25441:23;25437:33;25434:53;;;25483:1;25480;25473:12;25434:53;25532:7;25525:4;25514:9;25510:20;25506:34;25496:62;;25554:1;25551;25544:12;25496:62;25656:20;25633:3;25656:20;:::i;:::-;25698:3;25739;25728:9;25724:19;25766:7;25758:6;25755:19;25752:39;;;25787:1;25784;25777:12;25752:39;25811:9;25829:214;25845:6;25840:3;25837:15;25829:214;;;25920:3;25914:10;25937:31;25962:5;25937:31;:::i;:::-;25981:18;;26028:4;26019:14;;;;25862;25829:214;;;-1:-1:-1;26112:13:193;26217:3;26202:19;;26196:26;26062:5;;-1:-1:-1;26112:13:193;-1:-1:-1;26196:26:193;-1:-1:-1;26267:65:193;;-1:-1:-1;26324:7:193;26318:3;26303:19;;26267:65;:::i;:::-;26257:75;;25246:1092;;;;;;;:::o;26343:127::-;26404:10;26399:3;26395:20;26392:1;26385:31;26435:4;26432:1;26425:15;26459:4;26456:1;26449:15;26475:128;26542:9;;;26563:11;;;26560:37;;;26577:18;;:::i;26608:266::-;26696:6;26691:3;26684:19;26748:6;26741:5;26734:4;26729:3;26725:14;26712:43;-1:-1:-1;26800:1:193;26775:16;;;26793:4;26771:27;;;26764:38;;;;26856:2;26835:15;;;-1:-1:-1;;26831:29:193;26822:39;;;26818:50;;26608:266::o;26879:485::-;27149:1;27145;27140:3;27136:11;27132:19;27124:6;27120:32;27109:9;27102:51;27189:6;27184:2;27173:9;27169:18;27162:34;27232:6;27227:2;27216:9;27212:18;27205:34;27275:3;27270:2;27259:9;27255:18;27248:31;27083:4;27296:62;27353:3;27342:9;27338:19;27330:6;27322;27296:62;:::i;27369:168::-;27442:9;;;27473;;27490:15;;;27484:22;;27470:37;27460:71;;27511:18;;:::i;27938:125::-;28003:9;;;28024:10;;;28021:36;;;28037:18;;:::i;28068:469::-;28144:43;28183:3;28175:5;28169:12;28144:43;:::i;:::-;28236:4;28229:5;28225:16;28219:23;28212:4;28207:3;28203:14;28196:47;28292:4;28285:5;28281:16;28275:23;28268:4;28263:3;28259:14;28252:47;28350:4;28343:5;28339:16;28333:23;28324:6;28319:3;28315:16;28308:49;28408:4;28401:5;28397:16;28391:23;28382:6;28377:3;28373:16;28366:49;28466:4;28459:5;28455:16;28449:23;28440:6;28435:3;28431:16;28424:49;28524:4;28517:5;28513:16;28507:23;28498:6;28493:3;28489:16;28482:49;;;28068:469::o;28542:370::-;28764:3;28749:19;;28777:59;28753:9;28818:6;28777:59;:::i;:::-;-1:-1:-1;;;;;28873:32:193;;;;28867:3;28852:19;;;;28845:61;28542:370;;-1:-1:-1;28542:370:193:o;28917:377::-;28992:6;29000;29053:2;29041:9;29032:7;29028:23;29024:32;29021:52;;;29069:1;29066;29059:12;29021:52;29101:9;29095:16;29120:29;29143:5;29120:29;:::i;:::-;29218:2;29203:18;;29197:25;29168:5;;-1:-1:-1;29231:31:193;29197:25;29231:31;:::i;29299:366::-;-1:-1:-1;;;;;29509:32:193;;;;29491:51;;29538:1;29578:21;;;;29573:2;29558:18;;29551:49;29643:14;29636:22;29631:2;29616:18;;29609:50;29479:2;29464:18;;29299:366::o;29670:380::-;29749:1;29745:12;;;;29792;;;29813:61;;29867:4;29859:6;29855:17;29845:27;;29813:61;29920:2;29912:6;29909:14;29889:18;29886:38;29883:161;;29966:10;29961:3;29957:20;29954:1;29947:31;30001:4;29998:1;29991:15;30029:4;30026:1;30019:15;29883:161;;29670:380;;;:::o;30055:127::-;30116:10;30111:3;30107:20;30104:1;30097:31;30147:4;30144:1;30137:15;30171:4;30168:1;30161:15;30395:132;30471:13;;30493:28;30471:13;30493:28;:::i;30532:160::-;30609:13;;30662:4;30651:16;;30641:27;;30631:55;;30682:1;30679;30672:12;30697:165;30775:13;;30828:8;30817:20;;30807:31;;30797:59;;30852:1;30849;30842:12;30867:134;30944:13;;30966:29;30944:13;30966:29;:::i;31006:664::-;31076:5;31129:3;31122:4;31114:6;31110:17;31106:27;31096:55;;31147:1;31144;31137:12;31096:55;31175:1;31236:4;31260:21;31236:4;31260:21;:::i;:::-;31249:32;-1:-1:-1;31331:17:193;;31249:32;31360:15;;;31357:35;;;31388:1;31385;31378:12;31357:35;31412:6;31427:212;31443:6;31438:3;31435:15;31427:212;;;31518:3;31512:10;31535:29;31558:5;31535:29;:::i;:::-;31577:18;;31624:4;31615:14;;;;31460;31427:212;;;-1:-1:-1;31657:7:193;;31006:664;-1:-1:-1;;;;;31006:664:193:o;31675:652::-;31734:5;31787:3;31780:4;31772:6;31768:17;31764:27;31754:55;;31805:1;31802;31795:12;31754:55;31833:1;31894:3;31917:21;31894:3;31917:21;:::i;:::-;31906:32;-1:-1:-1;31988:17:193;;31906:32;32017:15;;;32014:35;;;32045:1;32042;32035:12;32014:35;32069:6;32084:212;32100:6;32095:3;32092:15;32084:212;;;32175:3;32169:10;32192:29;32215:5;32192:29;:::i;:::-;32234:18;;32281:4;32272:14;;;;32117;32084:212;;32332:167;32410:13;;32463:10;32452:22;;32442:33;;32432:61;;32489:1;32486;32479:12;32504:615;32575:5;32628:3;32621:4;32613:6;32609:17;32605:27;32595:55;;32646:1;32643;32636:12;32595:55;32674:1;32735:4;32759:21;32735:4;32759:21;:::i;:::-;32748:32;-1:-1:-1;32830:17:193;;32748:32;32859:15;;;32856:35;;;32887:1;32884;32877:12;32856:35;32911:6;32926:162;32942:6;32937:3;32934:15;32926:162;;;33010:33;33039:3;33010:33;:::i;:::-;32998:46;;33073:4;33064:14;;;;32959;32926:162;;33124:603;33184:5;33237:3;33230:4;33222:6;33218:17;33214:27;33204:55;;33255:1;33252;33245:12;33204:55;33283:1;33344:3;33367:21;33344:3;33367:21;:::i;:::-;33356:32;-1:-1:-1;33438:17:193;;33356:32;33467:15;;;33464:35;;;33495:1;33492;33485:12;33464:35;33519:6;33534:162;33550:6;33545:3;33542:15;33534:162;;;33618:33;33647:3;33618:33;:::i;:::-;33606:46;;33681:4;33672:14;;;;33567;33534:162;;33732:1422;33832:6;33892:4;33880:9;33871:7;33867:23;33863:34;33909:2;33906:22;;;33924:1;33921;33914:12;33906:22;-1:-1:-1;33966:22:193;;:::i;:::-;34011:37;34038:9;34011:37;:::i;:::-;34004:5;33997:52;34081:46;34123:2;34112:9;34108:18;34081:46;:::i;:::-;34076:2;34069:5;34065:14;34058:70;34160:47;34203:2;34192:9;34188:18;34160:47;:::i;:::-;34155:2;34148:5;34144:14;34137:71;34240:47;34283:2;34272:9;34268:18;34240:47;:::i;:::-;34235:2;34228:5;34224:14;34217:71;34321:48;34364:3;34353:9;34349:19;34321:48;:::i;:::-;34315:3;34308:5;34304:15;34297:73;34403:48;34446:3;34435:9;34431:19;34403:48;:::i;:::-;34397:3;34390:5;34386:15;34379:73;34485:49;34529:3;34518:9;34514:19;34485:49;:::i;:::-;34479:3;34472:5;34468:15;34461:74;34568:49;34612:3;34601:9;34597:19;34568:49;:::i;:::-;34562:3;34555:5;34551:15;34544:74;34651:48;34694:3;34683:9;34679:19;34651:48;:::i;:::-;34645:3;34638:5;34634:15;34627:73;34733:74;34799:7;34793:3;34782:9;34778:19;34733:74;:::i;:::-;34727:3;34720:5;34716:15;34709:99;34844:64;34900:7;34893:4;34882:9;34878:20;34844:64;:::i;:::-;34835:6;34828:5;34824:18;34817:92;34945:76;35013:7;35006:4;34995:9;34991:20;34945:76;:::i;:::-;34936:6;34929:5;34925:18;34918:104;35058:65;35115:7;35108:4;35097:9;35093:20;35058:65;:::i;:::-;35049:6;35038:18;;35031:93;35042:5;33732:1422;-1:-1:-1;;;33732:1422:193:o;35159:151::-;35249:4;35242:12;;;35228;;;35224:31;;35267:14;;35264:40;;;35284:18;;:::i;35315:210::-;35413:1;35402:16;;;35384;;;;35380:39;-1:-1:-1;;35434:32:193;;35478:16;35468:27;;35431:65;35428:91;;;35499:18;;:::i;35530:127::-;35591:10;35586:3;35582:20;35579:1;35572:31;35622:4;35619:1;35612:15;35646:4;35643:1;35636:15;35662:193;35701:1;35727;35717:35;;35732:18;;:::i;:::-;-1:-1:-1;;;35768:18:193;;-1:-1:-1;;35788:13:193;;35764:38;35761:64;;;35805:18;;:::i;:::-;-1:-1:-1;35839:10:193;;35662:193::o;35860:240::-;-1:-1:-1;;;;;35929:42:193;;;35973;;;35925:91;;36028:43;;36025:69;;;36074:18;;:::i;36105:557::-;36403:1;36399;36394:3;36390:11;36386:19;36378:6;36374:32;36363:9;36356:51;36443:6;36438:2;36427:9;36423:18;36416:34;36486:6;36481:2;36470:9;36466:18;36459:34;36529:6;36524:2;36513:9;36509:18;36502:34;36573:3;36567;36556:9;36552:19;36545:32;36337:4;36594:62;36651:3;36640:9;36636:19;36628:6;36620;36594:62;:::i;:::-;36586:70;36105:557;-1:-1:-1;;;;;;;;36105:557:193:o;36667:629::-;36993:1;36989;36984:3;36980:11;36976:19;36968:6;36964:32;36953:9;36946:51;37033:6;37028:2;37017:9;37013:18;37006:34;37076:6;37071:2;37060:9;37056:18;37049:34;37119:6;37114:2;37103:9;37099:18;37092:34;37163:6;37157:3;37146:9;37142:19;37135:35;37207:3;37201;37190:9;37186:19;37179:32;36927:4;37228:62;37285:3;37274:9;37270:19;37262:6;37254;37228:62;:::i;:::-;37220:70;36667:629;-1:-1:-1;;;;;;;;;36667:629:193:o;37930:184::-;38000:6;38053:2;38041:9;38032:7;38028:23;38024:32;38021:52;;;38069:1;38066;38059:12;38021:52;-1:-1:-1;38092:16:193;;37930:184;-1:-1:-1;37930:184:193:o;38119:243::-;-1:-1:-1;;;;;38234:42:193;;;38190;;;38186:91;;38289:44;;38286:70;;;38336:18;;:::i;39340:410::-;39418:6;39426;39479:2;39467:9;39458:7;39454:23;39450:32;39447:52;;;39495:1;39492;39485:12;39447:52;39527:9;39521:16;39577:6;39570:5;39566:18;39559:5;39556:29;39546:57;;39599:1;39596;39589:12;39546:57;39672:2;39657:18;;39651:25;39622:5;;-1:-1:-1;39685:33:193;39651:25;39685:33;:::i;39755:120::-;39795:1;39821;39811:35;;39826:18;;:::i;:::-;-1:-1:-1;39860:9:193;;39755:120::o;39880:170::-;39977:10;39970:18;;;39950;;;39946:43;;40001:20;;39998:46;;;40024:18;;:::i;41236:385::-;41315:6;41323;41376:2;41364:9;41355:7;41351:23;41347:32;41344:52;;;41392:1;41389;41382:12;41344:52;41424:9;41418:16;41443:31;41468:5;41443:31;:::i;:::-;41543:2;41528:18;;41522:25;41493:5;;-1:-1:-1;41556:33:193;41522:25;41556:33;:::i;42002:251::-;42072:6;42125:2;42113:9;42104:7;42100:23;42096:32;42093:52;;;42141:1;42138;42131:12;42093:52;42173:9;42167:16;42192:31;42217:5;42192:31;:::i;42258:228::-;-1:-1:-1;;;;;42327:38:193;;;42367;;;42323:83;;42418:39;;42415:65;;;42460:18;;:::i;42939:231::-;-1:-1:-1;;;;;43050:38:193;;;43010;;;43006:83;;43101:40;;43098:66;;;43144:18;;:::i;43175:971::-;43531:3;43516:19;;43544:59;43520:9;43585:6;43544:59;:::i;:::-;-1:-1:-1;;;;;43640:32:193;;;;43634:3;43619:19;;43612:61;43710:13;;43704:3;43689:19;;43682:42;43779:4;43767:17;;43761:24;43755:3;43740:19;;43733:53;43841:4;43829:17;;43823:24;43817:3;43802:19;;43795:53;43903:4;43891:17;;43885:24;43879:3;43864:19;;43857:53;43965:4;43953:17;;43947:24;43941:3;43926:19;;43919:53;43660:3;44015:17;;44009:24;44003:3;43988:19;;43981:53;44089:4;44077:17;;;44071:24;44065:3;44050:19;;44043:53;44127:3;44112:19;;;44105:35;43175:971;;-1:-1:-1;43175:971:193:o;44151:245::-;44218:6;44271:2;44259:9;44250:7;44246:23;44242:32;44239:52;;;44287:1;44284;44277:12;44239:52;44319:9;44313:16;44338:28;44360:5;44338:28;:::i;44401:713::-;44476:12;;44510:3;44577:1;44587:177;44601:4;44598:1;44595:11;44587:177;;;44662:13;;44648:28;;44709:4;44737:17;;;;44698:16;;;;44621:1;44614:9;44587:177;;;44591:3;;;44813:4;44806:5;44802:16;44796:23;44789:4;44784:3;44780:14;44773:47;44869:4;44862:5;44858:16;44852:23;44845:4;44840:3;44836:14;44829:47;44927:4;44920:5;44916:16;44910:23;44901:6;44896:3;44892:16;44885:49;44985:4;44978:5;44974:16;44968:23;44959:6;44954:3;44950:16;44943:49;45043:4;45036:5;45032:16;45026:23;45017:6;45012:3;45008:16;45001:49;45101:4;45094:5;45090:16;45084:23;45075:6;45070:3;45066:16;45059:49;44401:713;;:::o;45119:721::-;-1:-1:-1;;;;;45533:32:193;;45515:51;;45502:3;45487:19;;45575:57;45628:2;45613:18;;45605:6;45575:57;:::i;:::-;-1:-1:-1;;;;;45669:32:193;;;;45663:3;45648:19;;45641:61;45733:3;45718:19;;45711:35;;;;45777:3;45762:19;;45755:35;;;;45821:3;45806:19;;;45799:35;45119:721;;-1:-1:-1;;45119:721:193:o;45845:433::-;46091:3;46076:19;;46104:48;46080:9;46134:6;46104:48;:::i;:::-;46196:14;;46189:22;46183:3;46168:19;;46161:51;46256:14;;46249:22;46243:3;46228:19;;;46221:51;45845:433;;-1:-1:-1;45845:433:193:o;46283:1075::-;46397:6;46457:3;46445:9;46436:7;46432:23;46428:33;46473:2;46470:22;;;46488:1;46485;46478:12;46470:22;-1:-1:-1;46530:22:193;;:::i;:::-;46597:16;;46622:22;;46710:2;46695:18;;;46689:25;46730:14;;;46723:31;46820:2;46805:18;;;46799:25;46840:14;;;46833:31;46930:2;46915:18;;;46909:25;46950:14;;;46943:31;47040:3;47025:19;;;47019:26;47061:15;;;47054:32;47152:3;47137:19;;;47131:26;47173:15;;;47166:32;47243:3;47228:19;;47222:26;47257:30;47222:26;47257:30;:::i;:::-;47314:3;47303:15;;47296:32;47307:5;46283:1075;-1:-1:-1;;;46283:1075:193:o;47363:136::-;47398:3;-1:-1:-1;;;47419:22:193;;47416:48;;47444:18;;:::i;:::-;-1:-1:-1;47484:1:193;47480:13;;47363:136::o;48055:127::-;48116:10;48111:3;48107:20;48104:1;48097:31;48147:4;48144:1;48137:15;48171:4;48168:1;48161:15;48187:157;48217:1;48251:4;48248:1;48244:12;48275:3;48265:37;;48282:18;;:::i;:::-;48334:3;48327:4;48324:1;48320:12;48316:22;48311:27;;;48187:157;;;;:::o;48632:385::-;48911:25;;;48898:3;48883:19;;48945:66;49007:2;48992:18;;48984:6;48945:66;:::i;49022:467::-;49133:6;49141;49149;49202:3;49190:9;49181:7;49177:23;49173:33;49170:53;;;49219:1;49216;49209:12;49170:53;49264:16;;49370:2;49355:18;;49349:25;49264:16;;-1:-1:-1;49349:25:193;-1:-1:-1;49419:64:193;49475:7;49470:2;49455:18;;49419:64;:::i;:::-;49409:74;;49022:467;;;;;:::o;49766:216::-;49830:9;;;49858:11;;;49805:3;49888:9;;49916:10;;49912:19;;49941:10;;49933:19;;49909:44;49906:70;;;49956:18;;:::i;:::-;49906:70;;49766:216;;;;:::o;49987:200::-;50053:9;;;50026:4;50081:9;;50109:10;;50121:12;;;50105:29;50144:12;;;50136:21;;50102:56;50099:82;;;50161:18;;:::i", "linkReferences": {"contracts/libraries/Interest.sol": {"Interest": [{"start": 2736, "length": 20}, {"start": 20977, "length": 20}]}, "contracts/libraries/Liquidation.sol": {"Liquidation": [{"start": 18406, "length": 20}, {"start": 18562, "length": 20}]}}, "immutableReferences": {"17857": [{"start": 9947, "length": 32}], "17860": [{"start": 9981, "length": 32}], "17863": [{"start": 5057, "length": 32}], "17866": [{"start": 5094, "length": 32}], "17869": [{"start": 5134, "length": 32}], "17872": [{"start": 5176, "length": 32}, {"start": 13460, "length": 32}], "17875": [{"start": 5216, "length": 32}], "17878": [{"start": 5256, "length": 32}], "17927": [{"start": 6749, "length": 32}, {"start": 15026, "length": 32}, {"start": 21157, "length": 32}], "17930": [{"start": 1599, "length": 32}, {"start": 2228, "length": 32}, {"start": 2453, "length": 32}, {"start": 3670, "length": 32}, {"start": 3956, "length": 32}, {"start": 4608, "length": 32}, {"start": 5445, "length": 32}, {"start": 5612, "length": 32}, {"start": 5754, "length": 32}, {"start": 5926, "length": 32}, {"start": 6837, "length": 32}, {"start": 10019, "length": 32}, {"start": 10691, "length": 32}, {"start": 10836, "length": 32}, {"start": 13163, "length": 32}, {"start": 13789, "length": 32}, {"start": 14015, "length": 32}, {"start": 16284, "length": 32}, {"start": 18089, "length": 32}, {"start": 18438, "length": 32}, {"start": 19561, "length": 32}, {"start": 20673, "length": 32}, {"start": 24193, "length": 32}]}}, "methodIdentifiers": {"IS_TEST()": "fa7626d4", "borrow(address,uint256,uint256,bytes)": "8b049907", "borrowLiquidity(address,uint256,bytes)": "8726b94c", "burn(address)": "89afcb44", "deposit(address)": "f340fa01", "excludeArtifacts()": "b5508aa9", "excludeContracts()": "e20c9f71", "excludeSelectors()": "b0464fdc", "excludeSenders()": "1ed7831c", "exposed_accrueInterestToToken(uint256,uint128)": "7485d319", "exposed_activeLiquidityAssets()": "c1bda029", "exposed_externalLiquidity()": "1a8e10bd", "exposed_getAssets(address)": "ed4a2563", "exposed_getAssets(uint256,address)": "c2a449f8", "exposed_getDepositAndBorrowAndActiveLiquidityAssets()": "d2a8c031", "exposed_getLendingStateTick(int56,uint256)": "5e23377a", "exposed_getTickRange()": "f107074e", "exposed_getTickRange(int16)": "167bc839", "exposed_getTickRangeWithoutLongTerm()": "164fa69f", "exposed_getTickRangeWithoutLongTerm(int16)": "bff9ebbf", "exposed_getTotalAssetsCached(uint256)": "91589137", "exposed_missingAssets()": "16181bca", "exposed_observations()": "599704a7", "exposed_resetTotalAssetsCached()": "823d2102", "exposed_saturationAndGeometricTWAPState()": "af90e352", "exposed_setReferenceReserves(uint256,uint256)": "6cab86d6", "exposed_sharesToAssetsScaler(uint256)": "377aadf0", "exposed_totalSat()": "e5b8fb4d", "externalLiquidity()": "861d4d88", "failed()": "ba414fa6", "getReserves()": "0902f1ac", "getTickRange()": "37116566", "liquidate(address,address,uint256,uint256,uint256,uint256,uint256,uint256,uint256,uint256)": "c9e7cde8", "mint(address)": "6a627842", "referenceReserves()": "2c0e7587", "repay(address)": "9f7c73e9", "repayLiquidity(address)": "c64b1ea4", "skim(address)": "bc25cf77", "swap(uint256,uint256,address,bytes)": "022c0d9f", "sync()": "fff6cae9", "targetArtifactSelectors()": "66d9a9a0", "targetArtifacts()": "85226c81", "targetContracts()": "3f7286f4", "targetInterfaces()": "2ade3880", "targetSelectors()": "916a17c6", "targetSenders()": "3e5e3c23", "tokens(uint256)": "4f64b2be", "totalAssets()": "01e1d114", "underlyingTokens()": "bd27dc9f", "updateExternalLiquidity(uint112)": "6945e18f", "validateOnUpdate(address,address,bool)": "032ca742", "withdraw(address)": "51cff8d9"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"AmmalgamCannotBorrowAgainstSameCollateral\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"AmmalgamDepositIsNotStrictlyBigger\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"AmmalgamLTV\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"AmmalgamMaxBorrowReached\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"AmmalgamMaxSlippage\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"AmmalgamTooMuchLeverage\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Forbidden\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InsufficientInputAmount\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InsufficientLiquidity\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InsufficientLiquidity\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InsufficientLiquidityBurned\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InsufficientLiquidityMinted\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InsufficientOutputAmount\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InsufficientRepayLiquidity\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidToAddress\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"K\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Locked\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NotEnoughRepaidForLiquidation\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Overflow\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"PriceOutOfBounds\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint8\",\"name\":\"bits\",\"type\":\"uint8\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"SafeCastOverflowedUintDowncast\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"}],\"name\":\"SafeERC20FailedOperation\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"TickOutOfBounds\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"borrower\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"tokenType\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"badDebtAssets\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"badDebtShares\",\"type\":\"uint256\"}],\"name\":\"BurnBadDebt\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint128\",\"name\":\"depositLAssets\",\"type\":\"uint128\"},{\"indexed\":false,\"internalType\":\"uint128\",\"name\":\"depositXAssets\",\"type\":\"uint128\"},{\"indexed\":false,\"internalType\":\"uint128\",\"name\":\"depositYAssets\",\"type\":\"uint128\"},{\"indexed\":false,\"internalType\":\"uint128\",\"name\":\"borrowLAssets\",\"type\":\"uint128\"},{\"indexed\":false,\"internalType\":\"uint128\",\"name\":\"borrowXAssets\",\"type\":\"uint128\"},{\"indexed\":false,\"internalType\":\"uint128\",\"name\":\"borrowYAssets\",\"type\":\"uint128\"}],\"name\":\"InterestAccrued\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"borrower\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"depositL\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"depositX\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"depositY\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"repayLX\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"repayLY\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"repayX\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"repayY\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"liquidationType\",\"type\":\"uint256\"}],\"name\":\"Liquidate\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amountXIn\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amountYIn\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amountXOut\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amountYOut\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"}],\"name\":\"Swap\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"reserveXAssets\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"reserveYAssets\",\"type\":\"uint256\"}],\"name\":\"Sync\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint112\",\"name\":\"externalLiquidity\",\"type\":\"uint112\"}],\"name\":\"UpdateExternalLiquidity\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"name\":\"log\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"log_address\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256[]\",\"name\":\"val\",\"type\":\"uint256[]\"}],\"name\":\"log_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"int256[]\",\"name\":\"val\",\"type\":\"int256[]\"}],\"name\":\"log_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address[]\",\"name\":\"val\",\"type\":\"address[]\"}],\"name\":\"log_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"name\":\"log_bytes\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"name\":\"log_bytes32\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"\",\"type\":\"int256\"}],\"name\":\"log_int\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"val\",\"type\":\"address\"}],\"name\":\"log_named_address\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"uint256[]\",\"name\":\"val\",\"type\":\"uint256[]\"}],\"name\":\"log_named_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"int256[]\",\"name\":\"val\",\"type\":\"int256[]\"}],\"name\":\"log_named_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"address[]\",\"name\":\"val\",\"type\":\"address[]\"}],\"name\":\"log_named_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"val\",\"type\":\"bytes\"}],\"name\":\"log_named_bytes\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"val\",\"type\":\"bytes32\"}],\"name\":\"log_named_bytes32\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"val\",\"type\":\"int256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"}],\"name\":\"log_named_decimal_int\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"val\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"}],\"name\":\"log_named_decimal_uint\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"val\",\"type\":\"int256\"}],\"name\":\"log_named_int\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"val\",\"type\":\"string\"}],\"name\":\"log_named_string\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"val\",\"type\":\"uint256\"}],\"name\":\"log_named_uint\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"name\":\"log_string\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"log_uint\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"name\":\"logs\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"IS_TEST\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amountXAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"amountYAssets\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"borrow\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"borrowAmountLAssets\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"borrowLiquidity\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"}],\"name\":\"burn\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"amountXAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"amountYAssets\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"}],\"name\":\"deposit\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeArtifacts\",\"outputs\":[{\"internalType\":\"string[]\",\"name\":\"excludedArtifacts_\",\"type\":\"string[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeContracts\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"excludedContracts_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeSelectors\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"bytes4[]\",\"name\":\"selectors\",\"type\":\"bytes4[]\"}],\"internalType\":\"struct StdInvariant.FuzzSelector[]\",\"name\":\"excludedSelectors_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeSenders\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"excludedSenders_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"tokenType\",\"type\":\"uint256\"},{\"internalType\":\"uint128\",\"name\":\"amount\",\"type\":\"uint128\"}],\"name\":\"exposed_accrueInterestToToken\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"exposed_activeLiquidityAssets\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"exposed_externalLiquidity\",\"outputs\":[{\"internalType\":\"uint112\",\"name\":\"\",\"type\":\"uint112\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"tokenType\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"}],\"name\":\"exposed_getAssets\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"}],\"name\":\"exposed_getAssets\",\"outputs\":[{\"internalType\":\"uint256[6]\",\"name\":\"\",\"type\":\"uint256[6]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"exposed_getDepositAndBorrowAndActiveLiquidityAssets\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"int56\",\"name\":\"lastLendingCumulativeSum\",\"type\":\"int56\"},{\"internalType\":\"uint256\",\"name\":\"duration\",\"type\":\"uint256\"}],\"name\":\"exposed_getLendingStateTick\",\"outputs\":[{\"internalType\":\"int16\",\"name\":\"lendingStateTick\",\"type\":\"int16\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"int16\",\"name\":\"currentTick\",\"type\":\"int16\"}],\"name\":\"exposed_getTickRange\",\"outputs\":[{\"internalType\":\"int16\",\"name\":\"\",\"type\":\"int16\"},{\"internalType\":\"int16\",\"name\":\"\",\"type\":\"int16\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"exposed_getTickRange\",\"outputs\":[{\"internalType\":\"int16\",\"name\":\"\",\"type\":\"int16\"},{\"internalType\":\"int16\",\"name\":\"\",\"type\":\"int16\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"exposed_getTickRangeWithoutLongTerm\",\"outputs\":[{\"internalType\":\"int16\",\"name\":\"\",\"type\":\"int16\"},{\"internalType\":\"int16\",\"name\":\"\",\"type\":\"int16\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"int16\",\"name\":\"currentTick\",\"type\":\"int16\"}],\"name\":\"exposed_getTickRangeWithoutLongTerm\",\"outputs\":[{\"internalType\":\"int16\",\"name\":\"\",\"type\":\"int16\"},{\"internalType\":\"int16\",\"name\":\"\",\"type\":\"int16\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"tokenType\",\"type\":\"uint256\"}],\"name\":\"exposed_getTotalAssetsCached\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"tokenTotalAssets\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"exposed_missingAssets\",\"outputs\":[{\"internalType\":\"uint112\",\"name\":\"\",\"type\":\"uint112\"},{\"internalType\":\"uint112\",\"name\":\"\",\"type\":\"uint112\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"exposed_observations\",\"outputs\":[{\"components\":[{\"internalType\":\"bool\",\"name\":\"isMidTermBufferInitialized\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"isLongTermBufferInitialized\",\"type\":\"bool\"},{\"internalType\":\"uint8\",\"name\":\"midTermIndex\",\"type\":\"uint8\"},{\"internalType\":\"uint8\",\"name\":\"longTermIndex\",\"type\":\"uint8\"},{\"internalType\":\"int16\",\"name\":\"lastTick\",\"type\":\"int16\"},{\"internalType\":\"int16\",\"name\":\"lastLendingStateTick\",\"type\":\"int16\"},{\"internalType\":\"uint24\",\"name\":\"midTermIntervalConfig\",\"type\":\"uint24\"},{\"internalType\":\"uint24\",\"name\":\"longTermIntervalConfig\",\"type\":\"uint24\"},{\"internalType\":\"int56\",\"name\":\"lendingCumulativeSum\",\"type\":\"int56\"},{\"internalType\":\"int56[51]\",\"name\":\"midTermCumulativeSum\",\"type\":\"int56[51]\"},{\"internalType\":\"int56[9]\",\"name\":\"longTermCumulativeSum\",\"type\":\"int56[9]\"},{\"internalType\":\"uint32[51]\",\"name\":\"midTermTimeInterval\",\"type\":\"uint32[51]\"},{\"internalType\":\"uint32[9]\",\"name\":\"longTermTimeInterval\",\"type\":\"uint32[9]\"}],\"internalType\":\"struct GeometricTWAP.Observations\",\"name\":\"\",\"type\":\"tuple\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"exposed_resetTotalAssetsCached\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"exposed_saturationAndGeometricTWAPState\",\"outputs\":[{\"internalType\":\"contract ISaturationAndGeometricTWAPState\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_referenceReserveX\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_referenceReserveY\",\"type\":\"uint256\"}],\"name\":\"exposed_setReferenceReserves\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"tokenType\",\"type\":\"uint256\"}],\"name\":\"exposed_sharesToAssetsScaler\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"exposed_totalSat\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"totalSatNetX\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"totalSatNetY\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"externalLiquidity\",\"outputs\":[{\"internalType\":\"uint112\",\"name\":\"\",\"type\":\"uint112\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"failed\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getReserves\",\"outputs\":[{\"internalType\":\"uint112\",\"name\":\"_reserveXAssets\",\"type\":\"uint112\"},{\"internalType\":\"uint112\",\"name\":\"_reserveYAssets\",\"type\":\"uint112\"},{\"internalType\":\"uint32\",\"name\":\"_lastTimestamp\",\"type\":\"uint32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getTickRange\",\"outputs\":[{\"internalType\":\"int16\",\"name\":\"minTick\",\"type\":\"int16\"},{\"internalType\":\"int16\",\"name\":\"maxTick\",\"type\":\"int16\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"borrower\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"depositLToBeTransferredInLAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"depositXToBeTransferredInXAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"depositYToBeTransferredInYAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"repayLXInXAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"repayLYInYAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"repayXInXAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"repayYInYAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"liquidationType\",\"type\":\"uint256\"}],\"name\":\"liquidate\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"}],\"name\":\"mint\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"liquidityShares\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"referenceReserves\",\"outputs\":[{\"internalType\":\"uint112\",\"name\":\"\",\"type\":\"uint112\"},{\"internalType\":\"uint112\",\"name\":\"\",\"type\":\"uint112\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"onBehalfOf\",\"type\":\"address\"}],\"name\":\"repay\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"repayXInXAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"repayYInYAssets\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"onBehalfOf\",\"type\":\"address\"}],\"name\":\"repayLiquidity\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"repaidLXInXAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"repaidLYInYAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"repayLiquidityAssets\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"}],\"name\":\"skim\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"amountXOut\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"amountYOut\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"swap\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"sync\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetArtifactSelectors\",\"outputs\":[{\"components\":[{\"internalType\":\"string\",\"name\":\"artifact\",\"type\":\"string\"},{\"internalType\":\"bytes4[]\",\"name\":\"selectors\",\"type\":\"bytes4[]\"}],\"internalType\":\"struct StdInvariant.FuzzArtifactSelector[]\",\"name\":\"targetedArtifactSelectors_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetArtifacts\",\"outputs\":[{\"internalType\":\"string[]\",\"name\":\"targetedArtifacts_\",\"type\":\"string[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetContracts\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"targetedContracts_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetInterfaces\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"string[]\",\"name\":\"artifacts\",\"type\":\"string[]\"}],\"internalType\":\"struct StdInvariant.FuzzInterface[]\",\"name\":\"targetedInterfaces_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetSelectors\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"bytes4[]\",\"name\":\"selectors\",\"type\":\"bytes4[]\"}],\"internalType\":\"struct StdInvariant.FuzzSelector[]\",\"name\":\"targetedSelectors_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetSenders\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"targetedSenders_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"tokenType\",\"type\":\"uint256\"}],\"name\":\"tokens\",\"outputs\":[{\"internalType\":\"contract IAmmalgamERC20\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"totalAssets\",\"outputs\":[{\"internalType\":\"uint128[6]\",\"name\":\"\",\"type\":\"uint128[6]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"underlyingTokens\",\"outputs\":[{\"internalType\":\"contract IERC20\",\"name\":\"\",\"type\":\"address\"},{\"internalType\":\"contract IERC20\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint112\",\"name\":\"_externalLiquidity\",\"type\":\"uint112\"}],\"name\":\"updateExternalLiquidity\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"validate\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"update\",\"type\":\"address\"},{\"internalType\":\"bool\",\"name\":\"isBorrow\",\"type\":\"bool\"}],\"name\":\"validateOnUpdate\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"}],\"name\":\"withdraw\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"errors\":{\"SafeCastOverflowedUintDowncast(uint8,uint256)\":[{\"details\":\"Value doesn't fit in an uint of `bits` size.\"}],\"SafeERC20FailedOperation(address)\":[{\"details\":\"An operation with an ERC-20 token failed.\"}]},\"events\":{\"BurnBadDebt(address,uint256,uint256,uint256)\":{\"details\":\"Emitted when bad debt is burned\",\"params\":{\"badDebtAssets\":\"The amount of bad debt assets being burned\",\"badDebtShares\":\"The amount of bad debt shares being burned\",\"borrower\":\"The address of the borrower\",\"tokenType\":\"The type of token being burned\"}},\"InterestAccrued(uint128,uint128,uint128,uint128,uint128,uint128)\":{\"details\":\"Emitted when Interest gets accrued\",\"params\":{\"borrowLAssets\":\"The amount of total `BORROW_L` assets in the pool after interest accrual\",\"borrowXAssets\":\"The amount of total `BORROW_X` assets in the pool after interest accrual\",\"borrowYAssets\":\"The amount of total `BORROW_Y` assets in the pool after interest accrual\",\"depositLAssets\":\"The amount of total `DEPOSIT_L` assets in the pool after interest accrual\",\"depositXAssets\":\"The amount of total `DEPOSIT_X` assets in the pool after interest accrual\",\"depositYAssets\":\"The amount of total `DEPOSIT_Y` assets in the pool after interest accrual\"}},\"Liquidate(address,address,uint256,uint256,uint256,uint256,uint256,uint256,uint256,uint256)\":{\"details\":\"Emitted on liquidation\",\"params\":{\"borrower\":\"The account being liquidated.\",\"depositL\":\"The amount of L tokens to be transferred from the hard deposit.\",\"depositX\":\"The amount of X tokens to be transferred from the hard deposit.\",\"depositY\":\"The amount of Y tokens to be transferred from the hard deposit.\",\"liquidationType\":\"The type of liquidation to be performed: HARD, SOFT, LEVERAGE\",\"repayLX\":\"The amount of L tokens repaid in X.\",\"repayLY\":\"The amount of L tokens repaid in Y.\",\"repayX\":\"The amount of X tokens repaid.\",\"repayY\":\"The amount of Y tokens repaid.\",\"to\":\"The account to send the liquidated deposit to\"}},\"Swap(address,uint256,uint256,uint256,uint256,address)\":{\"details\":\"Emitted on a token swap\",\"params\":{\"amountXIn\":\"The amount of token X provided for the swap\",\"amountXOut\":\"The amount of token X received from the swap\",\"amountYIn\":\"The amount of token Y provided for the swap\",\"amountYOut\":\"The amount of token Y received from the swap\",\"sender\":\"The address initiating the swap\",\"to\":\"Address where the swapped tokens are sent\"}},\"Sync(uint256,uint256)\":{\"details\":\"Emitted when reserves are synchronized\",\"params\":{\"reserveXAssets\":\"The updated reserve for token X\",\"reserveYAssets\":\"The updated reserve for token Y\"}},\"UpdateExternalLiquidity(uint112)\":{\"details\":\"Emitted when external liquidity is updated\",\"params\":{\"externalLiquidity\":\"The updated value for external liquidity\"}}},\"kind\":\"dev\",\"methods\":{\"borrow(address,uint256,uint256,bytes)\":{\"details\":\"Verifies the borrowing amounts, mints corresponding debt tokens, transfers the assets, and updates missing assets. Also supports flash loan interactions.\",\"params\":{\"amountXAssets\":\"Amount of asset X to borrow.\",\"amountYAssets\":\"Amount of asset Y to borrow.\",\"data\":\"Call data to be sent to external contract if flash loan interaction is desired.\",\"to\":\"Address to which the borrowed assets will be transferred.\"}},\"burn(address)\":{\"details\":\"Calculates the amounts of assets to be returned based on liquidity.      Requires amountXAssets and amountYAssets to be greater than 0.      Emits a #Burn event and performs a safe transfer of assets.\",\"params\":{\"to\":\"address to which the underlying assets will be transferred\"},\"returns\":{\"amountXAssets\":\"amount of first token to be returned\",\"amountYAssets\":\"amount of second token to be returned\"}},\"deposit(address)\":{\"details\":\"Verifies deposit amounts and types, adjusts reserves if necessary, mints corresponding tokens, and updates missing assets.\",\"params\":{\"to\":\"Address to which tokens will be minted.\"}},\"exposed_getTickRange(int16)\":{\"details\":\"Returns the minimum and maximum tick values that define a tick range.\",\"params\":{\"currentTick\":\"The current (most recent) tick based on the current reserves.\"},\"returns\":{\"_0\":\"The minimum tick value among current, mid-term, and long-term ticks.\",\"_1\":\"The maximum tick value among current, mid-term, and long-term ticks.\"}},\"exposed_observations()\":{\"details\":\"Returns the GeometricTWAP Observation struct.\",\"returns\":{\"_0\":\"observations A struct containing the current observations data.\"}},\"getReserves()\":{\"returns\":{\"_lastTimestamp\":\"The timestamp of the last operation.\",\"_reserveXAssets\":\"The current reserve of asset X.\",\"_reserveYAssets\":\"The current reserve of asset Y.\"}},\"liquidate(address,address,uint256,uint256,uint256,uint256,uint256,uint256,uint256,uint256)\":{\"params\":{\"borrower\":\"The account being liquidated\",\"depositLToBeTransferredInLAssets\":\"The amount of L to be transferred to the liquidator.\",\"depositXToBeTransferredInXAssets\":\"The amount of X to be transferred to the liquidator.\",\"depositYToBeTransferredInYAssets\":\"The amount of Y to be transferred to the liquidator.\",\"liquidationType\":\"The type of liquidation to be performed: HARD, SOFT, LEVERAGE\",\"repayLXInXAssets\":\"The amount of LX to be repaid by the liquidator.\",\"repayLYInYAssets\":\"The amount of LY to be repaid by the liquidator.\",\"repayXInXAssets\":\"The amount of X to be repaid by the liquidator.\",\"repayYInYAssets\":\"The amount of Y to be repaid by the liquidator.\",\"to\":\"The account to send the liquidated deposit to\"}},\"mint(address)\":{\"details\":\"Calculates the amount of tokens to mint based on reserves and balances. Requires liquidity > 0. Emits a #Mint event.\",\"params\":{\"to\":\"address to which tokens will be minted\"},\"returns\":{\"liquidityShares\":\"amount of tokens minted\"}},\"referenceReserves()\":{\"returns\":{\"_0\":\"The reference reserve for asset X.\",\"_1\":\"The reference reserve for asset Y.\"}},\"repay(address)\":{\"details\":\"Burns corresponding borrowed tokens, adjusts the reserves, and updates missing assets.\",\"params\":{\"onBehalfOf\":\"Address of the entity on whose behalf the repayment is made.\"},\"returns\":{\"repayXInXAssets\":\"Amount of token X repaid\",\"repayYInYAssets\":\"Amount of token Y repaid\"}},\"repayLiquidity(address)\":{\"details\":\"Calculates repayable liquidity, burns corresponding tokens, adjusts reserves, and updates active liquidity.\",\"params\":{\"onBehalfOf\":\"Address of the entity on whose behalf the liquidity repayment is made.\"},\"returns\":{\"repaidLXInXAssets\":\"Amount of liquidity repaid in X.\",\"repaidLYInYAssets\":\"Amount of liquidity repaid in Y.\",\"repayLiquidityAssets\":\"Amount of liquidity repaid in L.\"}},\"skim(address)\":{\"details\":\"Calculates the excess of tokenX and tokenY balances and transfers them to the specified address.\",\"params\":{\"to\":\"The address to which the excess tokens are transferred.\"}},\"swap(uint256,uint256,address,bytes)\":{\"details\":\"Requires at least one of `amountXOut` and `amountYOut` to be greater than 0,      and that the amount out does not exceed the reserves.      An optimistically transfer of tokens is performed.      A callback is executed if `data` is not empty.      Emits a #Swap event.\",\"params\":{\"amountXOut\":\"Amount of first token to be swapped out.\",\"amountYOut\":\"Amount of second token to be swapped out.\",\"data\":\"Data to be sent along with the call, can be used for a callback.\",\"to\":\"Address to which the swapped tokens are sent.\"}},\"sync()\":{\"details\":\"Reads the current balance of tokenX and tokenY in the contract, and updates the reserves to match these balances.\"},\"tokens(uint256)\":{\"params\":{\"tokenType\":\"The type of token for which the scaler is being computed.                  Can be one of BORROW_X, DEPOSIT_X, BORROW_Y, DEPOSIT_Y, BORROW_L, or DEPOSIT_L.\"},\"returns\":{\"_0\":\"The IAmmalgamERC20 token\"}},\"totalAssets()\":{\"details\":\"If the last lending state update is outdated (i.e., not matching the current block timestamp),      the function recalculates the assets based on the duration since the last update, the lending state,      and reserve balances. If the timestamp is current, the previous asset (without recalculation) is returned.\",\"returns\":{\"_0\":\"totalAssets An array of six `uint128` values representing the total assets for each of the 6 amalgam token types.  These values may be adjusted based on the time elapsed since the last update. If the timestamp is up-to-date, the  previously calculated total assets are returned without recalculation.\"}},\"underlyingTokens()\":{\"returns\":{\"_0\":\"The addresses of the underlying tokens.\"}},\"updateExternalLiquidity(uint112)\":{\"details\":\"This function sets the external liquidity to a new value and emits an event with the new value. It can only be called by the fee setter.\",\"params\":{\"_externalLiquidity\":\"The new external liquidity value.\"}},\"validateOnUpdate(address,address,bool)\":{\"details\":\"Implementation should properly protect against any creation of new debt or transfer of existing debt or collateral that would leave any individual address with insufficient collateral to cover all debts.\",\"params\":{\"update\":\"The address of the account having its saturation updated\",\"validate\":\"The address of the account being checked for solvency and having its saturation updated\"}}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"borrow(address,uint256,uint256,bytes)\":{\"notice\":\"Handles borrowing from the contract.\"},\"burn(address)\":{\"notice\":\"Burns liquidity tokens from the contract and sends the underlying assets to `to` address.\"},\"deposit(address)\":{\"notice\":\"Handles deposits into the contract.\"},\"exposed_activeLiquidityAssets()\":{\"notice\":\"Represents the amount of liquidity assets available in the         contract.\"},\"exposed_getTickRange(int16)\":{\"notice\":\"Retrieves the tick range values.\"},\"exposed_observations()\":{\"notice\":\"Retrieves the current observations from the contract.\"},\"getReserves()\":{\"notice\":\"Fetches the current reserves of asset X and asset Y, as well as the block of the last operation.\"},\"liquidate(address,address,uint256,uint256,uint256,uint256,uint256,uint256,uint256,uint256)\":{\"notice\":\"LTV based liquidation. The LTV dictates the max premium that can be had by the liquidator.\"},\"mint(address)\":{\"notice\":\"Mints tokens and assigns them to `to` address.\"},\"referenceReserves()\":{\"notice\":\"Returns the reference reserves for the block, these represent a snapshot of the   reserves at the start of the block weighted for mints, burns, borrow and repayment of   liquidity. These amounts are critical to calculating the correct fees for any swap.\"},\"repay(address)\":{\"notice\":\"Handles repayment of borrowed assets.\"},\"repayLiquidity(address)\":{\"notice\":\"Handles repayment of borrowed liquidity.\"},\"skim(address)\":{\"notice\":\"Transfers excess tokens to a specified address.\"},\"swap(uint256,uint256,address,bytes)\":{\"notice\":\"Executes a swap of tokens.\"},\"sync()\":{\"notice\":\"Updates the reserves to match the current token balances.\"},\"tokens(uint256)\":{\"notice\":\"Return the IAmmalgamERC20 token corresponding to the token type\"},\"totalAssets()\":{\"notice\":\"Computes the current total Assets.\"},\"underlyingTokens()\":{\"notice\":\"Get the underlying tokens for the AmmalgamERC20Controller.\"},\"updateExternalLiquidity(uint112)\":{\"notice\":\"Updates the external liquidity value.\"},\"validateOnUpdate(address,address,bool)\":{\"notice\":\"Validates the solvency of an account for a given token transfer operation.\"},\"withdraw(address)\":{\"notice\":\"withdraw X and/or Y\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"test/shared/FactoryPairTestFixture.sol\":\"PairHarness\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":1inch/=lib/1inch/\",\":@1inch/=lib/1inch/\",\":@mangrovedao/mangrove-core/=lib/mangrove-core/\",\":@mgv/forge-std/=lib/mangrove-core/lib/forge-std/src/\",\":@mgv/lib/=lib/mangrove-core/lib/\",\":@mgv/script/=lib/mangrove-core/script/\",\":@mgv/src/=lib/mangrove-core/src/\",\":@mgv/test/=lib/mangrove-core/test/\",\":@morpho-org/morpho-blue/=lib/morpho-blue/\",\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ExcessivelySafeCall/=lib/ExcessivelySafeCall/src/\",\":core/=lib/mangrove-core/lib/core/\",\":ds-test/=lib/ExcessivelySafeCall/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/openzeppelin-contracts/lib/forge-std/src/\",\":halmos-cheatcodes/=lib/morpho-blue/lib/halmos-cheatcodes/src/\",\":mangrove-core/=lib/mangrove-core/\",\":morpho-blue/=lib/morpho-blue/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":preprocessed/=lib/mangrove-core/lib/preprocessed/\"]},\"sources\":{\"contracts/AmmalgamPair.sol\":{\"keccak256\":\"0xe8f98844a55a216605e6c6dd6837977fafda458a6b5d0cfa1f3a18d25e9432e4\",\"urls\":[\"bzz-raw://65dda1a1de8dd64e31c666b13de3d0583b4b0da923c67065cadcddefe47562a2\",\"dweb:/ipfs/Qmaev9WFa4yyL8fXVoWkXwNsTTY8wY7jTBGDoKJbdwSCzS\"]},\"contracts/SaturationAndGeometricTWAPState.sol\":{\"keccak256\":\"0x5e293a35668bb216a99379ea2176894314cc0f1ac68644fcf4c07017da1a4419\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://00349bb86f1b657010919b4bc3f616ad56ef4883b99ab0eead36815dae93dc76\",\"dweb:/ipfs/QmbEd9GD2JxuDntX35YcfbSCcpRstDU9GDPUkBKGzsxvqE\"]},\"contracts/factories/AmmalgamFactory.sol\":{\"keccak256\":\"0xe0d9baf63d9538a7ecb8bd24ea61a8cdf6fc9c1e9eb028f343548adeb8b93e4e\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://bfca24db47fbbaeef8bc36996cbfed78eb48771ac71d2800f081fb58a8e8c92b\",\"dweb:/ipfs/QmUfYEwfmrjSmchqXi58SnpSina4qKQvD2Jbk5RqYsaoCa\"]},\"contracts/factories/ERC20DebtLiquidityTokenFactory.sol\":{\"keccak256\":\"0x72e3ada6a2f0792a353b730c1b45ae832f9ce2f58f0bda039383f8890cb2a4f7\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://4949e7b66647313aaba2e11d7edde06eb87345b476c1a20f890659c1af827b2b\",\"dweb:/ipfs/Qmf3emVXfGp1oc8iVYxnVqpJ88vnxxdj7WqPm1vzVKb1SD\"]},\"contracts/factories/ERC20LiquidityTokenFactory.sol\":{\"keccak256\":\"0x762974ca1ed600e0930a92bd2eb3a1a5f9ef0469ab2e6e811e4674e098238762\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://5fd5f33537aeea9bac1f18c6fca2057899ec5f90cb8c756622eb436d5b13e27e\",\"dweb:/ipfs/QmfYznzzwN1AmdnuzNKe1R6t8UeztaZVGuzJ8vKfzjMXYN\"]},\"contracts/factories/ERC4626DebtTokenFactory.sol\":{\"keccak256\":\"0x7deeb7a40d26bc790112f29836da83050fa3554e471e1dce4dda6bf29ab9bf67\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://5a46a4c8270e0b8a731259328b6c35c84de270a14f2f69ba04bc58d18400efc6\",\"dweb:/ipfs/QmQ56QbX6S9GjQinsFYtTMns6HgpcTXW1wnvQT6QgiuW1Z\"]},\"contracts/factories/ERC4626DepositTokenFactory.sol\":{\"keccak256\":\"0xf84b75119f2680f8079bb9567b0c03c0ad49b71a8c00f968d03d5fca2a954035\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://c3fc7a9e300a935991746d5be835418b09e6d2b20b65e3e297d4faf28516469b\",\"dweb:/ipfs/QmQMr9MA5a3UcZCiP3e2haYqzBsbE8Pe6rDq6j6RJ3ub4Z\"]},\"contracts/factories/NewTokensFactory.sol\":{\"keccak256\":\"0x86cd420e1df8a59b11a4ab53a16971a44953f0a07741ef69d95baa4bd60126ac\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://d8cdd98060f059705b9ae2b64ab3e74395c0f3a24e12f5ac11ca7e509c6a7aa0\",\"dweb:/ipfs/QmahgKkRzuWHpQ73DHGZ4Kvd2MQG7MpfPShayJDRJQYSVr\"]},\"contracts/interfaces/IAmmalgamPair.sol\":{\"keccak256\":\"0xa17e45b2348d8920d9970c5d50b300fc0a1e8d03350cdd0d1a624494baa70337\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://8d252e89e5d49d1c15a0c0c0a495a325b9f8d608714b29279a7bacb1e4bf8795\",\"dweb:/ipfs/QmRkZ7a8JJQYEw6HQMJjjkuAK8b5Th1X1ET6BG1R8mx4qw\"]},\"contracts/interfaces/ISaturationAndGeometricTWAPState.sol\":{\"keccak256\":\"0xc9add2ad41f8edd9d360ced8d2cd7bd18dd500304794434fb2e309fa0f5af83c\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://8ecc810c544ac734ef26a2f6bebea3f3bd12d773965d297991e0e0e72892fa20\",\"dweb:/ipfs/QmarXc1Ut4FZzPRRZs2M2udbJjuZUJQHQ8fvmSr3bpHErR\"]},\"contracts/interfaces/callbacks/IAmmalgamCallee.sol\":{\"keccak256\":\"0x904b858859d460a61c9e644ca87009d8e32ba20482ef218801c89c7fb1ece339\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://1a7cedebbacc453e3e4e339fcc76fd3268247c13982de82b4930d59a44256c1d\",\"dweb:/ipfs/QmdjdvYabbwAYcV72xjiXyq278xQivFtiqD3eQ5P9Gk4f1\"]},\"contracts/interfaces/callbacks/ITransferValidator.sol\":{\"keccak256\":\"0x6d9028fc4ad1914e6b2091e6ba46a9f836f9e67ea435c4a8fef41363f2ceaf56\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://7ecaade4884d460168f6978edf35706f7b9e363de6002942b1d171a338dca6a4\",\"dweb:/ipfs/QmS5wgfDt5Pn68rpCytpzhiy57LcmivVFQ5XLGXUUP5Tt8\"]},\"contracts/interfaces/factories/IAmmalgamFactory.sol\":{\"keccak256\":\"0x1c80089901e8d7d7451775b5eaa92092eb2b65319cb92fa7884281bae49f52b8\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://bf1201399bb9d5046e0e788ff88394b2468771096a2a0d3500af542923e84628\",\"dweb:/ipfs/QmeUaPyQpbPbP5fyPUT2FfzeDgHcdyQAn1DaNg9uCuGoj9\"]},\"contracts/interfaces/factories/IFactoryCallback.sol\":{\"keccak256\":\"0x33250cf8351adb4846a3d133a9bc06568288e4c680bcf5b1085e3bca40a35e52\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://5663a39af4ed3040a58beaa5641425b9adca83c2683dd220e0c11e644fefe52b\",\"dweb:/ipfs/QmYB8Vf37WDzQfSpMDjv8hVicuaF1wMBzf7xjHRjGCy3wT\"]},\"contracts/interfaces/factories/INewTokensFactory.sol\":{\"keccak256\":\"0x3b2f1ee34106d2694a9ebbe600be692bed645f4247f4a24da3d5ec46025ab3e9\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://73143452a06db52deb593585fea6f2ef7c46e9ef6d649562dc39e79e4e5dca2b\",\"dweb:/ipfs/QmYQEy7BZWnfWKnuac8GB4QPhG5qJpaHQAfkTBoUDEuX1E\"]},\"contracts/interfaces/factories/ITokenFactory.sol\":{\"keccak256\":\"0xac23e5c0441599add526b0c308faa7787f90bf01603b6dbc231944c166ca32d6\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://ac574b98b2c1034786581137a218277ec58e06e9612f76814f34960383083626\",\"dweb:/ipfs/QmZgZqVnshjzuHBXJTR9g87S15CyLwJUSErGEDoJpBd4kg\"]},\"contracts/interfaces/tokens/IAmmalgamERC20.sol\":{\"keccak256\":\"0x44a376269170b4270ec221ce3cb31a609b394e216cc4d2e27b818361b4369829\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://c48bc7586631f27ede73d3d0b4c1d7a29b1653e6c501c8b7fc9877c125f8f57e\",\"dweb:/ipfs/QmTSLtqnsxr7h7ct524rqYssHUo4qursmCZ7g5q3J1qQPK\"]},\"contracts/interfaces/tokens/IERC20DebtToken.sol\":{\"keccak256\":\"0xc50c6be17633c8ac677b4eaac7c05a6de1f1c938237179b59ad5e65bcfbcb03a\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://6c75327e01d70a0c22662a9a8214aa64e45c517146971f8636e5aa5bf06e7696\",\"dweb:/ipfs/QmV2ydBQ5S9ZBtRuPgBMBdvd2Hcnn8quCGEMhhAAuic15b\"]},\"contracts/interfaces/tokens/IPluginRegistry.sol\":{\"keccak256\":\"0x9a677620d88ac7dc42afb21d82a7b7a89bd934c1cada5450cf2b6200bf374ccf\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://304091e5d54e9ad7db24ba1022e84e39dd9305d9cc72fd87423c71b40de4ab3d\",\"dweb:/ipfs/QmZEF5MfmUcxzLF9fcGCLvGMTTLLhcWdCMCDK2WyXj6s7X\"]},\"contracts/interfaces/tokens/ITokenController.sol\":{\"keccak256\":\"0x7778001aaf582fe10005240eb6023b2b6cee3f100b6c2222bf6b9ade93732624\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://91e5c4519207d6a450be1e0a8649157e86d20f8ef6a91ff6512a31cf5561a570\",\"dweb:/ipfs/QmUqZLW27JJZHFPf2fgLDYSWWj5gM158DdaxTTmDVukRAg\"]},\"contracts/libraries/Convert.sol\":{\"keccak256\":\"0x944776d31291de1a9cdc6a52154c23c22b43a01c3edebe7a4140e267edbba975\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://36c03749859077ba47a3acfd574f8c30f34f97def4ce81d7f4feac9a7b62794c\",\"dweb:/ipfs/QmdycZay5X2WrbS8qS7RycLpZbMQx7yKszWQzGU3rqidpH\"]},\"contracts/libraries/GeometricTWAP.sol\":{\"keccak256\":\"0x3860409daa0fdb5d96f0bfb8b49cbca058b9fe32c8e32457f85d4ee2c5cdcb1e\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://81fe70a80f4005a6529c7e93f1a92547ce0bf74c357c280a91e8778b378b18aa\",\"dweb:/ipfs/QmdRQ1DqsCu11zfbLAbrrzJ9Ups3oKgTGimYo3Zm3ALiCz\"]},\"contracts/libraries/Interest.sol\":{\"keccak256\":\"0xbc8bfa20d7295dd70e3c716fd3dbeb5b45d313e3c609d063d186042cbf000646\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://b015e8d4976d3b6d7eaca07dfcc487aeed3a7d8b4c41c8369a7476dcfb211194\",\"dweb:/ipfs/QmecH84UnZYxDZ2aL6rQtnrEExLEAfo7q4Y47yuBXdymeX\"]},\"contracts/libraries/Liquidation.sol\":{\"keccak256\":\"0x842bc44bc3cff80360ab82c5920070b12680edefe9267bdffc2d6c3c3a692d63\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://85ecd75568a0729aec06741d0575ed07dad8b7daebd7ba3114a93f6019250877\",\"dweb:/ipfs/QmQMvWdsPWsQ4t1yv6eyZy5TM7h1EZpSJdt5b8fDLcumCW\"]},\"contracts/libraries/QuadraticSwapFees.sol\":{\"keccak256\":\"0x00f6b7909be4fa1fc1ba426dd8ae659d1c5cb20c79665148898c973f55cfdccb\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://c64da0826a9b0ffc08319709f6db03339d22d24deda902a6540393251da0aecb\",\"dweb:/ipfs/QmSNwBbn2VAS8HPY4hNZusEc4DoKKZAZHtpPdjL9Gz3gs3\"]},\"contracts/libraries/Saturation.sol\":{\"keccak256\":\"0xf44bc610ece4bc7ebdb0730aa6ad69ea47647e19d4c1944c663d2d2eb4f10860\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://421fdf8d0b27132bc324a42ede9aaf23b476e5134e1073f84e824620a2a44f20\",\"dweb:/ipfs/QmbvSfMuMzDmrfPkCAEp7ydtRDWu5EUiXq4MyrGGjFErzE\"]},\"contracts/libraries/TickMath.sol\":{\"keccak256\":\"0x753813c7ed638d22edb71f48f8eb8b4283b3db2ba5b136b5c8909bd37ffa3f12\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://04dd5085b72f6d73e1b17f58148e4d03639f654bdc4fdbc173b7c92ff102fc20\",\"dweb:/ipfs/QmSg4xTQPkngjNxs84428FZdSwH4AUQpwLXaASx7Qev6oG\"]},\"contracts/libraries/TokenSymbol.sol\":{\"keccak256\":\"0x628df064fdbdacfe6783964d7bf38cdf1b34e1ad07caa3cea39bf7468cc19b43\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://da6823ce0debaabe20f25281e81a4fc88de98d4df2942a5e276826ac381c227b\",\"dweb:/ipfs/QmNpEuQ25788xfcJwPk2xUB7fyP7fW5ENK2e9qgRqp1BcH\"]},\"contracts/libraries/Uint16Set.sol\":{\"keccak256\":\"0x26a714430fe1618d78386e953153b4bd2bf024baee54453ec9a7a0cc60e1534f\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://8667dd78541d656a09678e5f9cce4d49adc805955604ccaaec414e9b241f5e06\",\"dweb:/ipfs/QmZVWU2CzyDQfGit32HjJxDphBJMKG3d6JRuxbC682Z1gy\"]},\"contracts/libraries/Validation.sol\":{\"keccak256\":\"0x294848b2af973dbcd8b83732a57b67f14fd15e4af0668de05a2928b8eca5a463\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://fab25c941e87f6924b31e3f20742ca6b5ec1b7e4251543f4a61567a04ef4d778\",\"dweb:/ipfs/Qmf4ChH8afdHc3SfXkFPpNGp3e1hscyvnujPAMza3yuXeA\"]},\"contracts/libraries/constants.sol\":{\"keccak256\":\"0x0dfb294985a8f48287ff13e8476718ddb5334b1d8bf6bfa59a5db1dbcf6ca7c4\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://4bedcfdb2850cfb22b5daa768ab8125b4ccab97c90068d1d0ad4495bf942b362\",\"dweb:/ipfs/Qmf9p88yQN2JYRBR5D7q9BLmwhDJWpFk47ZuayrKqCyHat\"]},\"contracts/tokens/ERC20Base.sol\":{\"keccak256\":\"0xdd3db9eaa855b6ff747ffaa0e74dd2a64dd5b0d704356acb75c2690c3fc6bc2b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8f9e05ae3078bc3a29ef43861b006fb290606c1c9b2676baaed3ab98ecdb2d59\",\"dweb:/ipfs/QmZrvgxYtUD6jQVBvM9rT7jj5Vzb5csiThGj3ZwHSPryAL\"]},\"contracts/tokens/ERC20DebtBase.sol\":{\"keccak256\":\"0xc0a59cd54fcd847b160d662aa45a5fe7d24ed90c8030fe17fd5f9def427ed19a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://365c7f18505af36b2806404b1b3f2d897de6ac18e255ecfbb4ccc491cac7e444\",\"dweb:/ipfs/QmUqx8EBwRb6W1YQPb9MjwAhEEHNpZTCopbGWb1vbyuUpp\"]},\"contracts/tokens/ERC20DebtLiquidityToken.sol\":{\"keccak256\":\"0xf222ad5562ed41d74b0cfb5b4aad84ec9f4cb91b6d71928b30b018bab494efe8\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://a8e8f3e7ded2eae04c63ce3ae7a86c051d48d6db697cb6929d7064a4ec9d7371\",\"dweb:/ipfs/QmU3EuwHU3xB1e6MxaRjSRJcDMK73wfZig9uGWqZPaHnTn\"]},\"contracts/tokens/ERC20LiquidityToken.sol\":{\"keccak256\":\"0x2bb2429e551c031034c747749373d2e4c451580e9b203b689d6eaf03ad896358\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9ad5902756073578beee9068b74bd921e59a36b803cf34ef01570c670363689e\",\"dweb:/ipfs/QmTkT5K2XcB3ZbPDqd4ZAfnZMp2reCzu3Pv7JpRqhAtZHP\"]},\"contracts/tokens/ERC4626DebtToken.sol\":{\"keccak256\":\"0xe69b1ed2fb7b2d7c24c6838462001988b8e51795d215cfa74b9874d17257509e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c4f201e5f5621689046c58863ab9270cf770c68810d52269d1fc2ac93a7ccf96\",\"dweb:/ipfs/QmdtALf6LQdHhce3HsNdVtomQu8e5F5QcYU6S7H1PeBThZ\"]},\"contracts/tokens/ERC4626DepositToken.sol\":{\"keccak256\":\"0xd914aa43dc5e9f2f02f98b05561faf6f00853b701f51dfcd7a08a31feaf220be\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8c2282d40855587b2ac70e89d4e0e147b9afe32a41245fffc96b923a9e5ce7ac\",\"dweb:/ipfs/QmVn3tBkZcXKnqjfnLTXFkKtu1EetDL1UF7rRjNrHdRCSM\"]},\"contracts/tokens/PluginRegistry.sol\":{\"keccak256\":\"0x9263d71fc32da7d0ca4f8d272f8d75d565c1f06281952481322983bae9d7b488\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c9dcbc64172f4339547865b4f041826f0de5d464900f316edbe72e7d6bfb396d\",\"dweb:/ipfs/QmQykSWuY8xLJotWUPgG5JQDS5DmA2E5Hjb4c6Bz4YnbBQ\"]},\"contracts/tokens/TokenController.sol\":{\"keccak256\":\"0x8b76b9ebb9385f0c4b7c0b8210fb96b11a49a8c9a3a6e855752c32a5c12d54e6\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://de87bdae81940f397136f665d68907d4e4c32f35bf2cd0c9e9305a9fe190d159\",\"dweb:/ipfs/Qmce4hM6xofBYxzAXesHX4hkiHBexoGeQpCzpeCARctnCn\"]},\"contracts/utils/deployHelper.sol\":{\"keccak256\":\"0x9b9dd84e234bb2ffbf51da7e9ab42fe7b6329acf38de7f042d4f8abd146182f0\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://d07ded7de8e48a25ac7b0442c6e455338bd16ee483a89ad7f37585ab91865a3b\",\"dweb:/ipfs/QmeBAuZgRJEXeuX6qsGt46sTLovKNC5Pue8xFxbHMPtiBR\"]},\"lib/1inch/solidity-utils/contracts/libraries/AddressArray.sol\":{\"keccak256\":\"0x7895eaf7b55d9612b22ec586970488de51436c021b9f9414b3c27c3583c8856e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e43897dfeff84e2983b017ab100d74061a6b9bed4618ec37a7cbb68bf064ac22\",\"dweb:/ipfs/Qmejoj4CuNr1giwGBDG7SgRtfYmi64fgy2DsGP2AAW9gH9\"]},\"lib/1inch/solidity-utils/contracts/libraries/AddressSet.sol\":{\"keccak256\":\"0xbb8e2a541ac268f00f382c9fba9403b3ec5b58a48dc7236920d7c87817f93318\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://614b60ff60f75c46aa41d00ec5dd78136d42e0d6035aa2331d16f26d2f5f5368\",\"dweb:/ipfs/QmUJWqcx2heKcmBhxpHfAnpKrwnevqtAwaQKT7Bmpke5NB\"]},\"lib/1inch/token-plugins/contracts/ERC20Hooks.sol\":{\"keccak256\":\"0xd2657f278b2ed4667663344aa06c02b52b15862c69c215570de329aa1a4683a2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://4ffb90146369b7fa890489a0d3ecab5aa81c9f47b3ec7a1776fd8bc14eaa33d5\",\"dweb:/ipfs/QmWo24VHySo9jQBeXfE87Z3Hh576cNKcwccLrBAMsfax1c\"]},\"lib/1inch/token-plugins/contracts/interfaces/IERC20Hooks.sol\":{\"keccak256\":\"0x2fb4fcbf91a7edf36e7ada3f578a8de1aee7ebdd12460648d3e09d4448351875\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d09a05543afcad3c0c2d2b9013647408cc44e1b17f362665ee20f9abed5964e8\",\"dweb:/ipfs/QmX3MMGsVJSfsrVoYY2jWK2BXd3i6scBhQQxytk461mizh\"]},\"lib/1inch/token-plugins/contracts/interfaces/IHook.sol\":{\"keccak256\":\"0x474893cc48ee17530ad0e978ecbccaa726943615a5736260187a37b8e133ee80\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://511ed52ec427cced459878f28453790817d4cbacc8601d77eb8dfa28e2e0b30d\",\"dweb:/ipfs/QmXvbB1bAZZanGepiUxWh3zZQUaHQZeYpR1UdaW4w5yKVS\"]},\"lib/1inch/token-plugins/contracts/libs/ReentrancyGuard.sol\":{\"keccak256\":\"0xa88ccab1ee6b34a9007986ca15ea5fd5580864029130eb38333183af1bc4f66c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b5ad18c862e59b0f24edc900519a55c7aee5537a42d039d6ea5e34df40267bb0\",\"dweb:/ipfs/QmVMRSjUEPxCno1sFdhWvpRqAqoqns8zwVyd7yCHYC6P8Z\"]},\"lib/ExcessivelySafeCall/src/ExcessivelySafeCall.sol\":{\"keccak256\":\"0x7d9d432e8f02168bf3f790e3dabcf36402782acf7ffa476cabe86fc4d8962eb2\",\"license\":\"MIT OR Apache-2.0\",\"urls\":[\"bzz-raw://1adc13e7f399f500ea5f81480ad149a50408fde7990a2c6347e6377486f389dc\",\"dweb:/ipfs/QmSvm5TUBJqknsqNJLLHqNS4MLSH5k3vNrbquVg6ZKSfx9\"]},\"lib/mangrove-core/lib/core/BitLib.sol\":{\"keccak256\":\"0x80f6885268986b9e976b424993aa875cf7aab8464403ed675a86ade9e9be5ee3\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5a31b4e1e0dc95de9a1dbb114c40c44814de5db3e2d857c93c2524a61454f6c8\",\"dweb:/ipfs/QmRkgE9ue5rGwE6XDnszF2e2meWqAC9nnKM97xKHjHphQr\"]},\"lib/morpho-blue/src/libraries/MathLib.sol\":{\"keccak256\":\"0xa7354cbbcecef7bc0c94b61061c4e5da75515056b8e2db65e826b00d7369744a\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://d7419c59bb906fcfa49320b68f265c3200090e5c30b194766256aee70b012e08\",\"dweb:/ipfs/Qmbo4uaW6XYnudya4bb6RU6riWXFk5M3CWJge5XzTTaEfd\"]},\"lib/openzeppelin-contracts/contracts/access/AccessControl.sol\":{\"keccak256\":\"0xb64ecf1154f183412bcde47168f3af245e4120846346a0b3872c631e361156d2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://85331049a60659bc4489733ccd3cbeb177b65691122a8cb637bf9267ab25e23d\",\"dweb:/ipfs/QmbGpDcdwKTirzSCoZfE4rHG7jBSWsE4K2iSb6UCYXtLJv\"]},\"lib/openzeppelin-contracts/contracts/access/IAccessControl.sol\":{\"keccak256\":\"0x5643a5cadd1278581308b20becb48a50946c159fc31c29fc407ea9a61fc865d1\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c7d79f305a239207a24fa2174897c8ea8ff1e81cb790d440fd54c89a0e85f63e\",\"dweb:/ipfs/QmT847eeAMnRN3DaG1zsKNMn7qipNAidqv1REnKexPkrfA\"]},\"lib/openzeppelin-contracts/contracts/access/Ownable.sol\":{\"keccak256\":\"0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6\",\"dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a\"]},\"lib/openzeppelin-contracts/contracts/governance/utils/IVotes.sol\":{\"keccak256\":\"0xb541b133f2d85eb37ae866cb21123be93bd3671b6840c47f951da52d3a704548\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://22443f43ece107eaf453aa9a41a59410cece5429c2779c794fbd2c8b5aa29d38\",\"dweb:/ipfs/Qmbum2jwLYuT9aZ2fr9NMLwWFsVavonrGm2VnbAL9hP2jn\"]},\"lib/openzeppelin-contracts/contracts/governance/utils/Votes.sol\":{\"keccak256\":\"0x3f91c79d6f55db9e4fc36e1cfe6a483a7b0f5be60fecbd979555071673746d47\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9b1e3c64cbeb2757a2a1a45c69f7f3984a93b0eadd1016341b64f9d94f89d7c4\",\"dweb:/ipfs/QmP1Mj14U4vMTFa2rv2nodMbWSCov2ac9Md8W2aUcgYdKX\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC1363.sol\":{\"keccak256\":\"0xc15298eb2b9ba5e18a8c9d12f93ad17a3e162a5c1d9b85f54c8adb5827b0d4da\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1f3c3d8f81d2daf1231890a6a2f897be365d6a479b53dcd52ec2527b5d3faf41\",\"dweb:/ipfs/QmeNdkd6u4at9pd2GAyyqxzrVGGvxfLpGmAKnFoYM5ya2e\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC165.sol\":{\"keccak256\":\"0x81b022028c39007cce9920c394b9cddd1cb9f3a1c0398f254b4a6492df92ad2b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e0b61b8a5c69b4df993c3d6f94c174ab293aa8698d149bce7be2d88f82929beb\",\"dweb:/ipfs/QmbtacmB1k8ginfrHvAJpjVeqnjYGfXYrkXmMPYEb83z4t\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC20.sol\":{\"keccak256\":\"0xb5d81383d40f4006d1ce4bbad0064e7a930e17302cbe2a745e09cb403f042733\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3fc4a5681c2f00f41f49260a36ae6bbe1121dd93d470ea24d51d556eff2980be\",\"dweb:/ipfs/QmUBW6TwVWtGP96ka9TfuGivd27kH8CtkXD8RQAAecSFiR\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC3156FlashBorrower.sol\":{\"keccak256\":\"0xad94c8d7246a50210f7bcb54e5b91fc9f1c6e137263ac972ca5dd0f7f6d4d49d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6938e96fbb0cf3d961788b6c3522400e255d8057d1b9f7e08a50e0b48486b007\",\"dweb:/ipfs/QmNXG3MPzDXjHJ9iWDYCz4vi9RBTgVBnZjndnfBwMfhkyD\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC3156FlashLender.sol\":{\"keccak256\":\"0xd92910b862581523ad4e9b99f0bf738f4e62700a5e305953c7fda7db2cfd0f73\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://799f3f0f30822ac806800bbe7fe63b9991490c4e1d9edb75f5993d9350320819\",\"dweb:/ipfs/QmT8T4SokW6YxpDJQiafpeYNRGtjC5gFHxRqKTRXRyP6zB\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC4626.sol\":{\"keccak256\":\"0x932fe72aeda629f70ee2ca902cfd8cce9faa0a13b39222c240979f42984c4541\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://4e9e0a320185800eb9a6d57ef0e4ccf3bb32a6b5dd44882f7552617c30d4a05e\",\"dweb:/ipfs/QmYJVpT7DDPWx3DWro8vtM6Gqre2AyufsyCYoHm9cfQ1vr\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC5267.sol\":{\"keccak256\":\"0xf29e9088951d8a2074d872a733674618fe5c164df21b8b5cf4a6295f523ba7ad\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://562a1abc7ea505582827ce0c9a2f778360a1a8242742683af179930640020215\",\"dweb:/ipfs/QmPjx5f6KKaPfsDi1uV3ovQN9gHTAcNkMAFJZxE1Adw6VT\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC5805.sol\":{\"keccak256\":\"0xc8960b7d3e504e98883de33856a917a473c05034cd61880df2a60b5c47c214fe\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://80542373fa695b68d65b4d7222e852df5bda035a82e86ee559336c93b2bf7051\",\"dweb:/ipfs/QmZgH14DPTnKfA5gMSTMiUa6ExuqFfAozmEtLXiWc1iDiw\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC6372.sol\":{\"keccak256\":\"0xa602c8beaae2d9e2ab1ce585a54547a6d4da32d32e4d002d20ccba55b19258d8\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ac6553b5b07788a0bb67cc53596837d795280233a9a5cb3a9b3e1fde56822f78\",\"dweb:/ipfs/QmVoHXoma4ZbPKVRJJRosvhipa4rtCMU9QQvWHWKiRUxvi\"]},\"lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol\":{\"keccak256\":\"0x85cf779582f7860b00bba1d259be00e754bfbac3c2339e324d0113d682d9e9f9\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://2ddf369affd106e2a9e2b8a22a6ce3da8a6ccda14c6ba5b8c87f6b08169e6318\",\"dweb:/ipfs/QmNadAttd47ycHShxhk33JUJhrbzmyZQ7mHs7WEyG4Qkmp\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol\":{\"keccak256\":\"0x5aad1745bddba11752c3962464a3b12e0af079310cc22d1f43f0388ae1aaf8db\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://577fad916bfdfe89aadf2685322fec7562cb0ed87722923085213cd9f85d7b79\",\"dweb:/ipfs/QmSM3J6PjrAUyEoNbdhq1ECZLXczKdCTzZTBUieKHsBYEL\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0xfe37358f223eddd65d61bb62b0b7bdb69d7101b5ec8d484292b8c1583a153b8a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://28dd43f30af3c12ae0fc08dd031b1250e906ef3c95f63f30fac6fd15aee2a662\",\"dweb:/ipfs/QmUkSyWsSRx36w1ti7U6qnGnQgJq16wpMhjeJrnyn9AXwG\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Burnable.sol\":{\"keccak256\":\"0x2659248df25e34000ed214b3dc8da2160bc39874c992b477d9e2b1b3283dc073\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c345af1b0e7ea28d1216d6a04ab28f5534a5229b9edf9ca3cd0e84950ae58d26\",\"dweb:/ipfs/QmY63jtSrYpLRe8Gj1ep2vMDCKxGNNG3hnNVKBVnrs2nmA\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20FlashMint.sol\":{\"keccak256\":\"0x4d43ed4b9ff9e4c671274976d59a58dbcc7b69bd7ac11b1710f5b7607cf15b74\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0b47b42998f675cb6a51f2e74ef5906a6fa63ec6718f3fd56ee035d6f77143f9\",\"dweb:/ipfs/QmREnAXqPJBvAwfWfDzaFhNfSRWF4Jdy9ZrpHLw1KdQweY\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Permit.sol\":{\"keccak256\":\"0x6485b101d7335f0fd25abc996c5e2fc965e72e5fbd0a7ad1a465bd3f012b5fd8\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1f8d296121044e697bcd34c4cd394cebd46fc30b04ce94fccff37200872e6834\",\"dweb:/ipfs/QmTNdmLdoHgMzoCDZ8Txk9aYvwtuyeJYHf5mjLWgzGTZAu\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Votes.sol\":{\"keccak256\":\"0x62dc9346044aabf22d78541bd495aa6ca05a7f5100aed26196ba35d40b59fcb5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5221df4501c74cd4493fee1a0f0788e02c4dc78c3c601e9f557f557c5a53ea92\",\"dweb:/ipfs/QmZpzyYY9dKLrgvYhXSHT93jwqb1UGvtGNMQk5dpECY5pa\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC4626.sol\":{\"keccak256\":\"0x0d99c706d010fa15de36e7e7b7a03dd0fdc9bcec52f9f812ef80ec7f3fc6fa63\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://bee73c81a2964e8da0537de14082e60d64cd7b1cd9162adc04b58317e334c896\",\"dweb:/ipfs/QmbQ75T9PEJuiLk1kypX68rEBFtTaEzPWsy8Dv99buqVPH\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol\":{\"keccak256\":\"0xbaffa0bcc92adf28a53cc3b68551fc3632cb8f849a0028cb8d5c06e4677715e9\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://32e6f8f6b2e883c85e6a602c0882d9962ce2f92406961244e86cd974df815912\",\"dweb:/ipfs/Qmahvx6fPpecicq1aUE1JihCxV5ep1bfuPukzrxa8Ub5PS\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Permit.sol\":{\"keccak256\":\"0x093f32ab700c2b05373387263915a75f5455cdb0f09a7630cc621e27b7b50d04\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d163e6ef21df143969df5557305e8c643a135c7660a678d0c65dca91772114a0\",\"dweb:/ipfs/QmTZUgiwEro5oLRhbJ2iSWyCqu1JTDekoFHALVUn4eHqYK\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol\":{\"keccak256\":\"0x1183b415155c1a7bf56d45edad5b17caf0da70935ac420698cbe8afb6750cbb2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://21d9edaeb3e5e8f93eb0fdab41530654e8169b1990b3bbfcf5e4527c52aa03f5\",\"dweb:/ipfs/QmWrqpNW3x5k3pTjvrT8XU1hauHnXTjqaPL2tfzMuWYosj\"]},\"lib/openzeppelin-contracts/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]},\"lib/openzeppelin-contracts/contracts/utils/Nonces.sol\":{\"keccak256\":\"0x0082767004fca261c332e9ad100868327a863a88ef724e844857128845ab350f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://132dce9686a54e025eb5ba5d2e48208f847a1ec3e60a3e527766d7bf53fb7f9e\",\"dweb:/ipfs/QmXn1a2nUZMpu2z6S88UoTfMVtY2YNh86iGrzJDYmMkKeZ\"]},\"lib/openzeppelin-contracts/contracts/utils/Panic.sol\":{\"keccak256\":\"0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a\",\"dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG\"]},\"lib/openzeppelin-contracts/contracts/utils/Pausable.sol\":{\"keccak256\":\"0xdb484371dfbb848cb6f5d70464e9ac9b2900e4164ead76bbce4fef0b44bcc68f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f9d6f6f6600a2bec622f699081b58350873b5e63ce05464d17d674a290bb8a7c\",\"dweb:/ipfs/QmQKVzSQY1PM3Bid4QhgVVZyx6B4Jx7XgaQzLKHj38vJz8\"]},\"lib/openzeppelin-contracts/contracts/utils/ShortStrings.sol\":{\"keccak256\":\"0x1fcf8cceb1a67e6c8512267e780933c4a3f63ef44756e6c818fda79be51c8402\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://617d7d57f6f9cd449068b4d23daf485676d083aae648e038d05eb3a13291de35\",\"dweb:/ipfs/QmPADWPiGaSzZDFNpFEUx4ZPqhzPkYncBpHyTfAGcfsqzy\"]},\"lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol\":{\"keccak256\":\"0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b\",\"dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM\"]},\"lib/openzeppelin-contracts/contracts/utils/Strings.sol\":{\"keccak256\":\"0x1402d9ac66fbca0a2b282cd938f01f3cd5fb1e4c696ed28b37839401674aef52\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d3e6c46b6d1ea36bd73e0ac443a53504089167b98baa24923d702a865a38d211\",\"dweb:/ipfs/QmdutUpr5KktmvgtqG2v96Bo8nVKLJ3PgPedxbsRD42CuQ\"]},\"lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol\":{\"keccak256\":\"0x6c29257484c0595ca5af8844fafe99cc5eace7447c9f5bced71d6b3a19a6a2a5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://cce7ac0bdb05f73c0918e362dea2e52426e00ddf0a1018f14accdcf78c6eb6e4\",\"dweb:/ipfs/QmbkNq5dDxww27FzFFiKgW3S7C5VoZpjdZGpSCtsb9hP32\"]},\"lib/openzeppelin-contracts/contracts/utils/cryptography/EIP712.sol\":{\"keccak256\":\"0xda8013da608bda3c9eaa9e59053d38d7888e64bb40aa557e5929cd702f8de87e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3ea13234c6b00ae79dc1a98e7e7f2faf38d37e76a687ccd0c95ad84b03ea570f\",\"dweb:/ipfs/QmWtdefDm5jiEzAjmfPMZ5B1NKVxFoMiD5ZoD68hcNTHun\"]},\"lib/openzeppelin-contracts/contracts/utils/cryptography/MessageHashUtils.sol\":{\"keccak256\":\"0x26670fef37d4adf55570ba78815eec5f31cb017e708f61886add4fc4da665631\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b16d45febff462bafd8a5669f904796a835baf607df58a8461916d3bf4f08c59\",\"dweb:/ipfs/QmU2eJFpjmT4vxeJWJyLeQb8Xht1kdB8Y6MKLDPFA9WPux\"]},\"lib/openzeppelin-contracts/contracts/utils/introspection/ERC165.sol\":{\"keccak256\":\"0x41ddfafe0d00dc22e35119d41cb0ca93673960689d35710fd12875139e64bd9f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://49d90142e15cdc4ca00de16e1882fa0a0daad8b46403628beb90c67a3efe4fc4\",\"dweb:/ipfs/QmNizYnFNcGixHxsknEccr2cQWyyQBqFF7h2bXLmefQz6M\"]},\"lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol\":{\"keccak256\":\"0x4ee0e04cc52827588793a141d5efb9830f179a17e80867cc332b3a30ceb30fd9\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://17d8f47fce493b34099ed9005c5aee3012488f063cfe1c34ed8f9e6fc3d576e5\",\"dweb:/ipfs/QmZco2GbZZhEMvG3BovyoGMAFKvfi2LhfNGQLn283LPrXf\"]},\"lib/openzeppelin-contracts/contracts/utils/math/Math.sol\":{\"keccak256\":\"0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3\",\"dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB\"]},\"lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol\":{\"keccak256\":\"0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8\",\"dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy\"]},\"lib/openzeppelin-contracts/contracts/utils/math/SignedMath.sol\":{\"keccak256\":\"0xb1970fac7b64e6c09611e6691791e848d5e3fe410fa5899e7df2e0afd77a99e3\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://db5fbb3dddd8b7047465b62575d96231ba8a2774d37fb4737fbf23340fabbb03\",\"dweb:/ipfs/QmVUSvooZKEdEdap619tcJjTLcAuH6QBdZqAzWwnAXZAWJ\"]},\"lib/openzeppelin-contracts/contracts/utils/structs/Checkpoints.sol\":{\"keccak256\":\"0x743aa2d21f6c26885e0aa6a1c84f7f7bc58fbd6df6bab32bed23f1a41f50454a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://a651d38b4261840d3744e571edf2b59455352a8c7dac5d35b019afefa343ea3b\",\"dweb:/ipfs/QmSy3UkTCQDYTjKtGwtqPRrXaofcqtVZxaF6j1dV44wqvr\"]},\"lib/openzeppelin-contracts/contracts/utils/types/Time.sol\":{\"keccak256\":\"0x36776530f012618bc7526ceb28e77b85e582cb12d9b9466a71d4bd6bf952e4cc\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9f867d046908497287d8a67643dd5d7e38c4027af4ab0a74ffbe1d6790c383c6\",\"dweb:/ipfs/QmQ7s9gMP1nkwThFmoDifnGgpUMsMe5q5ZrAxGDsNnRGza\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/Base.sol\":{\"keccak256\":\"0x4ff1a785311017d1eedb1b4737956fa383067ad34eb439abfec1d989754dde1c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f553622969b9fdb930246704a4c10dfaee6b1a4468c142fa7eb9dc292a438224\",\"dweb:/ipfs/QmcxqHnqdQsMVtgsfH9VNLmZ3g7GhgNagfq7yvNCDcCHFK\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdAssertions.sol\":{\"keccak256\":\"0x4584f551c3a875c26423c1e50a77179bc2f9c9c1ee6d0f0c6be0f89ca5ee8270\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ef118876a862b60ba8a6612b8b3f3a6c226e11a0071a2e9695f021586d1bfdbe\",\"dweb:/ipfs/QmXd2vD91PkvoFWasFkgbDi89PctmgsHVxGvGNyhjmRK2b\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdChains.sol\":{\"keccak256\":\"0xcd3e64ec9ffa19a2c0715bbdaf7ddf28887cc418e079bec4373fd6a3f9961a7b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e981a2ab738590928e9efa5f3d95a408c718eb12d73a113d7675f3ed55a026a1\",\"dweb:/ipfs/QmTgSEkWWsBRy32goRCaUkraSgpZHtgbZoKC3iEFNz5RDc\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdCheats.sol\":{\"keccak256\":\"0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41\",\"dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdError.sol\":{\"keccak256\":\"0xbf477b11a42d7611696956546bcfaa29317d1166bf65e402344599c05943fc77\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://bc2e117d1135e030862b96a6526a43feb38d396cc79857f1fb696d4eff0e5fd6\",\"dweb:/ipfs/QmdSuQ5RrQudTLsNmWXGEeVJX8gR5U9XPm6m4dwwuQnJrj\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdInvariant.sol\":{\"keccak256\":\"0x4dab3021edfa9511bbdd80c48f060ef62eaf457f99eaf841f561fc2557d9a08d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://07668628673174cf8f27f8f4e1f862bab775013ec247eb34f698c5005f229391\",\"dweb:/ipfs/QmPJsiiYwmCZXMsHmQv5tg8VF3CAhNdat7WnKLTWZZH2v5\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdJson.sol\":{\"keccak256\":\"0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974\",\"dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdMath.sol\":{\"keccak256\":\"0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92\",\"dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdStorage.sol\":{\"keccak256\":\"0xb91ab24383a5872b894fc93325eef1add6cbbf981628f18e860068bf88bb7dcc\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://2651e33b9ac8fc5803ed0a43078c1bf4fa90a0b0347aafd95776b02daccdcc57\",\"dweb:/ipfs/QmbdRyPuGJdZgnCuMnp7c1WsBo1Spf7j1KMmws1Z5rd4BQ\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdStyle.sol\":{\"keccak256\":\"0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8\",\"dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdToml.sol\":{\"keccak256\":\"0x58a72c765ed3f7ff6b105509689658795b8a3739b8931772a497155878381861\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b4a3746f4fabaeb980bd77d9e091d3904ee38a6c0e191bfa8ba6874c6f8558a3\",\"dweb:/ipfs/QmUfFDMEn461FgGEXt5HicyGD54sc28sLaQ9JRWDMBKed8\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdUtils.sol\":{\"keccak256\":\"0x7274081e11c05164fd8eadde4de8305c033e58a43008dea58065f3170ccf0737\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e9801614b6c9d3e472982e6cc68f5f1ad03682f84eafb686be65633c7132d138\",\"dweb:/ipfs/QmcQSUcrm2A7XuektnxJjvYGmZtBeA6LKPxCXRqdXBX776\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/Test.sol\":{\"keccak256\":\"0x3b4bb409a156dee9ce261458117fe9f81080ca844a8a26c07c857c46d155effe\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5792c69fe24bdc063a14e08fe68275007fdb1e5e7e343840a77938cb7e95a64e\",\"dweb:/ipfs/QmcAMhaurUwzhytJFYix4vRNeZeV8g27b8LnV3t7dvYtiK\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/Vm.sol\":{\"keccak256\":\"0x44bfadcf5a89b8058f80258f2259585c740f9cc45669a0579f4f2753ff2c6354\",\"license\":\"MIT OR Apache-2.0\",\"urls\":[\"bzz-raw://bbc366c8b3499d5030e3b2e45bac23770531f2f5243a0e80e3d5a66b6f9a312c\",\"dweb:/ipfs/QmNxDEB3BaVnKzNaWedtdMshhvCEddB1AsdJZcsQx6jdtC\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/console.sol\":{\"keccak256\":\"0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57\",\"dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/console2.sol\":{\"keccak256\":\"0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d\",\"dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/interfaces/IMulticall3.sol\":{\"keccak256\":\"0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0\",\"dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/safeconsole.sol\":{\"keccak256\":\"0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab\",\"dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3\"]},\"test/InterestTests/InterestFixture.sol\":{\"keccak256\":\"0x458f1f72b1417a73ecdea81c25b269592e95c1808ca6aaa6b60a25243e143ed3\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://abeb9b791b75f44d48f898182c673d80ea1c0f513fffe48a6834fdebebc6fdbe\",\"dweb:/ipfs/QmU92joERfyZJaTonAknmtRBkTjs5Jb7S2zM8Zk1XAnhwj\"]},\"test/example/PeripheralDelegationContractExample.sol\":{\"keccak256\":\"0xf212fd0b2dd3a358c826623bd320e3aa0630892b9f6ba777b8126d3e2cfcfb14\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://2ad79b2d7eb1b46c69383b9e9090bf2031ff779e46637d850b881db3dc84d797\",\"dweb:/ipfs/QmTPx8qw1zdYXRzjBnmuzMCt8yiErwFiLBk47xnbTm1erP\"]},\"test/shared/FactoryPairTestFixture.sol\":{\"keccak256\":\"0x62487d7b3402461a61bd0c99be82302c8c1a94533c525a0ff6bcfe888af730a5\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://dfd509aaec3469ed23d1cda8c6f603b7f0163fc29ec4ba6e4b06b60ca8fdc042\",\"dweb:/ipfs/QmTPDPM7kt77VNWr61MVZGmNZp67RG8jKzdmz7zwWep4GE\"]},\"test/shared/StubErc20.sol\":{\"keccak256\":\"0xf3508dc98ae444d142d9993c52cebd856aba40c3e53d64bfeb63e71d190b12ee\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0cc01d254b6d5569d1cb426250db9df1b01afde9dd7b52e1efa0691112fcd918\",\"dweb:/ipfs/QmPnL9wFpSKXprrEFS9kkC2WzK2kAgWSH1snom1wiorCxn\"]},\"test/shared/utilities.sol\":{\"keccak256\":\"0xc64b147bbe73bf59fdec4202c5b7c5dbcadd7550f4b2ea2390ea689e194d7cb8\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://ab03a14b75d4b9df7795eeefd7e6d4a1d7af7b58ce948741cdd5d056a2c30416\",\"dweb:/ipfs/QmShemddxGaLyTGtC3yLdMtdHf9Gj3H8rjf2umzbFmP6aG\"]},\"test/utils/DepletedAssetUtils.sol\":{\"keccak256\":\"0x2273187d5eb782fb341d44265bd6e8afcef18ab3cfabcb4a0b77a75f15298c42\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://04d0a060b217603f6c7e38efc49be20932f08c56e46b1f9cea54eb722595458e\",\"dweb:/ipfs/QmdJHgaFbbtGDHPpHEFucTvrj4p4LT1piMPjtbrWBMXzAR\"]},\"test/utils/constants.sol\":{\"keccak256\":\"0xe7d13ea4f26a2c43b7beed68c83a0e36555ead8f6bfd181430c74f853546fc34\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://5098f47b615afa3d6489c2c8c2576f6202601fb15b1f32e6900639986e44f1fd\",\"dweb:/ipfs/QmPU1Ejtv4yY7eqjW1SpVgvS8vMqwyEjMeNGCLax3Mwk9d\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "type": "error", "name": "AmmalgamCannotBorrowAgainstSameCollateral"}, {"inputs": [], "type": "error", "name": "AmmalgamDepositIsNotStrictlyBigger"}, {"inputs": [], "type": "error", "name": "AmmalgamLTV"}, {"inputs": [], "type": "error", "name": "AmmalgamMaxBorrowReached"}, {"inputs": [], "type": "error", "name": "AmmalgamMaxSlippage"}, {"inputs": [], "type": "error", "name": "AmmalgamTooMuchLeverage"}, {"inputs": [], "type": "error", "name": "Forbidden"}, {"inputs": [], "type": "error", "name": "InsufficientInputAmount"}, {"inputs": [], "type": "error", "name": "InsufficientLiquidity"}, {"inputs": [], "type": "error", "name": "InsufficientLiquidity"}, {"inputs": [], "type": "error", "name": "InsufficientLiquidityBurned"}, {"inputs": [], "type": "error", "name": "InsufficientLiquidityMinted"}, {"inputs": [], "type": "error", "name": "InsufficientOutputAmount"}, {"inputs": [], "type": "error", "name": "InsufficientRepayLiquidity"}, {"inputs": [], "type": "error", "name": "InvalidToAddress"}, {"inputs": [], "type": "error", "name": "K"}, {"inputs": [], "type": "error", "name": "Locked"}, {"inputs": [], "type": "error", "name": "NotEnoughRepaidForLiquidation"}, {"inputs": [], "type": "error", "name": "Overflow"}, {"inputs": [], "type": "error", "name": "PriceOutOfBounds"}, {"inputs": [{"internalType": "uint8", "name": "bits", "type": "uint8"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "type": "error", "name": "SafeCastOverflowedUintDowncast"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "type": "error", "name": "SafeERC20FailedOperation"}, {"inputs": [], "type": "error", "name": "TickOutOfBounds"}, {"inputs": [{"internalType": "address", "name": "borrower", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "tokenType", "type": "uint256", "indexed": true}, {"internalType": "uint256", "name": "badDebtAssets", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "badDebtShares", "type": "uint256", "indexed": false}], "type": "event", "name": "BurnBadDebt", "anonymous": false}, {"inputs": [{"internalType": "uint128", "name": "depositLAssets", "type": "uint128", "indexed": false}, {"internalType": "uint128", "name": "depositXAssets", "type": "uint128", "indexed": false}, {"internalType": "uint128", "name": "depositYAssets", "type": "uint128", "indexed": false}, {"internalType": "uint128", "name": "borrowLAssets", "type": "uint128", "indexed": false}, {"internalType": "uint128", "name": "borrowXAssets", "type": "uint128", "indexed": false}, {"internalType": "uint128", "name": "borrowYAssets", "type": "uint128", "indexed": false}], "type": "event", "name": "InterestAccrued", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "borrower", "type": "address", "indexed": true}, {"internalType": "address", "name": "to", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "depositL", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "depositX", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "depositY", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "repayLX", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "repayLY", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "repayX", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "repayY", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "liquidationType", "type": "uint256", "indexed": false}], "type": "event", "name": "Liquidate", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "amountXIn", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "amountYIn", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "amountXOut", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "amountYOut", "type": "uint256", "indexed": false}, {"internalType": "address", "name": "to", "type": "address", "indexed": true}], "type": "event", "name": "<PERSON><PERSON><PERSON>", "anonymous": false}, {"inputs": [{"internalType": "uint256", "name": "reserveXAssets", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "reserveYAssets", "type": "uint256", "indexed": false}], "type": "event", "name": "Sync", "anonymous": false}, {"inputs": [{"internalType": "uint112", "name": "externalLiquidity", "type": "uint112", "indexed": false}], "type": "event", "name": "UpdateExternalLiquidity", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "", "type": "string", "indexed": false}], "type": "event", "name": "log", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "", "type": "address", "indexed": false}], "type": "event", "name": "log_address", "anonymous": false}, {"inputs": [{"internalType": "uint256[]", "name": "val", "type": "uint256[]", "indexed": false}], "type": "event", "name": "log_array", "anonymous": false}, {"inputs": [{"internalType": "int256[]", "name": "val", "type": "int256[]", "indexed": false}], "type": "event", "name": "log_array", "anonymous": false}, {"inputs": [{"internalType": "address[]", "name": "val", "type": "address[]", "indexed": false}], "type": "event", "name": "log_array", "anonymous": false}, {"inputs": [{"internalType": "bytes", "name": "", "type": "bytes", "indexed": false}], "type": "event", "name": "log_bytes", "anonymous": false}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32", "indexed": false}], "type": "event", "name": "log_bytes32", "anonymous": false}, {"inputs": [{"internalType": "int256", "name": "", "type": "int256", "indexed": false}], "type": "event", "name": "log_int", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "address", "name": "val", "type": "address", "indexed": false}], "type": "event", "name": "log_named_address", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "uint256[]", "name": "val", "type": "uint256[]", "indexed": false}], "type": "event", "name": "log_named_array", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "int256[]", "name": "val", "type": "int256[]", "indexed": false}], "type": "event", "name": "log_named_array", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "address[]", "name": "val", "type": "address[]", "indexed": false}], "type": "event", "name": "log_named_array", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "bytes", "name": "val", "type": "bytes", "indexed": false}], "type": "event", "name": "log_named_bytes", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "bytes32", "name": "val", "type": "bytes32", "indexed": false}], "type": "event", "name": "log_named_bytes32", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "int256", "name": "val", "type": "int256", "indexed": false}, {"internalType": "uint256", "name": "decimals", "type": "uint256", "indexed": false}], "type": "event", "name": "log_named_decimal_int", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "uint256", "name": "val", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "decimals", "type": "uint256", "indexed": false}], "type": "event", "name": "log_named_decimal_uint", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "int256", "name": "val", "type": "int256", "indexed": false}], "type": "event", "name": "log_named_int", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "string", "name": "val", "type": "string", "indexed": false}], "type": "event", "name": "log_named_string", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "uint256", "name": "val", "type": "uint256", "indexed": false}], "type": "event", "name": "log_named_uint", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "", "type": "string", "indexed": false}], "type": "event", "name": "log_string", "anonymous": false}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256", "indexed": false}], "type": "event", "name": "log_uint", "anonymous": false}, {"inputs": [{"internalType": "bytes", "name": "", "type": "bytes", "indexed": false}], "type": "event", "name": "logs", "anonymous": false}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "IS_TEST", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amountXAssets", "type": "uint256"}, {"internalType": "uint256", "name": "amountYAssets", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "borrow"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "borrowAmountLAssets", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "borrowLiquidity", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "burn", "outputs": [{"internalType": "uint256", "name": "amountXAssets", "type": "uint256"}, {"internalType": "uint256", "name": "amountYAssets", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "deposit"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeArtifacts", "outputs": [{"internalType": "string[]", "name": "excludedArtifacts_", "type": "string[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeContracts", "outputs": [{"internalType": "address[]", "name": "excludedContracts_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeSelectors", "outputs": [{"internalType": "struct StdInvariant.FuzzSelector[]", "name": "excludedSelectors_", "type": "tuple[]", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "bytes4[]", "name": "selectors", "type": "bytes4[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeSenders", "outputs": [{"internalType": "address[]", "name": "excludedSenders_", "type": "address[]"}]}, {"inputs": [{"internalType": "uint256", "name": "tokenType", "type": "uint256"}, {"internalType": "uint128", "name": "amount", "type": "uint128"}], "stateMutability": "nonpayable", "type": "function", "name": "exposed_accrueInterestToToken"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "exposed_activeLiquidityAssets", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "exposed_externalLiquidity", "outputs": [{"internalType": "uint112", "name": "", "type": "uint112"}]}, {"inputs": [{"internalType": "uint256", "name": "tokenType", "type": "uint256"}, {"internalType": "address", "name": "user", "type": "address"}], "stateMutability": "view", "type": "function", "name": "exposed_getAssets", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "stateMutability": "view", "type": "function", "name": "exposed_getAssets", "outputs": [{"internalType": "uint256[6]", "name": "", "type": "uint256[6]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "exposed_getDepositAndBorrowAndActiveLiquidityAssets", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "int56", "name": "lastLendingCumulativeSum", "type": "int56"}, {"internalType": "uint256", "name": "duration", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "exposed_getLendingStateTick", "outputs": [{"internalType": "int16", "name": "lendingStateTick", "type": "int16"}]}, {"inputs": [{"internalType": "int16", "name": "currentTick", "type": "int16"}], "stateMutability": "view", "type": "function", "name": "exposed_getTickRange", "outputs": [{"internalType": "int16", "name": "", "type": "int16"}, {"internalType": "int16", "name": "", "type": "int16"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "exposed_getTickRange", "outputs": [{"internalType": "int16", "name": "", "type": "int16"}, {"internalType": "int16", "name": "", "type": "int16"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "exposed_getTickRangeWithoutLongTerm", "outputs": [{"internalType": "int16", "name": "", "type": "int16"}, {"internalType": "int16", "name": "", "type": "int16"}]}, {"inputs": [{"internalType": "int16", "name": "currentTick", "type": "int16"}], "stateMutability": "view", "type": "function", "name": "exposed_getTickRangeWithoutLongTerm", "outputs": [{"internalType": "int16", "name": "", "type": "int16"}, {"internalType": "int16", "name": "", "type": "int16"}]}, {"inputs": [{"internalType": "uint256", "name": "tokenType", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "exposed_getTotalAssetsCached", "outputs": [{"internalType": "uint256", "name": "tokenTotalAssets", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "exposed_missingAssets", "outputs": [{"internalType": "uint112", "name": "", "type": "uint112"}, {"internalType": "uint112", "name": "", "type": "uint112"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "exposed_observations", "outputs": [{"internalType": "struct GeometricTWAP.Observations", "name": "", "type": "tuple", "components": [{"internalType": "bool", "name": "isMidTermBufferInitialized", "type": "bool"}, {"internalType": "bool", "name": "isLongTermBufferInitialized", "type": "bool"}, {"internalType": "uint8", "name": "midTermIndex", "type": "uint8"}, {"internalType": "uint8", "name": "longTermIndex", "type": "uint8"}, {"internalType": "int16", "name": "lastTick", "type": "int16"}, {"internalType": "int16", "name": "lastLendingStateTick", "type": "int16"}, {"internalType": "uint24", "name": "midTermIntervalConfig", "type": "uint24"}, {"internalType": "uint24", "name": "longTermIntervalConfig", "type": "uint24"}, {"internalType": "int56", "name": "lendingCumulativeSum", "type": "int56"}, {"internalType": "int56[51]", "name": "midTermCumulativeSum", "type": "int56[51]"}, {"internalType": "int56[9]", "name": "longTermCumulativeSum", "type": "int56[9]"}, {"internalType": "uint32[51]", "name": "midTermTimeInterval", "type": "uint32[51]"}, {"internalType": "uint32[9]", "name": "longTermTimeInterval", "type": "uint32[9]"}]}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "exposed_resetTotalAssetsCached"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "exposed_saturationAndGeometricTWAPState", "outputs": [{"internalType": "contract ISaturationAndGeometricTWAPState", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "uint256", "name": "_referenceReserveX", "type": "uint256"}, {"internalType": "uint256", "name": "_referenceReserveY", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "exposed_setReferenceReserves"}, {"inputs": [{"internalType": "uint256", "name": "tokenType", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "exposed_sharesToAssetsScaler", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "exposed_totalSat", "outputs": [{"internalType": "uint256", "name": "totalSatNetX", "type": "uint256"}, {"internalType": "uint256", "name": "totalSatNetY", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "externalLiquidity", "outputs": [{"internalType": "uint112", "name": "", "type": "uint112"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "failed", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getReserves", "outputs": [{"internalType": "uint112", "name": "_reserveXAssets", "type": "uint112"}, {"internalType": "uint112", "name": "_reserveYAssets", "type": "uint112"}, {"internalType": "uint32", "name": "_lastTimestamp", "type": "uint32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getTickRange", "outputs": [{"internalType": "int16", "name": "minTick", "type": "int16"}, {"internalType": "int16", "name": "maxTick", "type": "int16"}]}, {"inputs": [{"internalType": "address", "name": "borrower", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "depositLToBeTransferredInLAssets", "type": "uint256"}, {"internalType": "uint256", "name": "depositXToBeTransferredInXAssets", "type": "uint256"}, {"internalType": "uint256", "name": "depositYToBeTransferredInYAssets", "type": "uint256"}, {"internalType": "uint256", "name": "repayLXInXAssets", "type": "uint256"}, {"internalType": "uint256", "name": "repayLYInYAssets", "type": "uint256"}, {"internalType": "uint256", "name": "repayXInXAssets", "type": "uint256"}, {"internalType": "uint256", "name": "repayYInYAssets", "type": "uint256"}, {"internalType": "uint256", "name": "liquidationType", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "liquidate"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "mint", "outputs": [{"internalType": "uint256", "name": "liquidityShares", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "referenceReserves", "outputs": [{"internalType": "uint112", "name": "", "type": "uint112"}, {"internalType": "uint112", "name": "", "type": "uint112"}]}, {"inputs": [{"internalType": "address", "name": "onBehalfOf", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "repay", "outputs": [{"internalType": "uint256", "name": "repayXInXAssets", "type": "uint256"}, {"internalType": "uint256", "name": "repayYInYAssets", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "onBehalfOf", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "repayLiquidity", "outputs": [{"internalType": "uint256", "name": "repaidLXInXAssets", "type": "uint256"}, {"internalType": "uint256", "name": "repaidLYInYAssets", "type": "uint256"}, {"internalType": "uint256", "name": "repayLiquidityAssets", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "skim"}, {"inputs": [{"internalType": "uint256", "name": "amountXOut", "type": "uint256"}, {"internalType": "uint256", "name": "amountYOut", "type": "uint256"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "swap"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "sync"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetArtifactSelectors", "outputs": [{"internalType": "struct StdInvariant.FuzzArtifactSelector[]", "name": "targetedArtifactSelectors_", "type": "tuple[]", "components": [{"internalType": "string", "name": "artifact", "type": "string"}, {"internalType": "bytes4[]", "name": "selectors", "type": "bytes4[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetArtifacts", "outputs": [{"internalType": "string[]", "name": "targetedArtifacts_", "type": "string[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetContracts", "outputs": [{"internalType": "address[]", "name": "targetedContracts_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetInterfaces", "outputs": [{"internalType": "struct StdInvariant.FuzzInterface[]", "name": "targetedInterfaces_", "type": "tuple[]", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "string[]", "name": "artifacts", "type": "string[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetSelectors", "outputs": [{"internalType": "struct StdInvariant.FuzzSelector[]", "name": "targetedSelectors_", "type": "tuple[]", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "bytes4[]", "name": "selectors", "type": "bytes4[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetSenders", "outputs": [{"internalType": "address[]", "name": "targetedSenders_", "type": "address[]"}]}, {"inputs": [{"internalType": "uint256", "name": "tokenType", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "tokens", "outputs": [{"internalType": "contract IAmmalgamERC20", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "totalAssets", "outputs": [{"internalType": "uint128[6]", "name": "", "type": "uint128[6]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "underlyingTokens", "outputs": [{"internalType": "contract IERC20", "name": "", "type": "address"}, {"internalType": "contract IERC20", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "uint112", "name": "_externalLiquidity", "type": "uint112"}], "stateMutability": "nonpayable", "type": "function", "name": "updateExternalLiquidity"}, {"inputs": [{"internalType": "address", "name": "validate", "type": "address"}, {"internalType": "address", "name": "update", "type": "address"}, {"internalType": "bool", "name": "isBorrow", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "validateOnUpdate"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "withdraw"}], "devdoc": {"kind": "dev", "methods": {"borrow(address,uint256,uint256,bytes)": {"details": "Verifies the borrowing amounts, mints corresponding debt tokens, transfers the assets, and updates missing assets. Also supports flash loan interactions.", "params": {"amountXAssets": "Amount of asset X to borrow.", "amountYAssets": "Amount of asset Y to borrow.", "data": "Call data to be sent to external contract if flash loan interaction is desired.", "to": "Address to which the borrowed assets will be transferred."}}, "burn(address)": {"details": "Calculates the amounts of assets to be returned based on liquidity.      Requires amountXAssets and amountYAssets to be greater than 0.      Emits a #Burn event and performs a safe transfer of assets.", "params": {"to": "address to which the underlying assets will be transferred"}, "returns": {"amountXAssets": "amount of first token to be returned", "amountYAssets": "amount of second token to be returned"}}, "deposit(address)": {"details": "Verifies deposit amounts and types, adjusts reserves if necessary, mints corresponding tokens, and updates missing assets.", "params": {"to": "Address to which tokens will be minted."}}, "exposed_getTickRange(int16)": {"details": "Returns the minimum and maximum tick values that define a tick range.", "params": {"currentTick": "The current (most recent) tick based on the current reserves."}, "returns": {"_0": "The minimum tick value among current, mid-term, and long-term ticks.", "_1": "The maximum tick value among current, mid-term, and long-term ticks."}}, "exposed_observations()": {"details": "Returns the GeometricTWAP Observation struct.", "returns": {"_0": "observations A struct containing the current observations data."}}, "getReserves()": {"returns": {"_lastTimestamp": "The timestamp of the last operation.", "_reserveXAssets": "The current reserve of asset X.", "_reserveYAssets": "The current reserve of asset Y."}}, "liquidate(address,address,uint256,uint256,uint256,uint256,uint256,uint256,uint256,uint256)": {"params": {"borrower": "The account being liquidated", "depositLToBeTransferredInLAssets": "The amount of L to be transferred to the liquidator.", "depositXToBeTransferredInXAssets": "The amount of X to be transferred to the liquidator.", "depositYToBeTransferredInYAssets": "The amount of Y to be transferred to the liquidator.", "liquidationType": "The type of liquidation to be performed: HARD, SOFT, LEVERAGE", "repayLXInXAssets": "The amount of LX to be repaid by the liquidator.", "repayLYInYAssets": "The amount of LY to be repaid by the liquidator.", "repayXInXAssets": "The amount of X to be repaid by the liquidator.", "repayYInYAssets": "The amount of Y to be repaid by the liquidator.", "to": "The account to send the liquidated deposit to"}}, "mint(address)": {"details": "Calculates the amount of tokens to mint based on reserves and balances. Requires liquidity > 0. Emits a #Mint event.", "params": {"to": "address to which tokens will be minted"}, "returns": {"liquidityShares": "amount of tokens minted"}}, "referenceReserves()": {"returns": {"_0": "The reference reserve for asset X.", "_1": "The reference reserve for asset Y."}}, "repay(address)": {"details": "<PERSON> corresponding borrowed tokens, adjusts the reserves, and updates missing assets.", "params": {"onBehalfOf": "Address of the entity on whose behalf the repayment is made."}, "returns": {"repayXInXAssets": "Amount of token X repaid", "repayYInYAssets": "Amount of token Y repaid"}}, "repayLiquidity(address)": {"details": "Calculates repayable liquidity, burns corresponding tokens, adjusts reserves, and updates active liquidity.", "params": {"onBehalfOf": "Address of the entity on whose behalf the liquidity repayment is made."}, "returns": {"repaidLXInXAssets": "Amount of liquidity repaid in X.", "repaidLYInYAssets": "Amount of liquidity repaid in Y.", "repayLiquidityAssets": "Amount of liquidity repaid in L."}}, "skim(address)": {"details": "Calculates the excess of tokenX and tokenY balances and transfers them to the specified address.", "params": {"to": "The address to which the excess tokens are transferred."}}, "swap(uint256,uint256,address,bytes)": {"details": "Requires at least one of `amountXOut` and `amountYOut` to be greater than 0,      and that the amount out does not exceed the reserves.      An optimistically transfer of tokens is performed.      A callback is executed if `data` is not empty.      Emits a #Swap event.", "params": {"amountXOut": "Amount of first token to be swapped out.", "amountYOut": "Amount of second token to be swapped out.", "data": "Data to be sent along with the call, can be used for a callback.", "to": "Address to which the swapped tokens are sent."}}, "sync()": {"details": "Reads the current balance of tokenX and tokenY in the contract, and updates the reserves to match these balances."}, "tokens(uint256)": {"params": {"tokenType": "The type of token for which the scaler is being computed.                  Can be one of BORROW_X, DEPOSIT_X, BORROW_Y, DEPOSIT_Y, BORROW_L, or DEPOSIT_L."}, "returns": {"_0": "The IAmmalgamERC20 token"}}, "totalAssets()": {"details": "If the last lending state update is outdated (i.e., not matching the current block timestamp),      the function recalculates the assets based on the duration since the last update, the lending state,      and reserve balances. If the timestamp is current, the previous asset (without recalculation) is returned.", "returns": {"_0": "totalAssets An array of six `uint128` values representing the total assets for each of the 6 amalgam token types.  These values may be adjusted based on the time elapsed since the last update. If the timestamp is up-to-date, the  previously calculated total assets are returned without recalculation."}}, "underlyingTokens()": {"returns": {"_0": "The addresses of the underlying tokens."}}, "updateExternalLiquidity(uint112)": {"details": "This function sets the external liquidity to a new value and emits an event with the new value. It can only be called by the fee setter.", "params": {"_externalLiquidity": "The new external liquidity value."}}, "validateOnUpdate(address,address,bool)": {"details": "Implementation should properly protect against any creation of new debt or transfer of existing debt or collateral that would leave any individual address with insufficient collateral to cover all debts.", "params": {"update": "The address of the account having its saturation updated", "validate": "The address of the account being checked for solvency and having its saturation updated"}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"borrow(address,uint256,uint256,bytes)": {"notice": "<PERSON><PERSON> borrowing from the contract."}, "burn(address)": {"notice": "Burns liquidity tokens from the contract and sends the underlying assets to `to` address."}, "deposit(address)": {"notice": "Handles deposits into the contract."}, "exposed_activeLiquidityAssets()": {"notice": "Represents the amount of liquidity assets available in the         contract."}, "exposed_getTickRange(int16)": {"notice": "Retrieves the tick range values."}, "exposed_observations()": {"notice": "Retrieves the current observations from the contract."}, "getReserves()": {"notice": "Fetches the current reserves of asset X and asset Y, as well as the block of the last operation."}, "liquidate(address,address,uint256,uint256,uint256,uint256,uint256,uint256,uint256,uint256)": {"notice": "LTV based liquidation. The LTV dictates the max premium that can be had by the liquidator."}, "mint(address)": {"notice": "Mints tokens and assigns them to `to` address."}, "referenceReserves()": {"notice": "Returns the reference reserves for the block, these represent a snapshot of the   reserves at the start of the block weighted for mints, burns, borrow and repayment of   liquidity. These amounts are critical to calculating the correct fees for any swap."}, "repay(address)": {"notice": "Handles repayment of borrowed assets."}, "repayLiquidity(address)": {"notice": "Handles repayment of borrowed liquidity."}, "skim(address)": {"notice": "Transfers excess tokens to a specified address."}, "swap(uint256,uint256,address,bytes)": {"notice": "Executes a swap of tokens."}, "sync()": {"notice": "Updates the reserves to match the current token balances."}, "tokens(uint256)": {"notice": "Return the IAmmalgamERC20 token corresponding to the token type"}, "totalAssets()": {"notice": "Computes the current total Assets."}, "underlyingTokens()": {"notice": "Get the underlying tokens for the AmmalgamERC20Controller."}, "updateExternalLiquidity(uint112)": {"notice": "Updates the external liquidity value."}, "validateOnUpdate(address,address,bool)": {"notice": "Validates the solvency of an account for a given token transfer operation."}, "withdraw(address)": {"notice": "withdraw X and/or Y"}}, "version": 1}}, "settings": {"remappings": ["1inch/=lib/1inch/", "@1inch/=lib/1inch/", "@mangrovedao/mangrove-core/=lib/mangrove-core/", "@mgv/forge-std/=lib/mangrove-core/lib/forge-std/src/", "@mgv/lib/=lib/mangrove-core/lib/", "@mgv/script/=lib/mangrove-core/script/", "@mgv/src/=lib/mangrove-core/src/", "@mgv/test/=lib/mangrove-core/test/", "@morpho-org/morpho-blue/=lib/morpho-blue/", "@openzeppelin/=lib/openzeppelin-contracts/", "ExcessivelySafeCall/=lib/ExcessivelySafeCall/src/", "core/=lib/mangrove-core/lib/core/", "ds-test/=lib/ExcessivelySafeCall/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/openzeppelin-contracts/lib/forge-std/src/", "halmos-cheatcodes/=lib/morpho-blue/lib/halmos-cheatcodes/src/", "mangrove-core/=lib/mangrove-core/", "morpho-blue/=lib/morpho-blue/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "preprocessed/=lib/mangrove-core/lib/preprocessed/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"test/shared/FactoryPairTestFixture.sol": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"contracts/AmmalgamPair.sol": {"keccak256": "0xe8f98844a55a216605e6c6dd6837977fafda458a6b5d0cfa1f3a18d25e9432e4", "urls": ["bzz-raw://65dda1a1de8dd64e31c666b13de3d0583b4b0da923c67065cadcddefe47562a2", "dweb:/ipfs/Qmaev9WFa4yyL8fXVoWkXwNsTTY8wY7jTBGDoKJbdwSCzS"], "license": null}, "contracts/SaturationAndGeometricTWAPState.sol": {"keccak256": "0x5e293a35668bb216a99379ea2176894314cc0f1ac68644fcf4c07017da1a4419", "urls": ["bzz-raw://00349bb86f1b657010919b4bc3f616ad56ef4883b99ab0eead36815dae93dc76", "dweb:/ipfs/QmbEd9GD2JxuDntX35YcfbSCcpRstDU9GDPUkBKGzsxvqE"], "license": "GPL-3.0-only"}, "contracts/factories/AmmalgamFactory.sol": {"keccak256": "0xe0d9baf63d9538a7ecb8bd24ea61a8cdf6fc9c1e9eb028f343548adeb8b93e4e", "urls": ["bzz-raw://bfca24db47fbbaeef8bc36996cbfed78eb48771ac71d2800f081fb58a8e8c92b", "dweb:/ipfs/QmUfYEwfmrjSmchqXi58SnpSina4qKQvD2Jbk5RqYsaoCa"], "license": "GPL-3.0-only"}, "contracts/factories/ERC20DebtLiquidityTokenFactory.sol": {"keccak256": "0x72e3ada6a2f0792a353b730c1b45ae832f9ce2f58f0bda039383f8890cb2a4f7", "urls": ["bzz-raw://4949e7b66647313aaba2e11d7edde06eb87345b476c1a20f890659c1af827b2b", "dweb:/ipfs/Qmf3emVXfGp1oc8iVYxnVqpJ88vnxxdj7WqPm1vzVKb1SD"], "license": "GPL-3.0-only"}, "contracts/factories/ERC20LiquidityTokenFactory.sol": {"keccak256": "0x762974ca1ed600e0930a92bd2eb3a1a5f9ef0469ab2e6e811e4674e098238762", "urls": ["bzz-raw://5fd5f33537aeea9bac1f18c6fca2057899ec5f90cb8c756622eb436d5b13e27e", "dweb:/ipfs/QmfYznzzwN1AmdnuzNKe1R6t8UeztaZVGuzJ8vKfzjMXYN"], "license": "GPL-3.0-only"}, "contracts/factories/ERC4626DebtTokenFactory.sol": {"keccak256": "0x7deeb7a40d26bc790112f29836da83050fa3554e471e1dce4dda6bf29ab9bf67", "urls": ["bzz-raw://5a46a4c8270e0b8a731259328b6c35c84de270a14f2f69ba04bc58d18400efc6", "dweb:/ipfs/QmQ56QbX6S9GjQinsFYtTMns6HgpcTXW1wnvQT6QgiuW1Z"], "license": "GPL-3.0-only"}, "contracts/factories/ERC4626DepositTokenFactory.sol": {"keccak256": "0xf84b75119f2680f8079bb9567b0c03c0ad49b71a8c00f968d03d5fca2a954035", "urls": ["bzz-raw://c3fc7a9e300a935991746d5be835418b09e6d2b20b65e3e297d4faf28516469b", "dweb:/ipfs/QmQMr9MA5a3UcZCiP3e2haYqzBsbE8Pe6rDq6j6RJ3ub4Z"], "license": "GPL-3.0-only"}, "contracts/factories/NewTokensFactory.sol": {"keccak256": "0x86cd420e1df8a59b11a4ab53a16971a44953f0a07741ef69d95baa4bd60126ac", "urls": ["bzz-raw://d8cdd98060f059705b9ae2b64ab3e74395c0f3a24e12f5ac11ca7e509c6a7aa0", "dweb:/ipfs/QmahgKkRzuWHpQ73DHGZ4Kvd2MQG7MpfPShayJDRJQYSVr"], "license": "GPL-3.0-only"}, "contracts/interfaces/IAmmalgamPair.sol": {"keccak256": "0xa17e45b2348d8920d9970c5d50b300fc0a1e8d03350cdd0d1a624494baa70337", "urls": ["bzz-raw://8d252e89e5d49d1c15a0c0c0a495a325b9f8d608714b29279a7bacb1e4bf8795", "dweb:/ipfs/QmRkZ7a8JJQYEw6HQMJjjkuAK8b5Th1X1ET6BG1R8mx4qw"], "license": "GPL-3.0-only"}, "contracts/interfaces/ISaturationAndGeometricTWAPState.sol": {"keccak256": "0xc9add2ad41f8edd9d360ced8d2cd7bd18dd500304794434fb2e309fa0f5af83c", "urls": ["bzz-raw://8ecc810c544ac734ef26a2f6bebea3f3bd12d773965d297991e0e0e72892fa20", "dweb:/ipfs/QmarXc1Ut4FZzPRRZs2M2udbJjuZUJQHQ8fvmSr3bpHErR"], "license": "GPL-3.0-only"}, "contracts/interfaces/callbacks/IAmmalgamCallee.sol": {"keccak256": "0x904b858859d460a61c9e644ca87009d8e32ba20482ef218801c89c7fb1ece339", "urls": ["bzz-raw://1a7cedebbacc453e3e4e339fcc76fd3268247c13982de82b4930d59a44256c1d", "dweb:/ipfs/QmdjdvYabbwAYcV72xjiXyq278xQivFtiqD3eQ5P9Gk4f1"], "license": "GPL-3.0-only"}, "contracts/interfaces/callbacks/ITransferValidator.sol": {"keccak256": "0x6d9028fc4ad1914e6b2091e6ba46a9f836f9e67ea435c4a8fef41363f2ceaf56", "urls": ["bzz-raw://7ecaade4884d460168f6978edf35706f7b9e363de6002942b1d171a338dca6a4", "dweb:/ipfs/QmS5wgfDt5Pn68rpCytpzhiy57LcmivVFQ5XLGXUUP5Tt8"], "license": "GPL-3.0-only"}, "contracts/interfaces/factories/IAmmalgamFactory.sol": {"keccak256": "0x1c80089901e8d7d7451775b5eaa92092eb2b65319cb92fa7884281bae49f52b8", "urls": ["bzz-raw://bf1201399bb9d5046e0e788ff88394b2468771096a2a0d3500af542923e84628", "dweb:/ipfs/QmeUaPyQpbPbP5fyPUT2FfzeDgHcdyQAn1DaNg9uCuGoj9"], "license": "GPL-3.0-only"}, "contracts/interfaces/factories/IFactoryCallback.sol": {"keccak256": "0x33250cf8351adb4846a3d133a9bc06568288e4c680bcf5b1085e3bca40a35e52", "urls": ["bzz-raw://5663a39af4ed3040a58beaa5641425b9adca83c2683dd220e0c11e644fefe52b", "dweb:/ipfs/QmYB8Vf37WDzQfSpMDjv8hVicuaF1wMBzf7xjHRjGCy3wT"], "license": "GPL-3.0-only"}, "contracts/interfaces/factories/INewTokensFactory.sol": {"keccak256": "0x3b2f1ee34106d2694a9ebbe600be692bed645f4247f4a24da3d5ec46025ab3e9", "urls": ["bzz-raw://73143452a06db52deb593585fea6f2ef7c46e9ef6d649562dc39e79e4e5dca2b", "dweb:/ipfs/QmYQEy7BZWnfWKnuac8GB4QPhG5qJpaHQAfkTBoUDEuX1E"], "license": "GPL-3.0-only"}, "contracts/interfaces/factories/ITokenFactory.sol": {"keccak256": "0xac23e5c0441599add526b0c308faa7787f90bf01603b6dbc231944c166ca32d6", "urls": ["bzz-raw://ac574b98b2c1034786581137a218277ec58e06e9612f76814f34960383083626", "dweb:/ipfs/QmZgZqVnshjzuHBXJTR9g87S15CyLwJUSErGEDoJpBd4kg"], "license": "GPL-3.0-only"}, "contracts/interfaces/tokens/IAmmalgamERC20.sol": {"keccak256": "0x44a376269170b4270ec221ce3cb31a609b394e216cc4d2e27b818361b4369829", "urls": ["bzz-raw://c48bc7586631f27ede73d3d0b4c1d7a29b1653e6c501c8b7fc9877c125f8f57e", "dweb:/ipfs/QmTSLtqnsxr7h7ct524rqYssHUo4qursmCZ7g5q3J1qQPK"], "license": "GPL-3.0-only"}, "contracts/interfaces/tokens/IERC20DebtToken.sol": {"keccak256": "0xc50c6be17633c8ac677b4eaac7c05a6de1f1c938237179b59ad5e65bcfbcb03a", "urls": ["bzz-raw://6c75327e01d70a0c22662a9a8214aa64e45c517146971f8636e5aa5bf06e7696", "dweb:/ipfs/QmV2ydBQ5S9ZBtRuPgBMBdvd2Hcnn8quCGEMhhAAuic15b"], "license": "GPL-3.0-only"}, "contracts/interfaces/tokens/IPluginRegistry.sol": {"keccak256": "0x9a677620d88ac7dc42afb21d82a7b7a89bd934c1cada5450cf2b6200bf374ccf", "urls": ["bzz-raw://304091e5d54e9ad7db24ba1022e84e39dd9305d9cc72fd87423c71b40de4ab3d", "dweb:/ipfs/QmZEF5MfmUcxzLF9fcGCLvGMTTLLhcWdCMCDK2WyXj6s7X"], "license": "MIT"}, "contracts/interfaces/tokens/ITokenController.sol": {"keccak256": "0x7778001aaf582fe10005240eb6023b2b6cee3f100b6c2222bf6b9ade93732624", "urls": ["bzz-raw://91e5c4519207d6a450be1e0a8649157e86d20f8ef6a91ff6512a31cf5561a570", "dweb:/ipfs/QmUqZLW27JJZHFPf2fgLDYSWWj5gM158DdaxTTmDVukRAg"], "license": "GPL-3.0-only"}, "contracts/libraries/Convert.sol": {"keccak256": "0x944776d31291de1a9cdc6a52154c23c22b43a01c3edebe7a4140e267edbba975", "urls": ["bzz-raw://36c03749859077ba47a3acfd574f8c30f34f97def4ce81d7f4feac9a7b62794c", "dweb:/ipfs/QmdycZay5X2WrbS8qS7RycLpZbMQx7yKszWQzGU3rqidpH"], "license": "GPL-3.0-only"}, "contracts/libraries/GeometricTWAP.sol": {"keccak256": "0x3860409daa0fdb5d96f0bfb8b49cbca058b9fe32c8e32457f85d4ee2c5cdcb1e", "urls": ["bzz-raw://81fe70a80f4005a6529c7e93f1a92547ce0bf74c357c280a91e8778b378b18aa", "dweb:/ipfs/QmdRQ1DqsCu11zfbLAbrrzJ9Ups3oKgTGimYo3Zm3ALiCz"], "license": "GPL-3.0-only"}, "contracts/libraries/Interest.sol": {"keccak256": "0xbc8bfa20d7295dd70e3c716fd3dbeb5b45d313e3c609d063d186042cbf000646", "urls": ["bzz-raw://b015e8d4976d3b6d7eaca07dfcc487aeed3a7d8b4c41c8369a7476dcfb211194", "dweb:/ipfs/QmecH84UnZYxDZ2aL6rQtnrEExLEAfo7q4Y47yuBXdymeX"], "license": "GPL-3.0-only"}, "contracts/libraries/Liquidation.sol": {"keccak256": "0x842bc44bc3cff80360ab82c5920070b12680edefe9267bdffc2d6c3c3a692d63", "urls": ["bzz-raw://85ecd75568a0729aec06741d0575ed07dad8b7daebd7ba3114a93f6019250877", "dweb:/ipfs/QmQMvWdsPWsQ4t1yv6eyZy5TM7h1EZpSJdt5b8fDLcumCW"], "license": "GPL-3.0-only"}, "contracts/libraries/QuadraticSwapFees.sol": {"keccak256": "0x00f6b7909be4fa1fc1ba426dd8ae659d1c5cb20c79665148898c973f55cfdccb", "urls": ["bzz-raw://c64da0826a9b0ffc08319709f6db03339d22d24deda902a6540393251da0aecb", "dweb:/ipfs/QmSNwBbn2VAS8HPY4hNZusEc4DoKKZAZHtpPdjL9Gz3gs3"], "license": "GPL-3.0-only"}, "contracts/libraries/Saturation.sol": {"keccak256": "0xf44bc610ece4bc7ebdb0730aa6ad69ea47647e19d4c1944c663d2d2eb4f10860", "urls": ["bzz-raw://421fdf8d0b27132bc324a42ede9aaf23b476e5134e1073f84e824620a2a44f20", "dweb:/ipfs/QmbvSfMuMzDmrfPkCAEp7ydtRDWu5EUiXq4MyrGGjFErzE"], "license": "GPL-3.0-only"}, "contracts/libraries/TickMath.sol": {"keccak256": "0x753813c7ed638d22edb71f48f8eb8b4283b3db2ba5b136b5c8909bd37ffa3f12", "urls": ["bzz-raw://04dd5085b72f6d73e1b17f58148e4d03639f654bdc4fdbc173b7c92ff102fc20", "dweb:/ipfs/QmSg4xTQPkngjNxs84428FZdSwH4AUQpwLXaASx7Qev6oG"], "license": "GPL-2.0-or-later"}, "contracts/libraries/TokenSymbol.sol": {"keccak256": "0x628df064fdbdacfe6783964d7bf38cdf1b34e1ad07caa3cea39bf7468cc19b43", "urls": ["bzz-raw://da6823ce0debaabe20f25281e81a4fc88de98d4df2942a5e276826ac381c227b", "dweb:/ipfs/QmNpEuQ25788xfcJwPk2xUB7fyP7fW5ENK2e9qgRqp1BcH"], "license": "GPL-3.0-only"}, "contracts/libraries/Uint16Set.sol": {"keccak256": "0x26a714430fe1618d78386e953153b4bd2bf024baee54453ec9a7a0cc60e1534f", "urls": ["bzz-raw://8667dd78541d656a09678e5f9cce4d49adc805955604ccaaec414e9b241f5e06", "dweb:/ipfs/QmZVWU2CzyDQfGit32HjJxDphBJMKG3d6JRuxbC682Z1gy"], "license": "GPL-3.0-only"}, "contracts/libraries/Validation.sol": {"keccak256": "0x294848b2af973dbcd8b83732a57b67f14fd15e4af0668de05a2928b8eca5a463", "urls": ["bzz-raw://fab25c941e87f6924b31e3f20742ca6b5ec1b7e4251543f4a61567a04ef4d778", "dweb:/ipfs/Qmf4ChH8afdHc3SfXkFPpNGp3e1hscyvnujPAMza3yuXeA"], "license": "GPL-3.0-only"}, "contracts/libraries/constants.sol": {"keccak256": "0x0dfb294985a8f48287ff13e8476718ddb5334b1d8bf6bfa59a5db1dbcf6ca7c4", "urls": ["bzz-raw://4bedcfdb2850cfb22b5daa768ab8125b4ccab97c90068d1d0ad4495bf942b362", "dweb:/ipfs/Qmf9p88yQN2JYRBR5D7q9BLmwhDJWpFk47ZuayrKqCyHat"], "license": "GPL-3.0-only"}, "contracts/tokens/ERC20Base.sol": {"keccak256": "0xdd3db9eaa855b6ff747ffaa0e74dd2a64dd5b0d704356acb75c2690c3fc6bc2b", "urls": ["bzz-raw://8f9e05ae3078bc3a29ef43861b006fb290606c1c9b2676baaed3ab98ecdb2d59", "dweb:/ipfs/QmZrvgxYtUD6jQVBvM9rT7jj5Vzb5csiThGj3ZwHSPryAL"], "license": "MIT"}, "contracts/tokens/ERC20DebtBase.sol": {"keccak256": "0xc0a59cd54fcd847b160d662aa45a5fe7d24ed90c8030fe17fd5f9def427ed19a", "urls": ["bzz-raw://365c7f18505af36b2806404b1b3f2d897de6ac18e255ecfbb4ccc491cac7e444", "dweb:/ipfs/QmUqx8EBwRb6W1YQPb9MjwAhEEHNpZTCopbGWb1vbyuUpp"], "license": "MIT"}, "contracts/tokens/ERC20DebtLiquidityToken.sol": {"keccak256": "0xf222ad5562ed41d74b0cfb5b4aad84ec9f4cb91b6d71928b30b018bab494efe8", "urls": ["bzz-raw://a8e8f3e7ded2eae04c63ce3ae7a86c051d48d6db697cb6929d7064a4ec9d7371", "dweb:/ipfs/QmU3EuwHU3xB1e6MxaRjSRJcDMK73wfZig9uGWqZPaHnTn"], "license": "MIT"}, "contracts/tokens/ERC20LiquidityToken.sol": {"keccak256": "0x2bb2429e551c031034c747749373d2e4c451580e9b203b689d6eaf03ad896358", "urls": ["bzz-raw://9ad5902756073578beee9068b74bd921e59a36b803cf34ef01570c670363689e", "dweb:/ipfs/QmTkT5K2XcB3ZbPDqd4ZAfnZMp2reCzu3Pv7JpRqhAtZHP"], "license": "MIT"}, "contracts/tokens/ERC4626DebtToken.sol": {"keccak256": "0xe69b1ed2fb7b2d7c24c6838462001988b8e51795d215cfa74b9874d17257509e", "urls": ["bzz-raw://c4f201e5f5621689046c58863ab9270cf770c68810d52269d1fc2ac93a7ccf96", "dweb:/ipfs/QmdtALf6LQdHhce3HsNdVtomQu8e5F5QcYU6S7H1PeBThZ"], "license": "MIT"}, "contracts/tokens/ERC4626DepositToken.sol": {"keccak256": "0xd914aa43dc5e9f2f02f98b05561faf6f00853b701f51dfcd7a08a31feaf220be", "urls": ["bzz-raw://8c2282d40855587b2ac70e89d4e0e147b9afe32a41245fffc96b923a9e5ce7ac", "dweb:/ipfs/QmVn3tBkZcXKnqjfnLTXFkKtu1EetDL1UF7rRjNrHdRCSM"], "license": "MIT"}, "contracts/tokens/PluginRegistry.sol": {"keccak256": "0x9263d71fc32da7d0ca4f8d272f8d75d565c1f06281952481322983bae9d7b488", "urls": ["bzz-raw://c9dcbc64172f4339547865b4f041826f0de5d464900f316edbe72e7d6bfb396d", "dweb:/ipfs/QmQykSWuY8xLJotWUPgG5JQDS5DmA2E5Hjb4c6Bz4YnbBQ"], "license": "MIT"}, "contracts/tokens/TokenController.sol": {"keccak256": "0x8b76b9ebb9385f0c4b7c0b8210fb96b11a49a8c9a3a6e855752c32a5c12d54e6", "urls": ["bzz-raw://de87bdae81940f397136f665d68907d4e4c32f35bf2cd0c9e9305a9fe190d159", "dweb:/ipfs/Qmce4hM6xofBYxzAXesHX4hkiHBexoGeQpCzpeCARctnCn"], "license": "GPL-3.0-only"}, "contracts/utils/deployHelper.sol": {"keccak256": "0x9b9dd84e234bb2ffbf51da7e9ab42fe7b6329acf38de7f042d4f8abd146182f0", "urls": ["bzz-raw://d07ded7de8e48a25ac7b0442c6e455338bd16ee483a89ad7f37585ab91865a3b", "dweb:/ipfs/QmeBAuZgRJEXeuX6qsGt46sTLovKNC5Pue8xFxbHMPtiBR"], "license": "GPL-3.0-only"}, "lib/1inch/solidity-utils/contracts/libraries/AddressArray.sol": {"keccak256": "0x7895eaf7b55d9612b22ec586970488de51436c021b9f9414b3c27c3583c8856e", "urls": ["bzz-raw://e43897dfeff84e2983b017ab100d74061a6b9bed4618ec37a7cbb68bf064ac22", "dweb:/ipfs/Qmejoj4CuNr1giwGBDG7SgRtfYmi64fgy2DsGP2AAW9gH9"], "license": "MIT"}, "lib/1inch/solidity-utils/contracts/libraries/AddressSet.sol": {"keccak256": "0xbb8e2a541ac268f00f382c9fba9403b3ec5b58a48dc7236920d7c87817f93318", "urls": ["bzz-raw://614b60ff60f75c46aa41d00ec5dd78136d42e0d6035aa2331d16f26d2f5f5368", "dweb:/ipfs/QmUJWqcx2heKcmBhxpHfAnpKrwnevqtAwaQKT7Bmpke5NB"], "license": "MIT"}, "lib/1inch/token-plugins/contracts/ERC20Hooks.sol": {"keccak256": "0xd2657f278b2ed4667663344aa06c02b52b15862c69c215570de329aa1a4683a2", "urls": ["bzz-raw://4ffb90146369b7fa890489a0d3ecab5aa81c9f47b3ec7a1776fd8bc14eaa33d5", "dweb:/ipfs/QmWo24VHySo9jQBeXfE87Z3Hh576cNKcwccLrBAMsfax1c"], "license": "MIT"}, "lib/1inch/token-plugins/contracts/interfaces/IERC20Hooks.sol": {"keccak256": "0x2fb4fcbf91a7edf36e7ada3f578a8de1aee7ebdd12460648d3e09d4448351875", "urls": ["bzz-raw://d09a05543afcad3c0c2d2b9013647408cc44e1b17f362665ee20f9abed5964e8", "dweb:/ipfs/QmX3MMGsVJSfsrVoYY2jWK2BXd3i6scBhQQxytk461mizh"], "license": "MIT"}, "lib/1inch/token-plugins/contracts/interfaces/IHook.sol": {"keccak256": "0x474893cc48ee17530ad0e978ecbccaa726943615a5736260187a37b8e133ee80", "urls": ["bzz-raw://511ed52ec427cced459878f28453790817d4cbacc8601d77eb8dfa28e2e0b30d", "dweb:/ipfs/QmXvbB1bAZZanGepiUxWh3zZQUaHQZeYpR1UdaW4w5yKVS"], "license": "MIT"}, "lib/1inch/token-plugins/contracts/libs/ReentrancyGuard.sol": {"keccak256": "0xa88ccab1ee6b34a9007986ca15ea5fd5580864029130eb38333183af1bc4f66c", "urls": ["bzz-raw://b5ad18c862e59b0f24edc900519a55c7aee5537a42d039d6ea5e34df40267bb0", "dweb:/ipfs/QmVMRSjUEPxCno1sFdhWvpRqAqoqns8zwVyd7yCHYC6P8Z"], "license": "MIT"}, "lib/ExcessivelySafeCall/src/ExcessivelySafeCall.sol": {"keccak256": "0x7d9d432e8f02168bf3f790e3dabcf36402782acf7ffa476cabe86fc4d8962eb2", "urls": ["bzz-raw://1adc13e7f399f500ea5f81480ad149a50408fde7990a2c6347e6377486f389dc", "dweb:/ipfs/QmSvm5TUBJqknsqNJLLHqNS4MLSH5k3vNrbquVg6ZKSfx9"], "license": "MIT OR Apache-2.0"}, "lib/mangrove-core/lib/core/BitLib.sol": {"keccak256": "0x80f6885268986b9e976b424993aa875cf7aab8464403ed675a86ade9e9be5ee3", "urls": ["bzz-raw://5a31b4e1e0dc95de9a1dbb114c40c44814de5db3e2d857c93c2524a61454f6c8", "dweb:/ipfs/QmRkgE9ue5rGwE6XDnszF2e2meWqAC9nnKM97xKHjHphQr"], "license": "MIT"}, "lib/morpho-blue/src/libraries/MathLib.sol": {"keccak256": "0xa7354cbbcecef7bc0c94b61061c4e5da75515056b8e2db65e826b00d7369744a", "urls": ["bzz-raw://d7419c59bb906fcfa49320b68f265c3200090e5c30b194766256aee70b012e08", "dweb:/ipfs/Qmbo4uaW6XYnudya4bb6RU6riWXFk5M3CWJge5XzTTaEfd"], "license": "GPL-2.0-or-later"}, "lib/openzeppelin-contracts/contracts/access/AccessControl.sol": {"keccak256": "0xb64ecf1154f183412bcde47168f3af245e4120846346a0b3872c631e361156d2", "urls": ["bzz-raw://85331049a60659bc4489733ccd3cbeb177b65691122a8cb637bf9267ab25e23d", "dweb:/ipfs/QmbGpDcdwKTirzSCoZfE4rHG7jBSWsE4K2iSb6UCYXtLJv"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/access/IAccessControl.sol": {"keccak256": "0x5643a5cadd1278581308b20becb48a50946c159fc31c29fc407ea9a61fc865d1", "urls": ["bzz-raw://c7d79f305a239207a24fa2174897c8ea8ff1e81cb790d440fd54c89a0e85f63e", "dweb:/ipfs/QmT847eeAMnRN3DaG1zsKNMn7qipNAidqv1REnKexPkrfA"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/access/Ownable.sol": {"keccak256": "0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb", "urls": ["bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6", "dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/governance/utils/IVotes.sol": {"keccak256": "0xb541b133f2d85eb37ae866cb21123be93bd3671b6840c47f951da52d3a704548", "urls": ["bzz-raw://22443f43ece107eaf453aa9a41a59410cece5429c2779c794fbd2c8b5aa29d38", "dweb:/ipfs/Qmbum2jwLYuT9aZ2fr9NMLwWFsVavonrGm2VnbAL9hP2jn"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/governance/utils/Votes.sol": {"keccak256": "0x3f91c79d6f55db9e4fc36e1cfe6a483a7b0f5be60fecbd979555071673746d47", "urls": ["bzz-raw://9b1e3c64cbeb2757a2a1a45c69f7f3984a93b0eadd1016341b64f9d94f89d7c4", "dweb:/ipfs/QmP1Mj14U4vMTFa2rv2nodMbWSCov2ac9Md8W2aUcgYdKX"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC1363.sol": {"keccak256": "0xc15298eb2b9ba5e18a8c9d12f93ad17a3e162a5c1d9b85f54c8adb5827b0d4da", "urls": ["bzz-raw://1f3c3d8f81d2daf1231890a6a2f897be365d6a479b53dcd52ec2527b5d3faf41", "dweb:/ipfs/QmeNdkd6u4at9pd2GAyyqxzrVGGvxfLpGmAKnFoYM5ya2e"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC165.sol": {"keccak256": "0x81b022028c39007cce9920c394b9cddd1cb9f3a1c0398f254b4a6492df92ad2b", "urls": ["bzz-raw://e0b61b8a5c69b4df993c3d6f94c174ab293aa8698d149bce7be2d88f82929beb", "dweb:/ipfs/QmbtacmB1k8ginfrHvAJpjVeqnjYGfXYrkXmMPYEb83z4t"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC20.sol": {"keccak256": "0xb5d81383d40f4006d1ce4bbad0064e7a930e17302cbe2a745e09cb403f042733", "urls": ["bzz-raw://3fc4a5681c2f00f41f49260a36ae6bbe1121dd93d470ea24d51d556eff2980be", "dweb:/ipfs/QmUBW6TwVWtGP96ka9TfuGivd27kH8CtkXD8RQAAecSFiR"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC3156FlashBorrower.sol": {"keccak256": "0xad94c8d7246a50210f7bcb54e5b91fc9f1c6e137263ac972ca5dd0f7f6d4d49d", "urls": ["bzz-raw://6938e96fbb0cf3d961788b6c3522400e255d8057d1b9f7e08a50e0b48486b007", "dweb:/ipfs/QmNXG3MPzDXjHJ9iWDYCz4vi9RBTgVBnZjndnfBwMfhkyD"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC3156FlashLender.sol": {"keccak256": "0xd92910b862581523ad4e9b99f0bf738f4e62700a5e305953c7fda7db2cfd0f73", "urls": ["bzz-raw://799f3f0f30822ac806800bbe7fe63b9991490c4e1d9edb75f5993d9350320819", "dweb:/ipfs/QmT8T4SokW6YxpDJQiafpeYNRGtjC5gFHxRqKTRXRyP6zB"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC4626.sol": {"keccak256": "0x932fe72aeda629f70ee2ca902cfd8cce9faa0a13b39222c240979f42984c4541", "urls": ["bzz-raw://4e9e0a320185800eb9a6d57ef0e4ccf3bb32a6b5dd44882f7552617c30d4a05e", "dweb:/ipfs/QmYJVpT7DDPWx3DWro8vtM6Gqre2AyufsyCYoHm9cfQ1vr"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC5267.sol": {"keccak256": "0xf29e9088951d8a2074d872a733674618fe5c164df21b8b5cf4a6295f523ba7ad", "urls": ["bzz-raw://562a1abc7ea505582827ce0c9a2f778360a1a8242742683af179930640020215", "dweb:/ipfs/QmPjx5f6KKaPfsDi1uV3ovQN9gHTAcNkMAFJZxE1Adw6VT"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC5805.sol": {"keccak256": "0xc8960b7d3e504e98883de33856a917a473c05034cd61880df2a60b5c47c214fe", "urls": ["bzz-raw://80542373fa695b68d65b4d7222e852df5bda035a82e86ee559336c93b2bf7051", "dweb:/ipfs/QmZgH14DPTnKfA5gMSTMiUa6ExuqFfAozmEtLXiWc1iDiw"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC6372.sol": {"keccak256": "0xa602c8beaae2d9e2ab1ce585a54547a6d4da32d32e4d002d20ccba55b19258d8", "urls": ["bzz-raw://ac6553b5b07788a0bb67cc53596837d795280233a9a5cb3a9b3e1fde56822f78", "dweb:/ipfs/QmVoHXoma4ZbPKVRJJRosvhipa4rtCMU9QQvWHWKiRUxvi"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol": {"keccak256": "0x85cf779582f7860b00bba1d259be00e754bfbac3c2339e324d0113d682d9e9f9", "urls": ["bzz-raw://2ddf369affd106e2a9e2b8a22a6ce3da8a6ccda14c6ba5b8c87f6b08169e6318", "dweb:/ipfs/QmNadAttd47ycHShxhk33JUJhrbzmyZQ7mHs7WEyG4Qkmp"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol": {"keccak256": "0x5aad1745bddba11752c3962464a3b12e0af079310cc22d1f43f0388ae1aaf8db", "urls": ["bzz-raw://577fad916bfdfe89aadf2685322fec7562cb0ed87722923085213cd9f85d7b79", "dweb:/ipfs/QmSM3J6PjrAUyEoNbdhq1ECZLXczKdCTzZTBUieKHsBYEL"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol": {"keccak256": "0xfe37358f223eddd65d61bb62b0b7bdb69d7101b5ec8d484292b8c1583a153b8a", "urls": ["bzz-raw://28dd43f30af3c12ae0fc08dd031b1250e906ef3c95f63f30fac6fd15aee2a662", "dweb:/ipfs/QmUkSyWsSRx36w1ti7U6qnGnQgJq16wpMhjeJrnyn9AXwG"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Burnable.sol": {"keccak256": "0x2659248df25e34000ed214b3dc8da2160bc39874c992b477d9e2b1b3283dc073", "urls": ["bzz-raw://c345af1b0e7ea28d1216d6a04ab28f5534a5229b9edf9ca3cd0e84950ae58d26", "dweb:/ipfs/QmY63jtSrYpLRe8Gj1ep2vMDCKxGNNG3hnNVKBVnrs2nmA"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20FlashMint.sol": {"keccak256": "0x4d43ed4b9ff9e4c671274976d59a58dbcc7b69bd7ac11b1710f5b7607cf15b74", "urls": ["bzz-raw://0b47b42998f675cb6a51f2e74ef5906a6fa63ec6718f3fd56ee035d6f77143f9", "dweb:/ipfs/QmREnAXqPJBvAwfWfDzaFhNfSRWF4Jdy9ZrpHLw1KdQweY"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Permit.sol": {"keccak256": "0x6485b101d7335f0fd25abc996c5e2fc965e72e5fbd0a7ad1a465bd3f012b5fd8", "urls": ["bzz-raw://1f8d296121044e697bcd34c4cd394cebd46fc30b04ce94fccff37200872e6834", "dweb:/ipfs/QmTNdmLdoHgMzoCDZ8Txk9aYvwtuyeJYHf5mjLWgzGTZAu"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Votes.sol": {"keccak256": "0x62dc9346044aabf22d78541bd495aa6ca05a7f5100aed26196ba35d40b59fcb5", "urls": ["bzz-raw://5221df4501c74cd4493fee1a0f0788e02c4dc78c3c601e9f557f557c5a53ea92", "dweb:/ipfs/QmZpzyYY9dKLrgvYhXSHT93jwqb1UGvtGNMQk5dpECY5pa"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC4626.sol": {"keccak256": "0x0d99c706d010fa15de36e7e7b7a03dd0fdc9bcec52f9f812ef80ec7f3fc6fa63", "urls": ["bzz-raw://bee73c81a2964e8da0537de14082e60d64cd7b1cd9162adc04b58317e334c896", "dweb:/ipfs/QmbQ75T9PEJuiLk1kypX68rEBFtTaEzPWsy8Dv99buqVPH"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"keccak256": "0xbaffa0bcc92adf28a53cc3b68551fc3632cb8f849a0028cb8d5c06e4677715e9", "urls": ["bzz-raw://32e6f8f6b2e883c85e6a602c0882d9962ce2f92406961244e86cd974df815912", "dweb:/ipfs/Qmahvx6fPpecicq1aUE1JihCxV5ep1bfuPukzrxa8Ub5PS"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Permit.sol": {"keccak256": "0x093f32ab700c2b05373387263915a75f5455cdb0f09a7630cc621e27b7b50d04", "urls": ["bzz-raw://d163e6ef21df143969df5557305e8c643a135c7660a678d0c65dca91772114a0", "dweb:/ipfs/QmTZUgiwEro5oLRhbJ2iSWyCqu1JTDekoFHALVUn4eHqYK"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol": {"keccak256": "0x1183b415155c1a7bf56d45edad5b17caf0da70935ac420698cbe8afb6750cbb2", "urls": ["bzz-raw://21d9edaeb3e5e8f93eb0fdab41530654e8169b1990b3bbfcf5e4527c52aa03f5", "dweb:/ipfs/QmWrqpNW3x5k3pTjvrT8XU1hauHnXTjqaPL2tfzMuWYosj"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Context.sol": {"keccak256": "0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2", "urls": ["bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12", "dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Nonces.sol": {"keccak256": "0x0082767004fca261c332e9ad100868327a863a88ef724e844857128845ab350f", "urls": ["bzz-raw://132dce9686a54e025eb5ba5d2e48208f847a1ec3e60a3e527766d7bf53fb7f9e", "dweb:/ipfs/QmXn1a2nUZMpu2z6S88UoTfMVtY2YNh86iGrzJDYmMkKeZ"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Panic.sol": {"keccak256": "0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a", "urls": ["bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a", "dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Pausable.sol": {"keccak256": "0xdb484371dfbb848cb6f5d70464e9ac9b2900e4164ead76bbce4fef0b44bcc68f", "urls": ["bzz-raw://f9d6f6f6600a2bec622f699081b58350873b5e63ce05464d17d674a290bb8a7c", "dweb:/ipfs/QmQKVzSQY1PM3Bid4QhgVVZyx6B4Jx7XgaQzLKHj38vJz8"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/ShortStrings.sol": {"keccak256": "0x1fcf8cceb1a67e6c8512267e780933c4a3f63ef44756e6c818fda79be51c8402", "urls": ["bzz-raw://617d7d57f6f9cd449068b4d23daf485676d083aae648e038d05eb3a13291de35", "dweb:/ipfs/QmPADWPiGaSzZDFNpFEUx4ZPqhzPkYncBpHyTfAGcfsqzy"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol": {"keccak256": "0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97", "urls": ["bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b", "dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Strings.sol": {"keccak256": "0x1402d9ac66fbca0a2b282cd938f01f3cd5fb1e4c696ed28b37839401674aef52", "urls": ["bzz-raw://d3e6c46b6d1ea36bd73e0ac443a53504089167b98baa24923d702a865a38d211", "dweb:/ipfs/QmdutUpr5KktmvgtqG2v96Bo8nVKLJ3PgPedxbsRD42CuQ"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol": {"keccak256": "0x6c29257484c0595ca5af8844fafe99cc5eace7447c9f5bced71d6b3a19a6a2a5", "urls": ["bzz-raw://cce7ac0bdb05f73c0918e362dea2e52426e00ddf0a1018f14accdcf78c6eb6e4", "dweb:/ipfs/QmbkNq5dDxww27FzFFiKgW3S7C5VoZpjdZGpSCtsb9hP32"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/cryptography/EIP712.sol": {"keccak256": "0xda8013da608bda3c9eaa9e59053d38d7888e64bb40aa557e5929cd702f8de87e", "urls": ["bzz-raw://3ea13234c6b00ae79dc1a98e7e7f2faf38d37e76a687ccd0c95ad84b03ea570f", "dweb:/ipfs/QmWtdefDm5jiEzAjmfPMZ5B1NKVxFoMiD5ZoD68hcNTHun"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/cryptography/MessageHashUtils.sol": {"keccak256": "0x26670fef37d4adf55570ba78815eec5f31cb017e708f61886add4fc4da665631", "urls": ["bzz-raw://b16d45febff462bafd8a5669f904796a835baf607df58a8461916d3bf4f08c59", "dweb:/ipfs/QmU2eJFpjmT4vxeJWJyLeQb8Xht1kdB8Y6MKLDPFA9WPux"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/introspection/ERC165.sol": {"keccak256": "0x41ddfafe0d00dc22e35119d41cb0ca93673960689d35710fd12875139e64bd9f", "urls": ["bzz-raw://49d90142e15cdc4ca00de16e1882fa0a0daad8b46403628beb90c67a3efe4fc4", "dweb:/ipfs/QmNizYnFNcGixHxsknEccr2cQWyyQBqFF7h2bXLmefQz6M"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol": {"keccak256": "0x4ee0e04cc52827588793a141d5efb9830f179a17e80867cc332b3a30ceb30fd9", "urls": ["bzz-raw://17d8f47fce493b34099ed9005c5aee3012488f063cfe1c34ed8f9e6fc3d576e5", "dweb:/ipfs/QmZco2GbZZhEMvG3BovyoGMAFKvfi2LhfNGQLn283LPrXf"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/math/Math.sol": {"keccak256": "0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6", "urls": ["bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3", "dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol": {"keccak256": "0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54", "urls": ["bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8", "dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/math/SignedMath.sol": {"keccak256": "0xb1970fac7b64e6c09611e6691791e848d5e3fe410fa5899e7df2e0afd77a99e3", "urls": ["bzz-raw://db5fbb3dddd8b7047465b62575d96231ba8a2774d37fb4737fbf23340fabbb03", "dweb:/ipfs/QmVUSvooZKEdEdap619tcJjTLcAuH6QBdZqAzWwnAXZAWJ"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/structs/Checkpoints.sol": {"keccak256": "0x743aa2d21f6c26885e0aa6a1c84f7f7bc58fbd6df6bab32bed23f1a41f50454a", "urls": ["bzz-raw://a651d38b4261840d3744e571edf2b59455352a8c7dac5d35b019afefa343ea3b", "dweb:/ipfs/QmSy3UkTCQDYTjKtGwtqPRrXaofcqtVZxaF6j1dV44wqvr"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/types/Time.sol": {"keccak256": "0x36776530f012618bc7526ceb28e77b85e582cb12d9b9466a71d4bd6bf952e4cc", "urls": ["bzz-raw://9f867d046908497287d8a67643dd5d7e38c4027af4ab0a74ffbe1d6790c383c6", "dweb:/ipfs/QmQ7s9gMP1nkwThFmoDifnGgpUMsMe5q5ZrAxGDsNnRGza"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/Base.sol": {"keccak256": "0x4ff1a785311017d1eedb1b4737956fa383067ad34eb439abfec1d989754dde1c", "urls": ["bzz-raw://f553622969b9fdb930246704a4c10dfaee6b1a4468c142fa7eb9dc292a438224", "dweb:/ipfs/QmcxqHnqdQsMVtgsfH9VNLmZ3g7GhgNagfq7yvNCDcCHFK"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdAssertions.sol": {"keccak256": "0x4584f551c3a875c26423c1e50a77179bc2f9c9c1ee6d0f0c6be0f89ca5ee8270", "urls": ["bzz-raw://ef118876a862b60ba8a6612b8b3f3a6c226e11a0071a2e9695f021586d1bfdbe", "dweb:/ipfs/QmXd2vD91PkvoFWasFkgbDi89PctmgsHVxGvGNyhjmRK2b"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdChains.sol": {"keccak256": "0xcd3e64ec9ffa19a2c0715bbdaf7ddf28887cc418e079bec4373fd6a3f9961a7b", "urls": ["bzz-raw://e981a2ab738590928e9efa5f3d95a408c718eb12d73a113d7675f3ed55a026a1", "dweb:/ipfs/QmTgSEkWWsBRy32goRCaUkraSgpZHtgbZoKC3iEFNz5RDc"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdCheats.sol": {"keccak256": "0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746", "urls": ["bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41", "dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdError.sol": {"keccak256": "0xbf477b11a42d7611696956546bcfaa29317d1166bf65e402344599c05943fc77", "urls": ["bzz-raw://bc2e117d1135e030862b96a6526a43feb38d396cc79857f1fb696d4eff0e5fd6", "dweb:/ipfs/QmdSuQ5RrQudTLsNmWXGEeVJX8gR5U9XPm6m4dwwuQnJrj"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdInvariant.sol": {"keccak256": "0x4dab3021edfa9511bbdd80c48f060ef62eaf457f99eaf841f561fc2557d9a08d", "urls": ["bzz-raw://07668628673174cf8f27f8f4e1f862bab775013ec247eb34f698c5005f229391", "dweb:/ipfs/QmPJsiiYwmCZXMsHmQv5tg8VF3CAhNdat7WnKLTWZZH2v5"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdJson.sol": {"keccak256": "0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500", "urls": ["bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974", "dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdMath.sol": {"keccak256": "0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2", "urls": ["bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92", "dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdStorage.sol": {"keccak256": "0xb91ab24383a5872b894fc93325eef1add6cbbf981628f18e860068bf88bb7dcc", "urls": ["bzz-raw://2651e33b9ac8fc5803ed0a43078c1bf4fa90a0b0347aafd95776b02daccdcc57", "dweb:/ipfs/QmbdRyPuGJdZgnCuMnp7c1WsBo1Spf7j1KMmws1Z5rd4BQ"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdStyle.sol": {"keccak256": "0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d", "urls": ["bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8", "dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdToml.sol": {"keccak256": "0x58a72c765ed3f7ff6b105509689658795b8a3739b8931772a497155878381861", "urls": ["bzz-raw://b4a3746f4fabaeb980bd77d9e091d3904ee38a6c0e191bfa8ba6874c6f8558a3", "dweb:/ipfs/QmUfFDMEn461FgGEXt5HicyGD54sc28sLaQ9JRWDMBKed8"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdUtils.sol": {"keccak256": "0x7274081e11c05164fd8eadde4de8305c033e58a43008dea58065f3170ccf0737", "urls": ["bzz-raw://e9801614b6c9d3e472982e6cc68f5f1ad03682f84eafb686be65633c7132d138", "dweb:/ipfs/QmcQSUcrm2A7XuektnxJjvYGmZtBeA6LKPxCXRqdXBX776"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/Test.sol": {"keccak256": "0x3b4bb409a156dee9ce261458117fe9f81080ca844a8a26c07c857c46d155effe", "urls": ["bzz-raw://5792c69fe24bdc063a14e08fe68275007fdb1e5e7e343840a77938cb7e95a64e", "dweb:/ipfs/QmcAMhaurUwzhytJFYix4vRNeZeV8g27b8LnV3t7dvYtiK"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/Vm.sol": {"keccak256": "0x44bfadcf5a89b8058f80258f2259585c740f9cc45669a0579f4f2753ff2c6354", "urls": ["bzz-raw://bbc366c8b3499d5030e3b2e45bac23770531f2f5243a0e80e3d5a66b6f9a312c", "dweb:/ipfs/QmNxDEB3BaVnKzNaWedtdMshhvCEddB1AsdJZcsQx6jdtC"], "license": "MIT OR Apache-2.0"}, "lib/openzeppelin-contracts/lib/forge-std/src/console.sol": {"keccak256": "0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5", "urls": ["bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57", "dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/console2.sol": {"keccak256": "0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f", "urls": ["bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d", "dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/interfaces/IMulticall3.sol": {"keccak256": "0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a", "urls": ["bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0", "dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/safeconsole.sol": {"keccak256": "0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11", "urls": ["bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab", "dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3"], "license": "MIT"}, "test/InterestTests/InterestFixture.sol": {"keccak256": "0x458f1f72b1417a73ecdea81c25b269592e95c1808ca6aaa6b60a25243e143ed3", "urls": ["bzz-raw://abeb9b791b75f44d48f898182c673d80ea1c0f513fffe48a6834fdebebc6fdbe", "dweb:/ipfs/QmU92joERfyZJaTonAknmtRBkTjs5Jb7S2zM8Zk1XAnhwj"], "license": "GPL-3.0-only"}, "test/example/PeripheralDelegationContractExample.sol": {"keccak256": "0xf212fd0b2dd3a358c826623bd320e3aa0630892b9f6ba777b8126d3e2cfcfb14", "urls": ["bzz-raw://2ad79b2d7eb1b46c69383b9e9090bf2031ff779e46637d850b881db3dc84d797", "dweb:/ipfs/QmTPx8qw1zdYXRzjBnmuzMCt8yiErwFiLBk47xnbTm1erP"], "license": "GPL-3.0-only"}, "test/shared/FactoryPairTestFixture.sol": {"keccak256": "0x62487d7b3402461a61bd0c99be82302c8c1a94533c525a0ff6bcfe888af730a5", "urls": ["bzz-raw://dfd509aaec3469ed23d1cda8c6f603b7f0163fc29ec4ba6e4b06b60ca8fdc042", "dweb:/ipfs/QmTPDPM7kt77VNWr61MVZGmNZp67RG8jKzdmz7zwWep4GE"], "license": "GPL-3.0-only"}, "test/shared/StubErc20.sol": {"keccak256": "0xf3508dc98ae444d142d9993c52cebd856aba40c3e53d64bfeb63e71d190b12ee", "urls": ["bzz-raw://0cc01d254b6d5569d1cb426250db9df1b01afde9dd7b52e1efa0691112fcd918", "dweb:/ipfs/QmPnL9wFpSKXprrEFS9kkC2WzK2kAgWSH1snom1wiorCxn"], "license": "MIT"}, "test/shared/utilities.sol": {"keccak256": "0xc64b147bbe73bf59fdec4202c5b7c5dbcadd7550f4b2ea2390ea689e194d7cb8", "urls": ["bzz-raw://ab03a14b75d4b9df7795eeefd7e6d4a1d7af7b58ce948741cdd5d056a2c30416", "dweb:/ipfs/QmShemddxGaLyTGtC3yLdMtdHf9Gj3H8rjf2umzbFmP6aG"], "license": "GPL-3.0-only"}, "test/utils/DepletedAssetUtils.sol": {"keccak256": "0x2273187d5eb782fb341d44265bd6e8afcef18ab3cfabcb4a0b77a75f15298c42", "urls": ["bzz-raw://04d0a060b217603f6c7e38efc49be20932f08c56e46b1f9cea54eb722595458e", "dweb:/ipfs/QmdJHgaFbbtGDHPpHEFucTvrj4p4LT1piMPjtbrWBMXzAR"], "license": "GPL-3.0-only"}, "test/utils/constants.sol": {"keccak256": "0xe7d13ea4f26a2c43b7beed68c83a0e36555ead8f6bfd181430c74f853546fc34", "urls": ["bzz-raw://5098f47b615afa3d6489c2c8c2576f6202601fb15b1f32e6900639986e44f1fd", "dweb:/ipfs/QmPU1Ejtv4yY7eqjW1SpVgvS8vMqwyEjMeNGCLax3Mwk9d"], "license": "GPL-3.0-only"}}, "version": 1}, "id": 180}