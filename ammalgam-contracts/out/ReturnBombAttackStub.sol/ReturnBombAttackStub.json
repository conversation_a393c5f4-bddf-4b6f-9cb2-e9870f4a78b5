{"abi": [{"type": "function", "name": "symbol", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "pure"}], "bytecode": {"object": "0x6080604052348015600e575f5ffd5b506105bc8061001c5f395ff3fe608060405234801561000f575f5ffd5b5060043610610029575f3560e01c806395d89b411461002d575b5f5ffd5b61003561004b565b604051610042919061006e565b60405180910390f35b60606040518061052001604052806104e381526020016100a46104e39139905090565b602081525f82518060208401528060208501604085015e5f604082850101526040601f19601f8301168401019150509291505056fe566a737566626575716e767a6b656977616c7270687a746f7164636c6d6b79726978626f686d7266656e7971767767647a696c7966626b6e68616a67707478646d6f77736375626c69656b6d6672746e6a766873777a6f71676470786c726b63616e6d6a7679666965686c6e6f6274777a736b727061786d6364666775766f7168696a796e777a656c746270646d757371766b67687872616f79696a6263666c6e747677707a7365756b646d6f72687a61796a69636c666e626b76677078717274776d6f736465756e68616a6c7a6662766b72706869677178776379746d6e6f736a6c64616275666b76687870796574697a726d716e63667775676b7064616f62687a716a726c79736d69657674776e66786762756f636c7a6a7970686b61727165696d63646e74767378676f7972666a7577626c687a6b70646d76787471736977656e6d636e666462676c7a75766b72686f6a6174706579776978737a716f6675626c64676b6e6a6d7279636874767a71616f77666a72627068646d786c7a69656b7376746e79676371756f6d776272646a6b7066686c65697a6176717873676f6e6379746d75717270776a687a64666265696b766e6c7973786774636f767277616d7562706c6e7a7165696664686b6a6d77746379766f78726e6771706273647568657a6c6b6a79697378726361626f7767666870786574766e6471756d7a6c697664726b626e656a7377786779717a666f6163766a6c7072686b6d747762737a767579716f636e646c69656d6674766e727078687a73616b7767756264717965696c637672746d6e666a73786f6865707167776c7a646e626b61756d7679726874696a706c73637866676f777a6e626574716b76797564726d6878706c7377636e696f6a61677464717a66627876796572756f6b6d686e6c70647a6d6369676a7166626f7968786b7473647775656c616e6f706967726a637679746268666d6b6c737565716470777a6e7678637261746f696279666a6b6d78677a646e68736c6f70716575727677716d746a636b6e796668627a78646769706c61746e7379756f7772686d656b6c6a7178667a6370766267646e746f617772756d7965716873706c6b7a766a63667872676974646f716e626176796b77687a6c70786d73756563726a6f69677471666c77646e6d62796b787669757a707263616a656f736c746662676864776d746b71787563796e697a76617072656c6b667173626e64746d6f6a79786875696376776c6e7a6f786770726d6a61786473796b6c666562777a6e686f636776757269706d71656a79667a786164746b77636273676f6c68726e6d76796a706c75667762656b746178726e6a687a767179646d676f77747365696b666c62757a716e796a6870777264616d7663676f6678756c6b6274707a6e69767173796577726d6f6375686478616c706a6b7a746667696576626e797173776468636f61706c6d747275696b786a7a7662667771796568676e6373646c726f706b6d7578697a76747966656e626477686a72716b6c6d73637470786f756779696166687a6e766b726a6f6c7165626475777473707a79786d676e76686b62777266636c6a6f74797875707a696471736d65676e686c62616669766b72777074646d6f75786a79637a68716c6e6b6267656976726a61747079776473787a6d66636f6875716e65706c7476796b787369776d7264717a6a6f676261686663756ca264697066735822122037726cc5f36db672578e214025313ef02554f32c2ec538d26e4f4f4f95ffc1aa64736f6c634300081c0033", "sourceMap": "67:1379:186:-:0;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "67:1379:186:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;103:1341;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;;144:13;169:1268;;;;;;;;;;;;;;;;;;;103:1341;:::o;14:418:193:-;163:2;152:9;145:21;126:4;195:6;189:13;238:6;233:2;222:9;218:18;211:34;297:6;292:2;284:6;280:15;275:2;264:9;260:18;254:50;353:1;348:2;339:6;328:9;324:22;320:31;313:42;423:2;416;412:7;407:2;399:6;395:15;391:29;380:9;376:45;372:54;364:62;;;14:418;;;;:::o", "linkReferences": {}}, "methodIdentifiers": {"symbol()": "95d89b41"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"symbol\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"pure\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"test/stubs/ReturnBombAttackStub.sol\":\"ReturnBombAttackStub\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":1inch/=lib/1inch/\",\":@1inch/=lib/1inch/\",\":@mangrovedao/mangrove-core/=lib/mangrove-core/\",\":@mgv/forge-std/=lib/mangrove-core/lib/forge-std/src/\",\":@mgv/lib/=lib/mangrove-core/lib/\",\":@mgv/script/=lib/mangrove-core/script/\",\":@mgv/src/=lib/mangrove-core/src/\",\":@mgv/test/=lib/mangrove-core/test/\",\":@morpho-org/morpho-blue/=lib/morpho-blue/\",\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ExcessivelySafeCall/=lib/ExcessivelySafeCall/src/\",\":core/=lib/mangrove-core/lib/core/\",\":ds-test/=lib/ExcessivelySafeCall/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/openzeppelin-contracts/lib/forge-std/src/\",\":halmos-cheatcodes/=lib/morpho-blue/lib/halmos-cheatcodes/src/\",\":mangrove-core/=lib/mangrove-core/\",\":morpho-blue/=lib/morpho-blue/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":preprocessed/=lib/mangrove-core/lib/preprocessed/\"]},\"sources\":{\"test/stubs/ReturnBombAttackStub.sol\":{\"keccak256\":\"0x769a9e4cde92134574572b01c04564b201603549eda0a6fd7c0018c2bc7de534\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://326572383a04e6e0a574393dad30ce77d036588aa562cd9f821768f678368e0e\",\"dweb:/ipfs/QmatH6yd87AXr1d4wPK5oH9JpgN59ZsX5KV3QBqUsjCb7L\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "stateMutability": "pure", "type": "function", "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["1inch/=lib/1inch/", "@1inch/=lib/1inch/", "@mangrovedao/mangrove-core/=lib/mangrove-core/", "@mgv/forge-std/=lib/mangrove-core/lib/forge-std/src/", "@mgv/lib/=lib/mangrove-core/lib/", "@mgv/script/=lib/mangrove-core/script/", "@mgv/src/=lib/mangrove-core/src/", "@mgv/test/=lib/mangrove-core/test/", "@morpho-org/morpho-blue/=lib/morpho-blue/", "@openzeppelin/=lib/openzeppelin-contracts/", "ExcessivelySafeCall/=lib/ExcessivelySafeCall/src/", "core/=lib/mangrove-core/lib/core/", "ds-test/=lib/ExcessivelySafeCall/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/openzeppelin-contracts/lib/forge-std/src/", "halmos-cheatcodes/=lib/morpho-blue/lib/halmos-cheatcodes/src/", "mangrove-core/=lib/mangrove-core/", "morpho-blue/=lib/morpho-blue/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "preprocessed/=lib/mangrove-core/lib/preprocessed/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"test/stubs/ReturnBombAttackStub.sol": "ReturnBombAttackStub"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"test/stubs/ReturnBombAttackStub.sol": {"keccak256": "0x769a9e4cde92134574572b01c04564b201603549eda0a6fd7c0018c2bc7de534", "urls": ["bzz-raw://326572383a04e6e0a574393dad30ce77d036588aa562cd9f821768f678368e0e", "dweb:/ipfs/QmatH6yd87AXr1d4wPK5oH9JpgN59ZsX5KV3QBqUsjCb7L"], "license": "GPL-3.0-only"}}, "version": 1}, "id": 186}