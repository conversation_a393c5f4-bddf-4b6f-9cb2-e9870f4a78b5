{"abi": [{"type": "constructor", "inputs": [{"name": "s", "type": "string", "internalType": "string"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "SYMBOL", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}], "bytecode": {"object": "0x60c0604052600360809081526241424360e81b60a0525f9061002190826100f6565b5034801561002d575f5ffd5b506040516103e63803806103e683398101604081905261004c916101b0565b5f61005782826100f6565b5050610260565b634e487b7160e01b5f52604160045260245ffd5b600181811c9082168061008657607f821691505b6020821081036100a457634e487b7160e01b5f52602260045260245ffd5b50919050565b601f8211156100f157805f5260205f20601f840160051c810160208510156100cf5750805b601f840160051c820191505b818110156100ee575f81556001016100db565b50505b505050565b81516001600160401b0381111561010f5761010f61005e565b6101238161011d8454610072565b846100aa565b6020601f821160018114610155575f831561013e5750848201515b5f19600385901b1c1916600184901b1784556100ee565b5f84815260208120601f198516915b828110156101845787850151825560209485019460019092019101610164565b50848210156101a157868401515f19600387901b60f8161c191681555b50505050600190811b01905550565b5f602082840312156101c0575f5ffd5b81516001600160401b038111156101d5575f5ffd5b8201601f810184136101e5575f5ffd5b80516001600160401b038111156101fe576101fe61005e565b604051601f8201601f19908116603f011681016001600160401b038111828210171561022c5761022c61005e565b604052818152828201602001861015610243575f5ffd5b8160208401602083015e5f91810160200191909152949350505050565b6101798061026d5f395ff3fe608060405234801561000f575f5ffd5b5060043610610029575f3560e01c8063f76f8d781461002d575b5f5ffd5b61003561004b565b60405161004291906100d6565b60405180910390f35b5f80546100579061010b565b80601f01602080910402602001604051908101604052809291908181526020018280546100839061010b565b80156100ce5780601f106100a5576101008083540402835291602001916100ce565b820191905f5260205f20905b8154815290600101906020018083116100b157829003601f168201915b505050505081565b602081525f82518060208401528060208501604085015e5f604082850101526040601f19601f83011684010191505092915050565b600181811c9082168061011f57607f821691505b60208210810361013d57634e487b7160e01b5f52602260045260245ffd5b5091905056fea2646970667358221220290d114be7af333ac16ac0eee93d80c7e4b6b931fa9b23a113692ef755eb242864736f6c634300081c0033", "sourceMap": "145:28:189:-:0;58:194;145:28;;58:194;145:28;;;-1:-1:-1;;;145:28:189;;-1:-1:-1;;145:28:189;;-1:-1:-1;145:28:189;:::i;:::-;;180:70;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;233:6;:10;242:1;233:6;:10;:::i;:::-;;180:70;58:194;;14:127:193;75:10;70:3;66:20;63:1;56:31;106:4;103:1;96:15;130:4;127:1;120:15;146:380;225:1;221:12;;;;268;;;289:61;;343:4;335:6;331:17;321:27;;289:61;396:2;388:6;385:14;365:18;362:38;359:161;;442:10;437:3;433:20;430:1;423:31;477:4;474:1;467:15;505:4;502:1;495:15;359:161;;146:380;;;:::o;657:518::-;759:2;754:3;751:11;748:421;;;795:5;792:1;785:16;839:4;836:1;826:18;909:2;897:10;893:19;890:1;886:27;880:4;876:38;945:4;933:10;930:20;927:47;;;-1:-1:-1;968:4:193;927:47;1023:2;1018:3;1014:12;1011:1;1007:20;1001:4;997:31;987:41;;1078:81;1096:2;1089:5;1086:13;1078:81;;;1155:1;1141:16;;1122:1;1111:13;1078:81;;;1082:3;;748:421;657:518;;;:::o;1351:1299::-;1471:10;;-1:-1:-1;;;;;1493:30:193;;1490:56;;;1526:18;;:::i;:::-;1555:97;1645:6;1605:38;1637:4;1631:11;1605:38;:::i;:::-;1599:4;1555:97;:::i;:::-;1701:4;1732:2;1721:14;;1749:1;1744:649;;;;2437:1;2454:6;2451:89;;;-1:-1:-1;2506:19:193;;;2500:26;2451:89;-1:-1:-1;;1308:1:193;1304:11;;;1300:24;1296:29;1286:40;1332:1;1328:11;;;1283:57;2553:81;;1714:930;;1744:649;604:1;597:14;;;641:4;628:18;;-1:-1:-1;;1780:20:193;;;1898:222;1912:7;1909:1;1906:14;1898:222;;;1994:19;;;1988:26;1973:42;;2101:4;2086:20;;;;2054:1;2042:14;;;;1928:12;1898:222;;;1902:3;2148:6;2139:7;2136:19;2133:201;;;2209:19;;;2203:26;-1:-1:-1;;2292:1:193;2288:14;;;2304:3;2284:24;2280:37;2276:42;2261:58;2246:74;;2133:201;-1:-1:-1;;;;2380:1:193;2364:14;;;2360:22;2347:36;;-1:-1:-1;1351:1299:193:o;2655:935::-;2735:6;2788:2;2776:9;2767:7;2763:23;2759:32;2756:52;;;2804:1;2801;2794:12;2756:52;2831:16;;-1:-1:-1;;;;;2859:30:193;;2856:50;;;2902:1;2899;2892:12;2856:50;2925:22;;2978:4;2970:13;;2966:27;-1:-1:-1;2956:55:193;;3007:1;3004;2997:12;2956:55;3034:9;;-1:-1:-1;;;;;3055:30:193;;3052:56;;;3088:18;;:::i;:::-;3137:2;3131:9;3229:2;3191:17;;-1:-1:-1;;3187:31:193;;;3220:2;3183:40;3179:54;3167:67;;-1:-1:-1;;;;;3249:34:193;;3285:22;;;3246:62;3243:88;;;3311:18;;:::i;:::-;3347:2;3340:22;3371;;;3412:15;;;3429:2;3408:24;3405:37;-1:-1:-1;3402:57:193;;;3455:1;3452;3445:12;3402:57;3504:6;3499:2;3495;3491:11;3486:2;3478:6;3474:15;3468:43;3557:1;3531:19;;;3552:2;3527:28;3520:39;;;;3535:6;2655:935;-1:-1:-1;;;;2655:935:193:o;:::-;58:194:189;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "0x608060405234801561000f575f5ffd5b5060043610610029575f3560e01c8063f76f8d781461002d575b5f5ffd5b61003561004b565b60405161004291906100d6565b60405180910390f35b5f80546100579061010b565b80601f01602080910402602001604051908101604052809291908181526020018280546100839061010b565b80156100ce5780601f106100a5576101008083540402835291602001916100ce565b820191905f5260205f20905b8154815290600101906020018083116100b157829003601f168201915b505050505081565b602081525f82518060208401528060208501604085015e5f604082850101526040601f19601f83011684010191505092915050565b600181811c9082168061011f57607f821691505b60208210810361013d57634e487b7160e01b5f52602260045260245ffd5b5091905056fea2646970667358221220290d114be7af333ac16ac0eee93d80c7e4b6b931fa9b23a113692ef755eb242864736f6c634300081c0033", "sourceMap": "58:194:189:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;145:28;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;14:418:193:-;163:2;152:9;145:21;126:4;195:6;189:13;238:6;233:2;222:9;218:18;211:34;297:6;292:2;284:6;280:15;275:2;264:9;260:18;254:50;353:1;348:2;339:6;328:9;324:22;320:31;313:42;423:2;416;412:7;407:2;399:6;395:15;391:29;380:9;376:45;372:54;364:62;;;14:418;;;;:::o;437:380::-;516:1;512:12;;;;559;;;580:61;;634:4;626:6;622:17;612:27;;580:61;687:2;679:6;676:14;656:18;653:38;650:161;;733:10;728:3;724:20;721:1;714:31;768:4;765:1;758:15;796:4;793:1;786:15;650:161;;437:380;;;:::o", "linkReferences": {}}, "methodIdentifiers": {"SYMBOL()": "f76f8d78"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"string\",\"name\":\"s\",\"type\":\"string\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[],\"name\":\"SYMBOL\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"test/stubs/TokenWithStringCAPSSymbolStub.sol\":\"TokenWithStringCAPSSymbolStub\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":1inch/=lib/1inch/\",\":@1inch/=lib/1inch/\",\":@mangrovedao/mangrove-core/=lib/mangrove-core/\",\":@mgv/forge-std/=lib/mangrove-core/lib/forge-std/src/\",\":@mgv/lib/=lib/mangrove-core/lib/\",\":@mgv/script/=lib/mangrove-core/script/\",\":@mgv/src/=lib/mangrove-core/src/\",\":@mgv/test/=lib/mangrove-core/test/\",\":@morpho-org/morpho-blue/=lib/morpho-blue/\",\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ExcessivelySafeCall/=lib/ExcessivelySafeCall/src/\",\":core/=lib/mangrove-core/lib/core/\",\":ds-test/=lib/ExcessivelySafeCall/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/openzeppelin-contracts/lib/forge-std/src/\",\":halmos-cheatcodes/=lib/morpho-blue/lib/halmos-cheatcodes/src/\",\":mangrove-core/=lib/mangrove-core/\",\":morpho-blue/=lib/morpho-blue/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":preprocessed/=lib/mangrove-core/lib/preprocessed/\"]},\"sources\":{\"test/stubs/TokenWithStringCAPSSymbolStub.sol\":{\"keccak256\":\"0xaa95d1579f3c21b916891d3e7c48e1a6c94c3845b38cea0bb9a3b8c6f8fe5725\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8442d3775a0a1caf35d19cbe2b9396b5579bb0bec5111382d1166b516ef490f0\",\"dweb:/ipfs/QmZrNuxtPgXrnVW2tBzK1WLHiun9Vwge2954JzvzwuySvz\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "string", "name": "s", "type": "string"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "SYMBOL", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["1inch/=lib/1inch/", "@1inch/=lib/1inch/", "@mangrovedao/mangrove-core/=lib/mangrove-core/", "@mgv/forge-std/=lib/mangrove-core/lib/forge-std/src/", "@mgv/lib/=lib/mangrove-core/lib/", "@mgv/script/=lib/mangrove-core/script/", "@mgv/src/=lib/mangrove-core/src/", "@mgv/test/=lib/mangrove-core/test/", "@morpho-org/morpho-blue/=lib/morpho-blue/", "@openzeppelin/=lib/openzeppelin-contracts/", "ExcessivelySafeCall/=lib/ExcessivelySafeCall/src/", "core/=lib/mangrove-core/lib/core/", "ds-test/=lib/ExcessivelySafeCall/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/openzeppelin-contracts/lib/forge-std/src/", "halmos-cheatcodes/=lib/morpho-blue/lib/halmos-cheatcodes/src/", "mangrove-core/=lib/mangrove-core/", "morpho-blue/=lib/morpho-blue/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "preprocessed/=lib/mangrove-core/lib/preprocessed/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"test/stubs/TokenWithStringCAPSSymbolStub.sol": "TokenWithStringCAPSSymbolStub"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"test/stubs/TokenWithStringCAPSSymbolStub.sol": {"keccak256": "0xaa95d1579f3c21b916891d3e7c48e1a6c94c3845b38cea0bb9a3b8c6f8fe5725", "urls": ["bzz-raw://8442d3775a0a1caf35d19cbe2b9396b5579bb0bec5111382d1166b516ef490f0", "dweb:/ipfs/QmZrNuxtPgXrnVW2tBzK1WLHiun9Vwge2954JzvzwuySvz"], "license": "MIT"}}, "version": 1}, "id": 189}