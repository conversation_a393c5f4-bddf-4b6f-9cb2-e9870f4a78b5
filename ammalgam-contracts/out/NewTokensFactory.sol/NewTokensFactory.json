{"abi": [{"type": "constructor", "inputs": [{"name": "_liquidityTokenFactory", "type": "address", "internalType": "contract ITokenFactory"}, {"name": "_depositTokenFactory", "type": "address", "internalType": "contract ITokenFactory"}, {"name": "_borrowTokenFactory", "type": "address", "internalType": "contract ITokenFactory"}, {"name": "_liquidityDebtTokenFactory", "type": "address", "internalType": "contract ITokenFactory"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "createAllTokens", "inputs": [{"name": "pair", "type": "address", "internalType": "address"}, {"name": "pluginRegistry", "type": "address", "internalType": "address"}, {"name": "tokenX", "type": "address", "internalType": "address"}, {"name": "tokenY", "type": "address", "internalType": "address"}], "outputs": [{"name": "tokens", "type": "address[6]", "internalType": "contract IAmmalgamERC20[6]"}], "stateMutability": "nonpayable"}, {"type": "error", "name": "ERC20TokenFactoryFailed", "inputs": []}], "bytecode": {"object": "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", "sourceMap": "1044:4099:7:-:0;;;1467:428;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;;;;;1678:46:7;;;;;1734:42;;;;;1786:38;;;;1834:54;;;1044:4099;;14:192:193;108:13;;-1:-1:-1;;;;;150:31:193;;140:42;;130:70;;196:1;193;186:12;130:70;14:192;;;:::o;211:612::-;396:6;404;412;420;473:3;461:9;452:7;448:23;444:33;441:53;;;490:1;487;480:12;441:53;513:55;558:9;513:55;:::i;:::-;503:65;;587:64;647:2;636:9;632:18;587:64;:::i;:::-;577:74;;670:64;730:2;719:9;715:18;670:64;:::i;:::-;660:74;;753:64;813:2;802:9;798:18;753:64;:::i;:::-;743:74;;211:612;;;;;;;:::o;:::-;1044:4099:7;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "1044:4099:7:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1901:2514;;;;;;:::i;:::-;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;;2052:31;;:::i;:::-;2095:21;2119:29;2141:6;2119:21;:29::i;:::-;2095:53;;2158:21;2182:29;2204:6;2182:21;:29::i;:::-;2158:53;;2221:25;2263:7;2277;2249:36;;;;;;;;;:::i;:::-;;;;;;;;;;;;;2221:64;;2316:334;2349:21;2144:1:30;2411:229:7;;;;;;;;2444:4;-1:-1:-1;;;;;2411:229:7;;;;;2466:14;-1:-1:-1;;;;;2411:229:7;;;;;2535:11;2498:49;;;;;;;;:::i;:::-;;;;;;;;;;;;;2411:229;;;;2587:11;2565:34;;;;;;;;:::i;:::-;;;;;;;;;;;;;2411:229;;;;247:1:19;2411:229:7;;;2316:11;:334::i;:::-;2296:6;247:1:19;2296:17:7;;;:354;-1:-1:-1;;;;;2296:354:7;;;-1:-1:-1;;;;;2296:354:7;;;;;2680:318;2713:19;2747:6;2767:221;;;;;;;;2800:4;-1:-1:-1;;;;;2767:221:7;;;;;2822:14;-1:-1:-1;;;;;2767:221:7;;;;;2891:7;2854:45;;;;;;;;:::i;:::-;;;;;;;;;;;;;2767:221;;;;2939:7;2917:30;;;;;;;;:::i;:::-;;;;;;;;;;;;;2767:221;;;;279:1:19;2767:221:7;;;2680:11;:318::i;:::-;2660:6;279:1:19;2660:17:7;;;:338;-1:-1:-1;;;;;2660:338:7;;;-1:-1:-1;;;;;2660:338:7;;;;;3028:318;3061:19;3095:6;3115:221;;;;;;;;3148:4;-1:-1:-1;;;;;3115:221:7;;;;;3170:14;-1:-1:-1;;;;;3115:221:7;;;;;3239:7;3202:45;;;;;;;;:::i;:::-;;;;;;;;;;;;;3115:221;;;;3287:7;3265:30;;;;;;;;:::i;:::-;;;;;;;;;;;;;3115:221;;;;311:1:19;3115:221:7;;;3028:11;:318::i;:::-;3008:6;311:1:19;3008:17:7;;;:338;-1:-1:-1;;;;;3008:338:7;;;-1:-1:-1;;;;;3008:338:7;;;;;3375:314;3408:16;3439:6;3459:220;;;;;;;;3492:4;-1:-1:-1;;;;;3459:220:7;;;;;3514:14;-1:-1:-1;;;;;3459:220:7;;;;;3582:7;3546:44;;;;;;;;:::i;:::-;;;;;;;;;;;;;3459:220;;;;3631:7;3608:31;;;;;;;;:::i;:::-;;;;;;;;;;;;;3459:220;;;;373:1:19;3459:220:7;;;3375:11;:314::i;:::-;3356:6;373:1:19;3356:16:7;;;:333;-1:-1:-1;;;;;3356:333:7;;;-1:-1:-1;;;;;3356:333:7;;;;;3718:314;3751:16;3782:6;3802:220;;;;;;;;3835:4;-1:-1:-1;;;;;3802:220:7;;;;;3857:14;-1:-1:-1;;;;;3802:220:7;;;;;3925:7;3889:44;;;;;;;;:::i;:::-;;;;;;;;;;;;;3802:220;;;;3974:7;3951:31;;;;;;;;:::i;:::-;;;;;;;;;;;;;3802:220;;;;404:1:19;3802:220:7;;;3718:11;:314::i;:::-;3699:6;404:1:19;3699:16:7;;;:333;-1:-1:-1;;;;;3699:333:7;;;-1:-1:-1;;;;;3699:333:7;;;;;4061:347;4094:25;2144:1:30;4160:238:7;;;;;;;;4193:4;-1:-1:-1;;;;;4160:238:7;;;;;4215:14;-1:-1:-1;;;;;4160:238:7;;;;;4293:11;4247:58;;;;;;;;:::i;:::-;;;;;;;;;;;;;4160:238;;;;4346:11;4323:35;;;;;;;;:::i;:::-;;;;;;;;;;;;;4160:238;;;;342:1:19;4160:238:7;;;4061:11;:347::i;:::-;-1:-1:-1;;;;;4042:366:7;:16;;;:366;-1:-1:-1;4042:6:7;;1901:2514;-1:-1:-1;;;;;;1901:2514:7:o;345:1700:27:-;626:35;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;626:35:27;-1:-1:-1;;;626:35:27;;;416:13;;442:12;;;;477:194;;536:5;;555:6;;416:13;;477:45;:194::i;:::-;441:230;;;;687:7;682:271;;893:35;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;893:35:27;-1:-1:-1;;;893:35:27;;;728:214;;791:5;;814:6;;838:2;;728:45;:214::i;:::-;710:232;;-1:-1:-1;710:232:27;-1:-1:-1;682:271:27;967:7;:28;;;;;978:4;:11;993:2;978:17;967:28;963:597;;;1012:14;1028:11;1054:4;1043:36;;;;;;;;;;;;:::i;:::-;1011:68;;;;1097:6;1107:4;1097:14;:25;;;;;1121:1;1115:3;:7;1097:25;1093:457;;;1231:12;1235:3;1240:2;1231:3;:12::i;:::-;1225:18;;1261:23;1297:3;1287:14;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;1287:14:27;-1:-1:-1;1470:2:27;1460:13;;;;1454:20;1449:2;1433:19;;1426:49;-1:-1:-1;1261:40:27;;345:1700;-1:-1:-1;;;;;345:1700:27:o;1093:457::-;997:563;;963:597;1574:7;:28;;;;;1585:4;:11;1600:2;1585:17;1574:28;1570:439;;;1618:11;1647:106;1660:4;:11;1654:3;:17;:38;;;;;1688:4;1675:17;;:4;1680:3;1675:9;;;;;;;;:::i;:::-;;;;;-1:-1:-1;;;;;;1675:9:27;:17;;1654:38;:59;;;;;1709:4;1696:17;;:4;1701:3;1696:9;;;;;;;;:::i;:::-;;;;;-1:-1:-1;;;;;;1696:9:27;:17;;1654:59;1647:106;;;1733:5;;;;:::i;:::-;;;;1647:106;;;1771:7;;1767:232;;1798:19;1830:3;1820:14;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;1820:14:27;-1:-1:-1;1798:36:27;-1:-1:-1;1857:9:27;1852:94;1876:3;1872:1;:7;1852:94;;;1920:4;1925:1;1920:7;;;;;;;;:::i;:::-;;;;;;;;;1908:6;1915:1;1908:9;;;;;;;;:::i;:::-;;;;:19;-1:-1:-1;;;;;1908:19:27;;;;;;;;-1:-1:-1;1881:3:27;;1852:94;;;-1:-1:-1;1977:6:27;345:1700;-1:-1:-1;;;;;345:1700:27:o;1767:232::-;1604:405;1570:439;2026:12;2032:5;2026;:12::i;:::-;2019:19;345:1700;-1:-1:-1;;;;345:1700:27:o;4421:720:7:-;4557:14;4856:12;4870:17;4903:12;-1:-1:-1;;;;;4903:25:7;4952:34;;;4988:6;4996:5;4929:73;;;;;;;;;:::i;:::-;;;;-1:-1:-1;;4929:73:7;;;;;;;;;;;;;;-1:-1:-1;;;;;4929:73:7;-1:-1:-1;;;;;;4929:73:7;;;;;;;;;;4903:100;;;;4929:73;4903:100;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4855:148;;;;5018:7;5013:71;;5048:25;;-1:-1:-1;;;5048:25:7;;;;;;;;;;;5013:71;5111:4;5100:34;;;;;;;;;;;;:::i;:::-;5093:41;4421:720;-1:-1:-1;;;;;;4421:720:7:o;3411:1263:48:-;3579:4;3585:12;3645:15;3670:13;3693:24;3730:8;3720:19;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;3720:19:48;;3693:46;;4208:1;4179;4142:9;4136:16;4104:4;4093:9;4089:20;4051:7;4022:4;3994:239;3982:251;;4300:16;4289:27;;4344:8;4335:7;4332:21;4329:76;;;4383:8;4372:19;;4329:76;4490:7;4477:11;4470:28;4610:7;4607:1;4600:4;4587:11;4583:22;4568:50;4645:8;;;;-1:-1:-1;3411:1263:48;-1:-1:-1;;;;;;3411:1263:48:o;2848:103:27:-;2905:7;2935:1;2931;:5;:13;;2943:1;2931:13;;;2939:1;2931:13;2924:20;;2848:103;;;;;:::o;2051:141::-;2159:25;;2120:13;6755:15:193;;;-1:-1:-1;;6751:53:193;2159:25:27;;;6739:66:193;2120:13:27;2152:33;;6821:12:193;;2159:25:27;;;;;;;;;;;;2269:13;2294:16;2327:4;:11;2341:1;2327:15;;;;:::i;:::-;2323:19;;:1;:19;:::i;:::-;2313:30;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;2313:30:27;;2294:49;;-1:-1:-1;;;2353:3:27;2357:1;2353:6;;;;;;;;:::i;:::-;;;;:12;-1:-1:-1;;;;;2353:12:27;;;;;;;;;-1:-1:-1;;;2375:3:27;2379:1;2375:6;;;;;;;;:::i;:::-;;;;:12;-1:-1:-1;;;;;2375:12:27;;;;;;;;-1:-1:-1;2409:1:27;2397:9;2420:387;2444:4;:11;2440:1;:15;2420:387;;;2476:9;2506:1;2494:4;2499:1;2494:7;;;;;;;;:::i;:::-;;;;;;;;;2488:14;;:19;;;;2476:31;;;;2521:9;2539:4;2544:1;2539:7;;;;;;;;:::i;:::-;;;;;;;;2550:4;2533:21;;-1:-1:-1;2662:6:27;2666:2;2662:1;:6;:::i;:::-;2661:13;;2672:2;2661:13;:::i;:::-;2652:6;:1;2656:2;2652:6;:::i;:::-;:22;;;;:::i;:::-;2639:37;;2628:3;2632;;;;:::i;:::-;;;2628:8;;;;;;;;:::i;:::-;;;;:48;-1:-1:-1;;;;;2628:48:27;;;;;;;;-1:-1:-1;2724:6:27;2728:2;2724:1;:6;:::i;:::-;2723:13;;2734:2;2723:13;:::i;:::-;2714:6;:1;2718:2;2714:6;:::i;:::-;:22;;;;:::i;:::-;2701:37;;2690:3;2694;;;;:::i;:::-;;;2690:8;;;;;;;;:::i;:::-;;;;:48;-1:-1:-1;;;;;2690:48:27;;;;;;;;-1:-1:-1;;;2457:3:27;;2420:387;;;-1:-1:-1;2831:3:27;;2198:644;-1:-1:-1;;;2198:644:27:o;-1:-1:-1:-;;;;;;;;;;;;;;;;;;;;;;;;:::o;14:131:193:-;-1:-1:-1;;;;;89:31:193;;79:42;;69:70;;135:1;132;125:12;69:70;14:131;:::o;150:671::-;236:6;244;252;260;313:3;301:9;292:7;288:23;284:33;281:53;;;330:1;327;320:12;281:53;369:9;356:23;388:31;413:5;388:31;:::i;:::-;438:5;-1:-1:-1;495:2:193;480:18;;467:32;508:33;467:32;508:33;:::i;:::-;560:7;-1:-1:-1;619:2:193;604:18;;591:32;632:33;591:32;632:33;:::i;:::-;684:7;-1:-1:-1;743:2:193;728:18;;715:32;756:33;715:32;756:33;:::i;:::-;150:671;;;;-1:-1:-1;150:671:193;;-1:-1:-1;;150:671:193:o;826:521::-;1029:3;1014:19;;1018:9;1110:6;987:4;1144:197;1158:4;1155:1;1152:11;1144:197;;;1221:13;;-1:-1:-1;;;;;1217:39:193;1205:52;;1286:4;1277:14;;;;1314:17;;;;1253:1;1171:9;1144:197;;;1148:3;;;826:521;;;;:::o;1352:212::-;1394:3;1432:5;1426:12;1476:6;1469:4;1462:5;1458:16;1453:3;1447:36;1538:1;1502:16;;1527:13;;;-1:-1:-1;1502:16:193;;1352:212;-1:-1:-1;1352:212:193:o;1569:419::-;1838:3;1869:30;1895:3;1887:6;1869:30;:::i;:::-;-1:-1:-1;;;1915:5:193;1908:18;1942:40;1979:1;1972:5;1968:13;1960:6;1942:40;:::i;:::-;1935:47;1569:419;-1:-1:-1;;;;;1569:419:193:o;1993:335::-;-1:-1:-1;;;2240:3:193;2233:34;2215:3;2283:39;2318:2;2313:3;2309:12;2301:6;2283:39;:::i;2333:318::-;-1:-1:-1;;;2579:3:193;2572:19;2554:3;2607:38;2642:1;2637:3;2633:11;2625:6;2607:38;:::i;2656:127::-;2717:10;2712:3;2708:20;2705:1;2698:31;2748:4;2745:1;2738:15;2772:4;2769:1;2762:15;2788:335;-1:-1:-1;;;3035:3:193;3028:34;3010:3;3078:39;3113:2;3108:3;3104:12;3096:6;3078:39;:::i;3128:334::-;-1:-1:-1;;;3375:3:193;3368:33;3350:3;3417:39;3452:2;3447:3;3443:12;3435:6;3417:39;:::i;3467:319::-;-1:-1:-1;;;3713:3:193;3706:20;3688:3;3742:38;3777:1;3772:3;3768:11;3760:6;3742:38;:::i;3791:344::-;4043:30;4038:3;4031:43;4013:3;4090:39;4125:2;4120:3;4116:12;4108:6;4090:39;:::i;4140:343::-;4219:6;4227;4280:2;4268:9;4259:7;4255:23;4251:32;4248:52;;;4296:1;4293;4286:12;4248:52;-1:-1:-1;;4341:16:193;;4447:2;4432:18;;;4426:25;4341:16;;4426:25;;-1:-1:-1;4140:343:193:o;4488:127::-;4549:10;4544:3;4540:20;4537:1;4530:31;4580:4;4577:1;4570:15;4604:4;4601:1;4594:15;4620:127;4681:10;4676:3;4672:20;4669:1;4662:31;4712:4;4709:1;4702:15;4736:4;4733:1;4726:15;4752:135;4791:3;4812:17;;;4809:43;;4832:18;;:::i;:::-;-1:-1:-1;4879:1:193;4868:13;;4752:135::o;4892:300::-;4945:3;4983:5;4977:12;5010:6;5005:3;4998:19;5066:6;5059:4;5052:5;5048:16;5041:4;5036:3;5032:14;5026:47;5118:1;5111:4;5102:6;5097:3;5093:16;5089:27;5082:38;5181:4;5174:2;5170:7;5165:2;5157:6;5153:15;5149:29;5144:3;5140:39;5136:50;5129:57;;;4892:300;;;;:::o;5197:934::-;5422:2;5404:21;;;5465:13;;-1:-1:-1;;;;;5461:39:193;;;5441:18;;;5434:67;5559:4;5547:17;;5541:24;5537:50;5532:2;5517:18;;5510:78;5623:15;;5617:22;5488:3;5670;5655:19;;5648:33;-1:-1:-1;;5704:63:193;5762:3;5747:19;;5617:22;5704:63;:::i;:::-;5690:77;;5816:2;5808:6;5804:15;5798:22;5890:2;5886:7;5874:9;5866:6;5862:22;5858:36;5851:4;5840:9;5836:20;5829:66;5918:52;5963:6;5947:14;5918:52;:::i;:::-;6025:3;6013:16;;;;6007:23;6001:3;5986:19;;5979:52;-1:-1:-1;;;;;;;;6092:32:193;;;;6085:4;6070:20;;;6063:62;5904:66;5197:934::o;6136:190::-;6265:3;6290:30;6316:3;6308:6;6290:30;:::i;6331:274::-;6424:6;6477:2;6465:9;6456:7;6452:23;6448:32;6445:52;;;6493:1;6490;6483:12;6445:52;6525:9;6519:16;6544:31;6569:5;6544:31;:::i;:::-;6594:5;6331:274;-1:-1:-1;;;6331:274:193:o;6844:168::-;6917:9;;;6948;;6965:15;;;6959:22;;6945:37;6935:71;;6986:18;;:::i;7017:125::-;7082:9;;;7103:10;;;7100:36;;;7116:18;;:::i;7147:217::-;7187:1;7213;7203:132;;7257:10;7252:3;7248:20;7245:1;7238:31;7292:4;7289:1;7282:15;7320:4;7317:1;7310:15;7203:132;-1:-1:-1;7349:9:193;;7147:217::o", "linkReferences": {}, "immutableReferences": {"4592": [{"start": 160, "length": 32}], "4595": [{"start": 352, "length": 32}, {"start": 546, "length": 32}], "4598": [{"start": 740, "length": 32}, {"start": 934, "length": 32}], "4601": [{"start": 1128, "length": 32}]}}, "methodIdentifiers": {"createAllTokens(address,address,address,address)": "3fe39c9b"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"contract ITokenFactory\",\"name\":\"_liquidityTokenFactory\",\"type\":\"address\"},{\"internalType\":\"contract ITokenFactory\",\"name\":\"_depositTokenFactory\",\"type\":\"address\"},{\"internalType\":\"contract ITokenFactory\",\"name\":\"_borrowTokenFactory\",\"type\":\"address\"},{\"internalType\":\"contract ITokenFactory\",\"name\":\"_liquidityDebtTokenFactory\",\"type\":\"address\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[],\"name\":\"ERC20TokenFactoryFailed\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"pair\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"pluginRegistry\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"tokenX\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"tokenY\",\"type\":\"address\"}],\"name\":\"createAllTokens\",\"outputs\":[{\"internalType\":\"contract IAmmalgamERC20[6]\",\"name\":\"tokens\",\"type\":\"address[6]\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{\"createAllTokens(address,address,address,address)\":{\"params\":{\"tokenX\":\"The address of tokenX.\",\"tokenY\":\"The address of tokenY.\"},\"returns\":{\"tokens\":\"An array of IAmmalgamERC20 tokens consisting of [liquidityToken, depositXToken, depositYToken, borrowXToken, borrowYToken, borrowLToken].\"}}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"createAllTokens(address,address,address,address)\":{\"notice\":\"Creates new instances of AmmalgamERC20 tokens for the given token addresses.\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/factories/NewTokensFactory.sol\":\"NewTokensFactory\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":1inch/=lib/1inch/\",\":@1inch/=lib/1inch/\",\":@mangrovedao/mangrove-core/=lib/mangrove-core/\",\":@mgv/forge-std/=lib/mangrove-core/lib/forge-std/src/\",\":@mgv/lib/=lib/mangrove-core/lib/\",\":@mgv/script/=lib/mangrove-core/script/\",\":@mgv/src/=lib/mangrove-core/src/\",\":@mgv/test/=lib/mangrove-core/test/\",\":@morpho-org/morpho-blue/=lib/morpho-blue/\",\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ExcessivelySafeCall/=lib/ExcessivelySafeCall/src/\",\":core/=lib/mangrove-core/lib/core/\",\":ds-test/=lib/ExcessivelySafeCall/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/openzeppelin-contracts/lib/forge-std/src/\",\":halmos-cheatcodes/=lib/morpho-blue/lib/halmos-cheatcodes/src/\",\":mangrove-core/=lib/mangrove-core/\",\":morpho-blue/=lib/morpho-blue/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":preprocessed/=lib/mangrove-core/lib/preprocessed/\"]},\"sources\":{\"contracts/factories/ERC20DebtLiquidityTokenFactory.sol\":{\"keccak256\":\"0x72e3ada6a2f0792a353b730c1b45ae832f9ce2f58f0bda039383f8890cb2a4f7\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://4949e7b66647313aaba2e11d7edde06eb87345b476c1a20f890659c1af827b2b\",\"dweb:/ipfs/Qmf3emVXfGp1oc8iVYxnVqpJ88vnxxdj7WqPm1vzVKb1SD\"]},\"contracts/factories/ERC20LiquidityTokenFactory.sol\":{\"keccak256\":\"0x762974ca1ed600e0930a92bd2eb3a1a5f9ef0469ab2e6e811e4674e098238762\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://5fd5f33537aeea9bac1f18c6fca2057899ec5f90cb8c756622eb436d5b13e27e\",\"dweb:/ipfs/QmfYznzzwN1AmdnuzNKe1R6t8UeztaZVGuzJ8vKfzjMXYN\"]},\"contracts/factories/ERC4626DebtTokenFactory.sol\":{\"keccak256\":\"0x7deeb7a40d26bc790112f29836da83050fa3554e471e1dce4dda6bf29ab9bf67\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://5a46a4c8270e0b8a731259328b6c35c84de270a14f2f69ba04bc58d18400efc6\",\"dweb:/ipfs/QmQ56QbX6S9GjQinsFYtTMns6HgpcTXW1wnvQT6QgiuW1Z\"]},\"contracts/factories/ERC4626DepositTokenFactory.sol\":{\"keccak256\":\"0xf84b75119f2680f8079bb9567b0c03c0ad49b71a8c00f968d03d5fca2a954035\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://c3fc7a9e300a935991746d5be835418b09e6d2b20b65e3e297d4faf28516469b\",\"dweb:/ipfs/QmQMr9MA5a3UcZCiP3e2haYqzBsbE8Pe6rDq6j6RJ3ub4Z\"]},\"contracts/factories/NewTokensFactory.sol\":{\"keccak256\":\"0x86cd420e1df8a59b11a4ab53a16971a44953f0a07741ef69d95baa4bd60126ac\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://d8cdd98060f059705b9ae2b64ab3e74395c0f3a24e12f5ac11ca7e509c6a7aa0\",\"dweb:/ipfs/QmahgKkRzuWHpQ73DHGZ4Kvd2MQG7MpfPShayJDRJQYSVr\"]},\"contracts/interfaces/IAmmalgamPair.sol\":{\"keccak256\":\"0xa17e45b2348d8920d9970c5d50b300fc0a1e8d03350cdd0d1a624494baa70337\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://8d252e89e5d49d1c15a0c0c0a495a325b9f8d608714b29279a7bacb1e4bf8795\",\"dweb:/ipfs/QmRkZ7a8JJQYEw6HQMJjjkuAK8b5Th1X1ET6BG1R8mx4qw\"]},\"contracts/interfaces/callbacks/ITransferValidator.sol\":{\"keccak256\":\"0x6d9028fc4ad1914e6b2091e6ba46a9f836f9e67ea435c4a8fef41363f2ceaf56\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://7ecaade4884d460168f6978edf35706f7b9e363de6002942b1d171a338dca6a4\",\"dweb:/ipfs/QmS5wgfDt5Pn68rpCytpzhiy57LcmivVFQ5XLGXUUP5Tt8\"]},\"contracts/interfaces/factories/INewTokensFactory.sol\":{\"keccak256\":\"0x3b2f1ee34106d2694a9ebbe600be692bed645f4247f4a24da3d5ec46025ab3e9\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://73143452a06db52deb593585fea6f2ef7c46e9ef6d649562dc39e79e4e5dca2b\",\"dweb:/ipfs/QmYQEy7BZWnfWKnuac8GB4QPhG5qJpaHQAfkTBoUDEuX1E\"]},\"contracts/interfaces/factories/ITokenFactory.sol\":{\"keccak256\":\"0xac23e5c0441599add526b0c308faa7787f90bf01603b6dbc231944c166ca32d6\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://ac574b98b2c1034786581137a218277ec58e06e9612f76814f34960383083626\",\"dweb:/ipfs/QmZgZqVnshjzuHBXJTR9g87S15CyLwJUSErGEDoJpBd4kg\"]},\"contracts/interfaces/tokens/IAmmalgamERC20.sol\":{\"keccak256\":\"0x44a376269170b4270ec221ce3cb31a609b394e216cc4d2e27b818361b4369829\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://c48bc7586631f27ede73d3d0b4c1d7a29b1653e6c501c8b7fc9877c125f8f57e\",\"dweb:/ipfs/QmTSLtqnsxr7h7ct524rqYssHUo4qursmCZ7g5q3J1qQPK\"]},\"contracts/interfaces/tokens/IERC20DebtToken.sol\":{\"keccak256\":\"0xc50c6be17633c8ac677b4eaac7c05a6de1f1c938237179b59ad5e65bcfbcb03a\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://6c75327e01d70a0c22662a9a8214aa64e45c517146971f8636e5aa5bf06e7696\",\"dweb:/ipfs/QmV2ydBQ5S9ZBtRuPgBMBdvd2Hcnn8quCGEMhhAAuic15b\"]},\"contracts/interfaces/tokens/IPluginRegistry.sol\":{\"keccak256\":\"0x9a677620d88ac7dc42afb21d82a7b7a89bd934c1cada5450cf2b6200bf374ccf\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://304091e5d54e9ad7db24ba1022e84e39dd9305d9cc72fd87423c71b40de4ab3d\",\"dweb:/ipfs/QmZEF5MfmUcxzLF9fcGCLvGMTTLLhcWdCMCDK2WyXj6s7X\"]},\"contracts/interfaces/tokens/ITokenController.sol\":{\"keccak256\":\"0x7778001aaf582fe10005240eb6023b2b6cee3f100b6c2222bf6b9ade93732624\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://91e5c4519207d6a450be1e0a8649157e86d20f8ef6a91ff6512a31cf5561a570\",\"dweb:/ipfs/QmUqZLW27JJZHFPf2fgLDYSWWj5gM158DdaxTTmDVukRAg\"]},\"contracts/libraries/Convert.sol\":{\"keccak256\":\"0x944776d31291de1a9cdc6a52154c23c22b43a01c3edebe7a4140e267edbba975\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://36c03749859077ba47a3acfd574f8c30f34f97def4ce81d7f4feac9a7b62794c\",\"dweb:/ipfs/QmdycZay5X2WrbS8qS7RycLpZbMQx7yKszWQzGU3rqidpH\"]},\"contracts/libraries/TokenSymbol.sol\":{\"keccak256\":\"0x628df064fdbdacfe6783964d7bf38cdf1b34e1ad07caa3cea39bf7468cc19b43\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://da6823ce0debaabe20f25281e81a4fc88de98d4df2942a5e276826ac381c227b\",\"dweb:/ipfs/QmNpEuQ25788xfcJwPk2xUB7fyP7fW5ENK2e9qgRqp1BcH\"]},\"contracts/libraries/constants.sol\":{\"keccak256\":\"0x0dfb294985a8f48287ff13e8476718ddb5334b1d8bf6bfa59a5db1dbcf6ca7c4\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://4bedcfdb2850cfb22b5daa768ab8125b4ccab97c90068d1d0ad4495bf942b362\",\"dweb:/ipfs/Qmf9p88yQN2JYRBR5D7q9BLmwhDJWpFk47ZuayrKqCyHat\"]},\"contracts/tokens/ERC20Base.sol\":{\"keccak256\":\"0xdd3db9eaa855b6ff747ffaa0e74dd2a64dd5b0d704356acb75c2690c3fc6bc2b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8f9e05ae3078bc3a29ef43861b006fb290606c1c9b2676baaed3ab98ecdb2d59\",\"dweb:/ipfs/QmZrvgxYtUD6jQVBvM9rT7jj5Vzb5csiThGj3ZwHSPryAL\"]},\"contracts/tokens/ERC20DebtBase.sol\":{\"keccak256\":\"0xc0a59cd54fcd847b160d662aa45a5fe7d24ed90c8030fe17fd5f9def427ed19a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://365c7f18505af36b2806404b1b3f2d897de6ac18e255ecfbb4ccc491cac7e444\",\"dweb:/ipfs/QmUqx8EBwRb6W1YQPb9MjwAhEEHNpZTCopbGWb1vbyuUpp\"]},\"contracts/tokens/ERC20DebtLiquidityToken.sol\":{\"keccak256\":\"0xf222ad5562ed41d74b0cfb5b4aad84ec9f4cb91b6d71928b30b018bab494efe8\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://a8e8f3e7ded2eae04c63ce3ae7a86c051d48d6db697cb6929d7064a4ec9d7371\",\"dweb:/ipfs/QmU3EuwHU3xB1e6MxaRjSRJcDMK73wfZig9uGWqZPaHnTn\"]},\"contracts/tokens/ERC20LiquidityToken.sol\":{\"keccak256\":\"0x2bb2429e551c031034c747749373d2e4c451580e9b203b689d6eaf03ad896358\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9ad5902756073578beee9068b74bd921e59a36b803cf34ef01570c670363689e\",\"dweb:/ipfs/QmTkT5K2XcB3ZbPDqd4ZAfnZMp2reCzu3Pv7JpRqhAtZHP\"]},\"contracts/tokens/ERC4626DebtToken.sol\":{\"keccak256\":\"0xe69b1ed2fb7b2d7c24c6838462001988b8e51795d215cfa74b9874d17257509e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c4f201e5f5621689046c58863ab9270cf770c68810d52269d1fc2ac93a7ccf96\",\"dweb:/ipfs/QmdtALf6LQdHhce3HsNdVtomQu8e5F5QcYU6S7H1PeBThZ\"]},\"contracts/tokens/ERC4626DepositToken.sol\":{\"keccak256\":\"0xd914aa43dc5e9f2f02f98b05561faf6f00853b701f51dfcd7a08a31feaf220be\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8c2282d40855587b2ac70e89d4e0e147b9afe32a41245fffc96b923a9e5ce7ac\",\"dweb:/ipfs/QmVn3tBkZcXKnqjfnLTXFkKtu1EetDL1UF7rRjNrHdRCSM\"]},\"lib/1inch/solidity-utils/contracts/libraries/AddressArray.sol\":{\"keccak256\":\"0x7895eaf7b55d9612b22ec586970488de51436c021b9f9414b3c27c3583c8856e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e43897dfeff84e2983b017ab100d74061a6b9bed4618ec37a7cbb68bf064ac22\",\"dweb:/ipfs/Qmejoj4CuNr1giwGBDG7SgRtfYmi64fgy2DsGP2AAW9gH9\"]},\"lib/1inch/solidity-utils/contracts/libraries/AddressSet.sol\":{\"keccak256\":\"0xbb8e2a541ac268f00f382c9fba9403b3ec5b58a48dc7236920d7c87817f93318\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://614b60ff60f75c46aa41d00ec5dd78136d42e0d6035aa2331d16f26d2f5f5368\",\"dweb:/ipfs/QmUJWqcx2heKcmBhxpHfAnpKrwnevqtAwaQKT7Bmpke5NB\"]},\"lib/1inch/token-plugins/contracts/ERC20Hooks.sol\":{\"keccak256\":\"0xd2657f278b2ed4667663344aa06c02b52b15862c69c215570de329aa1a4683a2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://4ffb90146369b7fa890489a0d3ecab5aa81c9f47b3ec7a1776fd8bc14eaa33d5\",\"dweb:/ipfs/QmWo24VHySo9jQBeXfE87Z3Hh576cNKcwccLrBAMsfax1c\"]},\"lib/1inch/token-plugins/contracts/interfaces/IERC20Hooks.sol\":{\"keccak256\":\"0x2fb4fcbf91a7edf36e7ada3f578a8de1aee7ebdd12460648d3e09d4448351875\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d09a05543afcad3c0c2d2b9013647408cc44e1b17f362665ee20f9abed5964e8\",\"dweb:/ipfs/QmX3MMGsVJSfsrVoYY2jWK2BXd3i6scBhQQxytk461mizh\"]},\"lib/1inch/token-plugins/contracts/interfaces/IHook.sol\":{\"keccak256\":\"0x474893cc48ee17530ad0e978ecbccaa726943615a5736260187a37b8e133ee80\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://511ed52ec427cced459878f28453790817d4cbacc8601d77eb8dfa28e2e0b30d\",\"dweb:/ipfs/QmXvbB1bAZZanGepiUxWh3zZQUaHQZeYpR1UdaW4w5yKVS\"]},\"lib/1inch/token-plugins/contracts/libs/ReentrancyGuard.sol\":{\"keccak256\":\"0xa88ccab1ee6b34a9007986ca15ea5fd5580864029130eb38333183af1bc4f66c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b5ad18c862e59b0f24edc900519a55c7aee5537a42d039d6ea5e34df40267bb0\",\"dweb:/ipfs/QmVMRSjUEPxCno1sFdhWvpRqAqoqns8zwVyd7yCHYC6P8Z\"]},\"lib/ExcessivelySafeCall/src/ExcessivelySafeCall.sol\":{\"keccak256\":\"0x7d9d432e8f02168bf3f790e3dabcf36402782acf7ffa476cabe86fc4d8962eb2\",\"license\":\"MIT OR Apache-2.0\",\"urls\":[\"bzz-raw://1adc13e7f399f500ea5f81480ad149a50408fde7990a2c6347e6377486f389dc\",\"dweb:/ipfs/QmSvm5TUBJqknsqNJLLHqNS4MLSH5k3vNrbquVg6ZKSfx9\"]},\"lib/openzeppelin-contracts/contracts/access/Ownable.sol\":{\"keccak256\":\"0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6\",\"dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC1363.sol\":{\"keccak256\":\"0xc15298eb2b9ba5e18a8c9d12f93ad17a3e162a5c1d9b85f54c8adb5827b0d4da\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1f3c3d8f81d2daf1231890a6a2f897be365d6a479b53dcd52ec2527b5d3faf41\",\"dweb:/ipfs/QmeNdkd6u4at9pd2GAyyqxzrVGGvxfLpGmAKnFoYM5ya2e\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC165.sol\":{\"keccak256\":\"0x81b022028c39007cce9920c394b9cddd1cb9f3a1c0398f254b4a6492df92ad2b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e0b61b8a5c69b4df993c3d6f94c174ab293aa8698d149bce7be2d88f82929beb\",\"dweb:/ipfs/QmbtacmB1k8ginfrHvAJpjVeqnjYGfXYrkXmMPYEb83z4t\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC20.sol\":{\"keccak256\":\"0xb5d81383d40f4006d1ce4bbad0064e7a930e17302cbe2a745e09cb403f042733\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3fc4a5681c2f00f41f49260a36ae6bbe1121dd93d470ea24d51d556eff2980be\",\"dweb:/ipfs/QmUBW6TwVWtGP96ka9TfuGivd27kH8CtkXD8RQAAecSFiR\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC4626.sol\":{\"keccak256\":\"0x932fe72aeda629f70ee2ca902cfd8cce9faa0a13b39222c240979f42984c4541\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://4e9e0a320185800eb9a6d57ef0e4ccf3bb32a6b5dd44882f7552617c30d4a05e\",\"dweb:/ipfs/QmYJVpT7DDPWx3DWro8vtM6Gqre2AyufsyCYoHm9cfQ1vr\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC5267.sol\":{\"keccak256\":\"0xf29e9088951d8a2074d872a733674618fe5c164df21b8b5cf4a6295f523ba7ad\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://562a1abc7ea505582827ce0c9a2f778360a1a8242742683af179930640020215\",\"dweb:/ipfs/QmPjx5f6KKaPfsDi1uV3ovQN9gHTAcNkMAFJZxE1Adw6VT\"]},\"lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol\":{\"keccak256\":\"0x85cf779582f7860b00bba1d259be00e754bfbac3c2339e324d0113d682d9e9f9\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://2ddf369affd106e2a9e2b8a22a6ce3da8a6ccda14c6ba5b8c87f6b08169e6318\",\"dweb:/ipfs/QmNadAttd47ycHShxhk33JUJhrbzmyZQ7mHs7WEyG4Qkmp\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol\":{\"keccak256\":\"0x5aad1745bddba11752c3962464a3b12e0af079310cc22d1f43f0388ae1aaf8db\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://577fad916bfdfe89aadf2685322fec7562cb0ed87722923085213cd9f85d7b79\",\"dweb:/ipfs/QmSM3J6PjrAUyEoNbdhq1ECZLXczKdCTzZTBUieKHsBYEL\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0xfe37358f223eddd65d61bb62b0b7bdb69d7101b5ec8d484292b8c1583a153b8a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://28dd43f30af3c12ae0fc08dd031b1250e906ef3c95f63f30fac6fd15aee2a662\",\"dweb:/ipfs/QmUkSyWsSRx36w1ti7U6qnGnQgJq16wpMhjeJrnyn9AXwG\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Permit.sol\":{\"keccak256\":\"0x6485b101d7335f0fd25abc996c5e2fc965e72e5fbd0a7ad1a465bd3f012b5fd8\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1f8d296121044e697bcd34c4cd394cebd46fc30b04ce94fccff37200872e6834\",\"dweb:/ipfs/QmTNdmLdoHgMzoCDZ8Txk9aYvwtuyeJYHf5mjLWgzGTZAu\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC4626.sol\":{\"keccak256\":\"0x0d99c706d010fa15de36e7e7b7a03dd0fdc9bcec52f9f812ef80ec7f3fc6fa63\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://bee73c81a2964e8da0537de14082e60d64cd7b1cd9162adc04b58317e334c896\",\"dweb:/ipfs/QmbQ75T9PEJuiLk1kypX68rEBFtTaEzPWsy8Dv99buqVPH\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol\":{\"keccak256\":\"0xbaffa0bcc92adf28a53cc3b68551fc3632cb8f849a0028cb8d5c06e4677715e9\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://32e6f8f6b2e883c85e6a602c0882d9962ce2f92406961244e86cd974df815912\",\"dweb:/ipfs/Qmahvx6fPpecicq1aUE1JihCxV5ep1bfuPukzrxa8Ub5PS\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Permit.sol\":{\"keccak256\":\"0x093f32ab700c2b05373387263915a75f5455cdb0f09a7630cc621e27b7b50d04\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d163e6ef21df143969df5557305e8c643a135c7660a678d0c65dca91772114a0\",\"dweb:/ipfs/QmTZUgiwEro5oLRhbJ2iSWyCqu1JTDekoFHALVUn4eHqYK\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol\":{\"keccak256\":\"0x1183b415155c1a7bf56d45edad5b17caf0da70935ac420698cbe8afb6750cbb2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://21d9edaeb3e5e8f93eb0fdab41530654e8169b1990b3bbfcf5e4527c52aa03f5\",\"dweb:/ipfs/QmWrqpNW3x5k3pTjvrT8XU1hauHnXTjqaPL2tfzMuWYosj\"]},\"lib/openzeppelin-contracts/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]},\"lib/openzeppelin-contracts/contracts/utils/Nonces.sol\":{\"keccak256\":\"0x0082767004fca261c332e9ad100868327a863a88ef724e844857128845ab350f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://132dce9686a54e025eb5ba5d2e48208f847a1ec3e60a3e527766d7bf53fb7f9e\",\"dweb:/ipfs/QmXn1a2nUZMpu2z6S88UoTfMVtY2YNh86iGrzJDYmMkKeZ\"]},\"lib/openzeppelin-contracts/contracts/utils/Panic.sol\":{\"keccak256\":\"0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a\",\"dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG\"]},\"lib/openzeppelin-contracts/contracts/utils/ShortStrings.sol\":{\"keccak256\":\"0x1fcf8cceb1a67e6c8512267e780933c4a3f63ef44756e6c818fda79be51c8402\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://617d7d57f6f9cd449068b4d23daf485676d083aae648e038d05eb3a13291de35\",\"dweb:/ipfs/QmPADWPiGaSzZDFNpFEUx4ZPqhzPkYncBpHyTfAGcfsqzy\"]},\"lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol\":{\"keccak256\":\"0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b\",\"dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM\"]},\"lib/openzeppelin-contracts/contracts/utils/Strings.sol\":{\"keccak256\":\"0x1402d9ac66fbca0a2b282cd938f01f3cd5fb1e4c696ed28b37839401674aef52\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d3e6c46b6d1ea36bd73e0ac443a53504089167b98baa24923d702a865a38d211\",\"dweb:/ipfs/QmdutUpr5KktmvgtqG2v96Bo8nVKLJ3PgPedxbsRD42CuQ\"]},\"lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol\":{\"keccak256\":\"0x6c29257484c0595ca5af8844fafe99cc5eace7447c9f5bced71d6b3a19a6a2a5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://cce7ac0bdb05f73c0918e362dea2e52426e00ddf0a1018f14accdcf78c6eb6e4\",\"dweb:/ipfs/QmbkNq5dDxww27FzFFiKgW3S7C5VoZpjdZGpSCtsb9hP32\"]},\"lib/openzeppelin-contracts/contracts/utils/cryptography/EIP712.sol\":{\"keccak256\":\"0xda8013da608bda3c9eaa9e59053d38d7888e64bb40aa557e5929cd702f8de87e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3ea13234c6b00ae79dc1a98e7e7f2faf38d37e76a687ccd0c95ad84b03ea570f\",\"dweb:/ipfs/QmWtdefDm5jiEzAjmfPMZ5B1NKVxFoMiD5ZoD68hcNTHun\"]},\"lib/openzeppelin-contracts/contracts/utils/cryptography/MessageHashUtils.sol\":{\"keccak256\":\"0x26670fef37d4adf55570ba78815eec5f31cb017e708f61886add4fc4da665631\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b16d45febff462bafd8a5669f904796a835baf607df58a8461916d3bf4f08c59\",\"dweb:/ipfs/QmU2eJFpjmT4vxeJWJyLeQb8Xht1kdB8Y6MKLDPFA9WPux\"]},\"lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol\":{\"keccak256\":\"0x4ee0e04cc52827588793a141d5efb9830f179a17e80867cc332b3a30ceb30fd9\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://17d8f47fce493b34099ed9005c5aee3012488f063cfe1c34ed8f9e6fc3d576e5\",\"dweb:/ipfs/QmZco2GbZZhEMvG3BovyoGMAFKvfi2LhfNGQLn283LPrXf\"]},\"lib/openzeppelin-contracts/contracts/utils/math/Math.sol\":{\"keccak256\":\"0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3\",\"dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB\"]},\"lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol\":{\"keccak256\":\"0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8\",\"dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy\"]},\"lib/openzeppelin-contracts/contracts/utils/math/SignedMath.sol\":{\"keccak256\":\"0xb1970fac7b64e6c09611e6691791e848d5e3fe410fa5899e7df2e0afd77a99e3\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://db5fbb3dddd8b7047465b62575d96231ba8a2774d37fb4737fbf23340fabbb03\",\"dweb:/ipfs/QmVUSvooZKEdEdap619tcJjTLcAuH6QBdZqAzWwnAXZAWJ\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "contract ITokenFactory", "name": "_liquidityTokenFactory", "type": "address"}, {"internalType": "contract ITokenFactory", "name": "_depositTokenFactory", "type": "address"}, {"internalType": "contract ITokenFactory", "name": "_borrowTokenFactory", "type": "address"}, {"internalType": "contract ITokenFactory", "name": "_liquidityDebtTokenFactory", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "type": "error", "name": "ERC20TokenFactoryFailed"}, {"inputs": [{"internalType": "address", "name": "pair", "type": "address"}, {"internalType": "address", "name": "pluginRegistry", "type": "address"}, {"internalType": "address", "name": "tokenX", "type": "address"}, {"internalType": "address", "name": "tokenY", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "createAllTokens", "outputs": [{"internalType": "contract IAmmalgamERC20[6]", "name": "tokens", "type": "address[6]"}]}], "devdoc": {"kind": "dev", "methods": {"createAllTokens(address,address,address,address)": {"params": {"tokenX": "The address of tokenX.", "tokenY": "The address of tokenY."}, "returns": {"tokens": "An array of IAmmalgamERC20 tokens consisting of [liquidityToken, depositXToken, depositYToken, borrowXToken, borrowYToken, borrowLToken]."}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"createAllTokens(address,address,address,address)": {"notice": "Creates new instances of AmmalgamERC20 tokens for the given token addresses."}}, "version": 1}}, "settings": {"remappings": ["1inch/=lib/1inch/", "@1inch/=lib/1inch/", "@mangrovedao/mangrove-core/=lib/mangrove-core/", "@mgv/forge-std/=lib/mangrove-core/lib/forge-std/src/", "@mgv/lib/=lib/mangrove-core/lib/", "@mgv/script/=lib/mangrove-core/script/", "@mgv/src/=lib/mangrove-core/src/", "@mgv/test/=lib/mangrove-core/test/", "@morpho-org/morpho-blue/=lib/morpho-blue/", "@openzeppelin/=lib/openzeppelin-contracts/", "ExcessivelySafeCall/=lib/ExcessivelySafeCall/src/", "core/=lib/mangrove-core/lib/core/", "ds-test/=lib/ExcessivelySafeCall/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/openzeppelin-contracts/lib/forge-std/src/", "halmos-cheatcodes/=lib/morpho-blue/lib/halmos-cheatcodes/src/", "mangrove-core/=lib/mangrove-core/", "morpho-blue/=lib/morpho-blue/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "preprocessed/=lib/mangrove-core/lib/preprocessed/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/factories/NewTokensFactory.sol": "NewTokensFactory"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"contracts/factories/ERC20DebtLiquidityTokenFactory.sol": {"keccak256": "0x72e3ada6a2f0792a353b730c1b45ae832f9ce2f58f0bda039383f8890cb2a4f7", "urls": ["bzz-raw://4949e7b66647313aaba2e11d7edde06eb87345b476c1a20f890659c1af827b2b", "dweb:/ipfs/Qmf3emVXfGp1oc8iVYxnVqpJ88vnxxdj7WqPm1vzVKb1SD"], "license": "GPL-3.0-only"}, "contracts/factories/ERC20LiquidityTokenFactory.sol": {"keccak256": "0x762974ca1ed600e0930a92bd2eb3a1a5f9ef0469ab2e6e811e4674e098238762", "urls": ["bzz-raw://5fd5f33537aeea9bac1f18c6fca2057899ec5f90cb8c756622eb436d5b13e27e", "dweb:/ipfs/QmfYznzzwN1AmdnuzNKe1R6t8UeztaZVGuzJ8vKfzjMXYN"], "license": "GPL-3.0-only"}, "contracts/factories/ERC4626DebtTokenFactory.sol": {"keccak256": "0x7deeb7a40d26bc790112f29836da83050fa3554e471e1dce4dda6bf29ab9bf67", "urls": ["bzz-raw://5a46a4c8270e0b8a731259328b6c35c84de270a14f2f69ba04bc58d18400efc6", "dweb:/ipfs/QmQ56QbX6S9GjQinsFYtTMns6HgpcTXW1wnvQT6QgiuW1Z"], "license": "GPL-3.0-only"}, "contracts/factories/ERC4626DepositTokenFactory.sol": {"keccak256": "0xf84b75119f2680f8079bb9567b0c03c0ad49b71a8c00f968d03d5fca2a954035", "urls": ["bzz-raw://c3fc7a9e300a935991746d5be835418b09e6d2b20b65e3e297d4faf28516469b", "dweb:/ipfs/QmQMr9MA5a3UcZCiP3e2haYqzBsbE8Pe6rDq6j6RJ3ub4Z"], "license": "GPL-3.0-only"}, "contracts/factories/NewTokensFactory.sol": {"keccak256": "0x86cd420e1df8a59b11a4ab53a16971a44953f0a07741ef69d95baa4bd60126ac", "urls": ["bzz-raw://d8cdd98060f059705b9ae2b64ab3e74395c0f3a24e12f5ac11ca7e509c6a7aa0", "dweb:/ipfs/QmahgKkRzuWHpQ73DHGZ4Kvd2MQG7MpfPShayJDRJQYSVr"], "license": "GPL-3.0-only"}, "contracts/interfaces/IAmmalgamPair.sol": {"keccak256": "0xa17e45b2348d8920d9970c5d50b300fc0a1e8d03350cdd0d1a624494baa70337", "urls": ["bzz-raw://8d252e89e5d49d1c15a0c0c0a495a325b9f8d608714b29279a7bacb1e4bf8795", "dweb:/ipfs/QmRkZ7a8JJQYEw6HQMJjjkuAK8b5Th1X1ET6BG1R8mx4qw"], "license": "GPL-3.0-only"}, "contracts/interfaces/callbacks/ITransferValidator.sol": {"keccak256": "0x6d9028fc4ad1914e6b2091e6ba46a9f836f9e67ea435c4a8fef41363f2ceaf56", "urls": ["bzz-raw://7ecaade4884d460168f6978edf35706f7b9e363de6002942b1d171a338dca6a4", "dweb:/ipfs/QmS5wgfDt5Pn68rpCytpzhiy57LcmivVFQ5XLGXUUP5Tt8"], "license": "GPL-3.0-only"}, "contracts/interfaces/factories/INewTokensFactory.sol": {"keccak256": "0x3b2f1ee34106d2694a9ebbe600be692bed645f4247f4a24da3d5ec46025ab3e9", "urls": ["bzz-raw://73143452a06db52deb593585fea6f2ef7c46e9ef6d649562dc39e79e4e5dca2b", "dweb:/ipfs/QmYQEy7BZWnfWKnuac8GB4QPhG5qJpaHQAfkTBoUDEuX1E"], "license": "GPL-3.0-only"}, "contracts/interfaces/factories/ITokenFactory.sol": {"keccak256": "0xac23e5c0441599add526b0c308faa7787f90bf01603b6dbc231944c166ca32d6", "urls": ["bzz-raw://ac574b98b2c1034786581137a218277ec58e06e9612f76814f34960383083626", "dweb:/ipfs/QmZgZqVnshjzuHBXJTR9g87S15CyLwJUSErGEDoJpBd4kg"], "license": "GPL-3.0-only"}, "contracts/interfaces/tokens/IAmmalgamERC20.sol": {"keccak256": "0x44a376269170b4270ec221ce3cb31a609b394e216cc4d2e27b818361b4369829", "urls": ["bzz-raw://c48bc7586631f27ede73d3d0b4c1d7a29b1653e6c501c8b7fc9877c125f8f57e", "dweb:/ipfs/QmTSLtqnsxr7h7ct524rqYssHUo4qursmCZ7g5q3J1qQPK"], "license": "GPL-3.0-only"}, "contracts/interfaces/tokens/IERC20DebtToken.sol": {"keccak256": "0xc50c6be17633c8ac677b4eaac7c05a6de1f1c938237179b59ad5e65bcfbcb03a", "urls": ["bzz-raw://6c75327e01d70a0c22662a9a8214aa64e45c517146971f8636e5aa5bf06e7696", "dweb:/ipfs/QmV2ydBQ5S9ZBtRuPgBMBdvd2Hcnn8quCGEMhhAAuic15b"], "license": "GPL-3.0-only"}, "contracts/interfaces/tokens/IPluginRegistry.sol": {"keccak256": "0x9a677620d88ac7dc42afb21d82a7b7a89bd934c1cada5450cf2b6200bf374ccf", "urls": ["bzz-raw://304091e5d54e9ad7db24ba1022e84e39dd9305d9cc72fd87423c71b40de4ab3d", "dweb:/ipfs/QmZEF5MfmUcxzLF9fcGCLvGMTTLLhcWdCMCDK2WyXj6s7X"], "license": "MIT"}, "contracts/interfaces/tokens/ITokenController.sol": {"keccak256": "0x7778001aaf582fe10005240eb6023b2b6cee3f100b6c2222bf6b9ade93732624", "urls": ["bzz-raw://91e5c4519207d6a450be1e0a8649157e86d20f8ef6a91ff6512a31cf5561a570", "dweb:/ipfs/QmUqZLW27JJZHFPf2fgLDYSWWj5gM158DdaxTTmDVukRAg"], "license": "GPL-3.0-only"}, "contracts/libraries/Convert.sol": {"keccak256": "0x944776d31291de1a9cdc6a52154c23c22b43a01c3edebe7a4140e267edbba975", "urls": ["bzz-raw://36c03749859077ba47a3acfd574f8c30f34f97def4ce81d7f4feac9a7b62794c", "dweb:/ipfs/QmdycZay5X2WrbS8qS7RycLpZbMQx7yKszWQzGU3rqidpH"], "license": "GPL-3.0-only"}, "contracts/libraries/TokenSymbol.sol": {"keccak256": "0x628df064fdbdacfe6783964d7bf38cdf1b34e1ad07caa3cea39bf7468cc19b43", "urls": ["bzz-raw://da6823ce0debaabe20f25281e81a4fc88de98d4df2942a5e276826ac381c227b", "dweb:/ipfs/QmNpEuQ25788xfcJwPk2xUB7fyP7fW5ENK2e9qgRqp1BcH"], "license": "GPL-3.0-only"}, "contracts/libraries/constants.sol": {"keccak256": "0x0dfb294985a8f48287ff13e8476718ddb5334b1d8bf6bfa59a5db1dbcf6ca7c4", "urls": ["bzz-raw://4bedcfdb2850cfb22b5daa768ab8125b4ccab97c90068d1d0ad4495bf942b362", "dweb:/ipfs/Qmf9p88yQN2JYRBR5D7q9BLmwhDJWpFk47ZuayrKqCyHat"], "license": "GPL-3.0-only"}, "contracts/tokens/ERC20Base.sol": {"keccak256": "0xdd3db9eaa855b6ff747ffaa0e74dd2a64dd5b0d704356acb75c2690c3fc6bc2b", "urls": ["bzz-raw://8f9e05ae3078bc3a29ef43861b006fb290606c1c9b2676baaed3ab98ecdb2d59", "dweb:/ipfs/QmZrvgxYtUD6jQVBvM9rT7jj5Vzb5csiThGj3ZwHSPryAL"], "license": "MIT"}, "contracts/tokens/ERC20DebtBase.sol": {"keccak256": "0xc0a59cd54fcd847b160d662aa45a5fe7d24ed90c8030fe17fd5f9def427ed19a", "urls": ["bzz-raw://365c7f18505af36b2806404b1b3f2d897de6ac18e255ecfbb4ccc491cac7e444", "dweb:/ipfs/QmUqx8EBwRb6W1YQPb9MjwAhEEHNpZTCopbGWb1vbyuUpp"], "license": "MIT"}, "contracts/tokens/ERC20DebtLiquidityToken.sol": {"keccak256": "0xf222ad5562ed41d74b0cfb5b4aad84ec9f4cb91b6d71928b30b018bab494efe8", "urls": ["bzz-raw://a8e8f3e7ded2eae04c63ce3ae7a86c051d48d6db697cb6929d7064a4ec9d7371", "dweb:/ipfs/QmU3EuwHU3xB1e6MxaRjSRJcDMK73wfZig9uGWqZPaHnTn"], "license": "MIT"}, "contracts/tokens/ERC20LiquidityToken.sol": {"keccak256": "0x2bb2429e551c031034c747749373d2e4c451580e9b203b689d6eaf03ad896358", "urls": ["bzz-raw://9ad5902756073578beee9068b74bd921e59a36b803cf34ef01570c670363689e", "dweb:/ipfs/QmTkT5K2XcB3ZbPDqd4ZAfnZMp2reCzu3Pv7JpRqhAtZHP"], "license": "MIT"}, "contracts/tokens/ERC4626DebtToken.sol": {"keccak256": "0xe69b1ed2fb7b2d7c24c6838462001988b8e51795d215cfa74b9874d17257509e", "urls": ["bzz-raw://c4f201e5f5621689046c58863ab9270cf770c68810d52269d1fc2ac93a7ccf96", "dweb:/ipfs/QmdtALf6LQdHhce3HsNdVtomQu8e5F5QcYU6S7H1PeBThZ"], "license": "MIT"}, "contracts/tokens/ERC4626DepositToken.sol": {"keccak256": "0xd914aa43dc5e9f2f02f98b05561faf6f00853b701f51dfcd7a08a31feaf220be", "urls": ["bzz-raw://8c2282d40855587b2ac70e89d4e0e147b9afe32a41245fffc96b923a9e5ce7ac", "dweb:/ipfs/QmVn3tBkZcXKnqjfnLTXFkKtu1EetDL1UF7rRjNrHdRCSM"], "license": "MIT"}, "lib/1inch/solidity-utils/contracts/libraries/AddressArray.sol": {"keccak256": "0x7895eaf7b55d9612b22ec586970488de51436c021b9f9414b3c27c3583c8856e", "urls": ["bzz-raw://e43897dfeff84e2983b017ab100d74061a6b9bed4618ec37a7cbb68bf064ac22", "dweb:/ipfs/Qmejoj4CuNr1giwGBDG7SgRtfYmi64fgy2DsGP2AAW9gH9"], "license": "MIT"}, "lib/1inch/solidity-utils/contracts/libraries/AddressSet.sol": {"keccak256": "0xbb8e2a541ac268f00f382c9fba9403b3ec5b58a48dc7236920d7c87817f93318", "urls": ["bzz-raw://614b60ff60f75c46aa41d00ec5dd78136d42e0d6035aa2331d16f26d2f5f5368", "dweb:/ipfs/QmUJWqcx2heKcmBhxpHfAnpKrwnevqtAwaQKT7Bmpke5NB"], "license": "MIT"}, "lib/1inch/token-plugins/contracts/ERC20Hooks.sol": {"keccak256": "0xd2657f278b2ed4667663344aa06c02b52b15862c69c215570de329aa1a4683a2", "urls": ["bzz-raw://4ffb90146369b7fa890489a0d3ecab5aa81c9f47b3ec7a1776fd8bc14eaa33d5", "dweb:/ipfs/QmWo24VHySo9jQBeXfE87Z3Hh576cNKcwccLrBAMsfax1c"], "license": "MIT"}, "lib/1inch/token-plugins/contracts/interfaces/IERC20Hooks.sol": {"keccak256": "0x2fb4fcbf91a7edf36e7ada3f578a8de1aee7ebdd12460648d3e09d4448351875", "urls": ["bzz-raw://d09a05543afcad3c0c2d2b9013647408cc44e1b17f362665ee20f9abed5964e8", "dweb:/ipfs/QmX3MMGsVJSfsrVoYY2jWK2BXd3i6scBhQQxytk461mizh"], "license": "MIT"}, "lib/1inch/token-plugins/contracts/interfaces/IHook.sol": {"keccak256": "0x474893cc48ee17530ad0e978ecbccaa726943615a5736260187a37b8e133ee80", "urls": ["bzz-raw://511ed52ec427cced459878f28453790817d4cbacc8601d77eb8dfa28e2e0b30d", "dweb:/ipfs/QmXvbB1bAZZanGepiUxWh3zZQUaHQZeYpR1UdaW4w5yKVS"], "license": "MIT"}, "lib/1inch/token-plugins/contracts/libs/ReentrancyGuard.sol": {"keccak256": "0xa88ccab1ee6b34a9007986ca15ea5fd5580864029130eb38333183af1bc4f66c", "urls": ["bzz-raw://b5ad18c862e59b0f24edc900519a55c7aee5537a42d039d6ea5e34df40267bb0", "dweb:/ipfs/QmVMRSjUEPxCno1sFdhWvpRqAqoqns8zwVyd7yCHYC6P8Z"], "license": "MIT"}, "lib/ExcessivelySafeCall/src/ExcessivelySafeCall.sol": {"keccak256": "0x7d9d432e8f02168bf3f790e3dabcf36402782acf7ffa476cabe86fc4d8962eb2", "urls": ["bzz-raw://1adc13e7f399f500ea5f81480ad149a50408fde7990a2c6347e6377486f389dc", "dweb:/ipfs/QmSvm5TUBJqknsqNJLLHqNS4MLSH5k3vNrbquVg6ZKSfx9"], "license": "MIT OR Apache-2.0"}, "lib/openzeppelin-contracts/contracts/access/Ownable.sol": {"keccak256": "0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb", "urls": ["bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6", "dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC1363.sol": {"keccak256": "0xc15298eb2b9ba5e18a8c9d12f93ad17a3e162a5c1d9b85f54c8adb5827b0d4da", "urls": ["bzz-raw://1f3c3d8f81d2daf1231890a6a2f897be365d6a479b53dcd52ec2527b5d3faf41", "dweb:/ipfs/QmeNdkd6u4at9pd2GAyyqxzrVGGvxfLpGmAKnFoYM5ya2e"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC165.sol": {"keccak256": "0x81b022028c39007cce9920c394b9cddd1cb9f3a1c0398f254b4a6492df92ad2b", "urls": ["bzz-raw://e0b61b8a5c69b4df993c3d6f94c174ab293aa8698d149bce7be2d88f82929beb", "dweb:/ipfs/QmbtacmB1k8ginfrHvAJpjVeqnjYGfXYrkXmMPYEb83z4t"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC20.sol": {"keccak256": "0xb5d81383d40f4006d1ce4bbad0064e7a930e17302cbe2a745e09cb403f042733", "urls": ["bzz-raw://3fc4a5681c2f00f41f49260a36ae6bbe1121dd93d470ea24d51d556eff2980be", "dweb:/ipfs/QmUBW6TwVWtGP96ka9TfuGivd27kH8CtkXD8RQAAecSFiR"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC4626.sol": {"keccak256": "0x932fe72aeda629f70ee2ca902cfd8cce9faa0a13b39222c240979f42984c4541", "urls": ["bzz-raw://4e9e0a320185800eb9a6d57ef0e4ccf3bb32a6b5dd44882f7552617c30d4a05e", "dweb:/ipfs/QmYJVpT7DDPWx3DWro8vtM6Gqre2AyufsyCYoHm9cfQ1vr"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC5267.sol": {"keccak256": "0xf29e9088951d8a2074d872a733674618fe5c164df21b8b5cf4a6295f523ba7ad", "urls": ["bzz-raw://562a1abc7ea505582827ce0c9a2f778360a1a8242742683af179930640020215", "dweb:/ipfs/QmPjx5f6KKaPfsDi1uV3ovQN9gHTAcNkMAFJZxE1Adw6VT"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol": {"keccak256": "0x85cf779582f7860b00bba1d259be00e754bfbac3c2339e324d0113d682d9e9f9", "urls": ["bzz-raw://2ddf369affd106e2a9e2b8a22a6ce3da8a6ccda14c6ba5b8c87f6b08169e6318", "dweb:/ipfs/QmNadAttd47ycHShxhk33JUJhrbzmyZQ7mHs7WEyG4Qkmp"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol": {"keccak256": "0x5aad1745bddba11752c3962464a3b12e0af079310cc22d1f43f0388ae1aaf8db", "urls": ["bzz-raw://577fad916bfdfe89aadf2685322fec7562cb0ed87722923085213cd9f85d7b79", "dweb:/ipfs/QmSM3J6PjrAUyEoNbdhq1ECZLXczKdCTzZTBUieKHsBYEL"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol": {"keccak256": "0xfe37358f223eddd65d61bb62b0b7bdb69d7101b5ec8d484292b8c1583a153b8a", "urls": ["bzz-raw://28dd43f30af3c12ae0fc08dd031b1250e906ef3c95f63f30fac6fd15aee2a662", "dweb:/ipfs/QmUkSyWsSRx36w1ti7U6qnGnQgJq16wpMhjeJrnyn9AXwG"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Permit.sol": {"keccak256": "0x6485b101d7335f0fd25abc996c5e2fc965e72e5fbd0a7ad1a465bd3f012b5fd8", "urls": ["bzz-raw://1f8d296121044e697bcd34c4cd394cebd46fc30b04ce94fccff37200872e6834", "dweb:/ipfs/QmTNdmLdoHgMzoCDZ8Txk9aYvwtuyeJYHf5mjLWgzGTZAu"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC4626.sol": {"keccak256": "0x0d99c706d010fa15de36e7e7b7a03dd0fdc9bcec52f9f812ef80ec7f3fc6fa63", "urls": ["bzz-raw://bee73c81a2964e8da0537de14082e60d64cd7b1cd9162adc04b58317e334c896", "dweb:/ipfs/QmbQ75T9PEJuiLk1kypX68rEBFtTaEzPWsy8Dv99buqVPH"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"keccak256": "0xbaffa0bcc92adf28a53cc3b68551fc3632cb8f849a0028cb8d5c06e4677715e9", "urls": ["bzz-raw://32e6f8f6b2e883c85e6a602c0882d9962ce2f92406961244e86cd974df815912", "dweb:/ipfs/Qmahvx6fPpecicq1aUE1JihCxV5ep1bfuPukzrxa8Ub5PS"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Permit.sol": {"keccak256": "0x093f32ab700c2b05373387263915a75f5455cdb0f09a7630cc621e27b7b50d04", "urls": ["bzz-raw://d163e6ef21df143969df5557305e8c643a135c7660a678d0c65dca91772114a0", "dweb:/ipfs/QmTZUgiwEro5oLRhbJ2iSWyCqu1JTDekoFHALVUn4eHqYK"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol": {"keccak256": "0x1183b415155c1a7bf56d45edad5b17caf0da70935ac420698cbe8afb6750cbb2", "urls": ["bzz-raw://21d9edaeb3e5e8f93eb0fdab41530654e8169b1990b3bbfcf5e4527c52aa03f5", "dweb:/ipfs/QmWrqpNW3x5k3pTjvrT8XU1hauHnXTjqaPL2tfzMuWYosj"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Context.sol": {"keccak256": "0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2", "urls": ["bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12", "dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Nonces.sol": {"keccak256": "0x0082767004fca261c332e9ad100868327a863a88ef724e844857128845ab350f", "urls": ["bzz-raw://132dce9686a54e025eb5ba5d2e48208f847a1ec3e60a3e527766d7bf53fb7f9e", "dweb:/ipfs/QmXn1a2nUZMpu2z6S88UoTfMVtY2YNh86iGrzJDYmMkKeZ"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Panic.sol": {"keccak256": "0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a", "urls": ["bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a", "dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/ShortStrings.sol": {"keccak256": "0x1fcf8cceb1a67e6c8512267e780933c4a3f63ef44756e6c818fda79be51c8402", "urls": ["bzz-raw://617d7d57f6f9cd449068b4d23daf485676d083aae648e038d05eb3a13291de35", "dweb:/ipfs/QmPADWPiGaSzZDFNpFEUx4ZPqhzPkYncBpHyTfAGcfsqzy"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol": {"keccak256": "0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97", "urls": ["bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b", "dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Strings.sol": {"keccak256": "0x1402d9ac66fbca0a2b282cd938f01f3cd5fb1e4c696ed28b37839401674aef52", "urls": ["bzz-raw://d3e6c46b6d1ea36bd73e0ac443a53504089167b98baa24923d702a865a38d211", "dweb:/ipfs/QmdutUpr5KktmvgtqG2v96Bo8nVKLJ3PgPedxbsRD42CuQ"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol": {"keccak256": "0x6c29257484c0595ca5af8844fafe99cc5eace7447c9f5bced71d6b3a19a6a2a5", "urls": ["bzz-raw://cce7ac0bdb05f73c0918e362dea2e52426e00ddf0a1018f14accdcf78c6eb6e4", "dweb:/ipfs/QmbkNq5dDxww27FzFFiKgW3S7C5VoZpjdZGpSCtsb9hP32"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/cryptography/EIP712.sol": {"keccak256": "0xda8013da608bda3c9eaa9e59053d38d7888e64bb40aa557e5929cd702f8de87e", "urls": ["bzz-raw://3ea13234c6b00ae79dc1a98e7e7f2faf38d37e76a687ccd0c95ad84b03ea570f", "dweb:/ipfs/QmWtdefDm5jiEzAjmfPMZ5B1NKVxFoMiD5ZoD68hcNTHun"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/cryptography/MessageHashUtils.sol": {"keccak256": "0x26670fef37d4adf55570ba78815eec5f31cb017e708f61886add4fc4da665631", "urls": ["bzz-raw://b16d45febff462bafd8a5669f904796a835baf607df58a8461916d3bf4f08c59", "dweb:/ipfs/QmU2eJFpjmT4vxeJWJyLeQb8Xht1kdB8Y6MKLDPFA9WPux"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol": {"keccak256": "0x4ee0e04cc52827588793a141d5efb9830f179a17e80867cc332b3a30ceb30fd9", "urls": ["bzz-raw://17d8f47fce493b34099ed9005c5aee3012488f063cfe1c34ed8f9e6fc3d576e5", "dweb:/ipfs/QmZco2GbZZhEMvG3BovyoGMAFKvfi2LhfNGQLn283LPrXf"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/math/Math.sol": {"keccak256": "0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6", "urls": ["bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3", "dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol": {"keccak256": "0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54", "urls": ["bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8", "dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/math/SignedMath.sol": {"keccak256": "0xb1970fac7b64e6c09611e6691791e848d5e3fe410fa5899e7df2e0afd77a99e3", "urls": ["bzz-raw://db5fbb3dddd8b7047465b62575d96231ba8a2774d37fb4737fbf23340fabbb03", "dweb:/ipfs/QmVUSvooZKEdEdap619tcJjTLcAuH6QBdZqAzWwnAXZAWJ"], "license": "MIT"}}, "version": 1}, "id": 7}