{"abi": [{"type": "function", "name": "IS_TEST", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "excludeArtifacts", "inputs": [], "outputs": [{"name": "excludedArtifacts_", "type": "string[]", "internalType": "string[]"}], "stateMutability": "view"}, {"type": "function", "name": "excludeContracts", "inputs": [], "outputs": [{"name": "excludedContracts_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "excludeSelectors", "inputs": [], "outputs": [{"name": "excludedSelectors_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzSelector[]", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "selectors", "type": "bytes4[]", "internalType": "bytes4[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "excludeSenders", "inputs": [], "outputs": [{"name": "excludedSenders_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "failed", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "targetArtifactSelectors", "inputs": [], "outputs": [{"name": "targetedArtifactSelectors_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzArtifactSelector[]", "components": [{"name": "artifact", "type": "string", "internalType": "string"}, {"name": "selectors", "type": "bytes4[]", "internalType": "bytes4[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "targetArtifacts", "inputs": [], "outputs": [{"name": "targetedArtifacts_", "type": "string[]", "internalType": "string[]"}], "stateMutability": "view"}, {"type": "function", "name": "targetContracts", "inputs": [], "outputs": [{"name": "targetedContracts_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "targetInterfaces", "inputs": [], "outputs": [{"name": "targetedInterfaces_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzInterface[]", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "artifacts", "type": "string[]", "internalType": "string[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "targetSelectors", "inputs": [], "outputs": [{"name": "targetedSelectors_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzSelector[]", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "selectors", "type": "bytes4[]", "internalType": "bytes4[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "targetSenders", "inputs": [], "outputs": [{"name": "targetedSenders_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "testGeometricTWAPFuzz_ObserveIndexWithMissingBlocks", "inputs": [{"name": "firstBlocks", "type": "uint24", "internalType": "uint24"}, {"name": "missingBlocks", "type": "uint24", "internalType": "uint24"}, {"name": "secondBlocks", "type": "uint24", "internalType": "uint24"}, {"name": "longTermIntervalConfigFactor", "type": "uint24", "internalType": "uint24"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "log", "inputs": [{"name": "", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "log_address", "inputs": [{"name": "", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "log_array", "inputs": [{"name": "val", "type": "uint256[]", "indexed": false, "internalType": "uint256[]"}], "anonymous": false}, {"type": "event", "name": "log_array", "inputs": [{"name": "val", "type": "int256[]", "indexed": false, "internalType": "int256[]"}], "anonymous": false}, {"type": "event", "name": "log_array", "inputs": [{"name": "val", "type": "address[]", "indexed": false, "internalType": "address[]"}], "anonymous": false}, {"type": "event", "name": "log_bytes", "inputs": [{"name": "", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "event", "name": "log_bytes32", "inputs": [{"name": "", "type": "bytes32", "indexed": false, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "log_int", "inputs": [{"name": "", "type": "int256", "indexed": false, "internalType": "int256"}], "anonymous": false}, {"type": "event", "name": "log_named_address", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "log_named_array", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "uint256[]", "indexed": false, "internalType": "uint256[]"}], "anonymous": false}, {"type": "event", "name": "log_named_array", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "int256[]", "indexed": false, "internalType": "int256[]"}], "anonymous": false}, {"type": "event", "name": "log_named_array", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "address[]", "indexed": false, "internalType": "address[]"}], "anonymous": false}, {"type": "event", "name": "log_named_bytes", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "event", "name": "log_named_bytes32", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "bytes32", "indexed": false, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "log_named_decimal_int", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "int256", "indexed": false, "internalType": "int256"}, {"name": "decimals", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "log_named_decimal_uint", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "decimals", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "log_named_int", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "int256", "indexed": false, "internalType": "int256"}], "anonymous": false}, {"type": "event", "name": "log_named_string", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "log_named_uint", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "log_string", "inputs": [{"name": "", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "log_uint", "inputs": [{"name": "", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "logs", "inputs": [{"name": "", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}], "bytecode": {"object": "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", "sourceMap": "875:3606:121:-:0;;;3126:44:97;;;3166:4;-1:-1:-1;;3126:44:97;;;;;;;;1016:26:107;;;;;;;;;;;875:3606:121;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "875:3606:121:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2907:134:100;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;3823:151;;;:::i;:::-;;;;;;;:::i;3684:133::-;;;:::i;3385:141::-;;;:::i;3193:186::-;;;:::i;:::-;;;;;;;:::i;3047:140::-;;;:::i;:::-;;;;;;;:::i;3532:146::-;;;:::i;:::-;;;;;;;:::i;2754:147::-;;;:::i;1852:1986:121:-;;;;;;:::i;:::-;;:::i;:::-;;2459:141:100;;;:::i;1243:204:96:-;;;:::i;:::-;;;6746:14:193;;6739:22;6721:41;;6709:2;6694:18;1243:204:96;6581:187:193;2606:142:100;;;:::i;1016:26:107:-;;;;;;;;;2907:134:100;2954:33;3018:16;2999:35;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;2999:35:100;;;;;;;;;;;;;;;;;;;;;;;2907:134;:::o;3823:151::-;3872:42;3948:19;3926:41;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3926:41:100;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3823:151;:::o;3684:133::-;3730:33;3794:16;3775:35;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3775:35:100;;;;;;;;;;;;;;;;;;;;;;3684:133;:::o;3385:141::-;3433:35;3501:18;3480:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3480:39:100;;;;;;;;;;;;;;;;;;;;;;3385:141;:::o;3193:186::-;3249:56;3346:26;3317:55;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3317:55:100;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3047:140;3095:34;3162:18;3141:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3532:146;3580:40;3653:18;3632:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3632:39:100;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3632:39:100;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2754:147;2803:40;2876:18;2855:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;2855:39:100;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;2855:39:100;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1852:1986:121;2106:66;;;;2142:1;513:2:181;2106:5:121;:66::i;:::-;2068:105;-1:-1:-1;2208:4:121;2310:65;;;;2329:30;513:2:181;2358:1:121;2329:30;:::i;:::-;2310:65;;2361:13;2310:5;:65::i;:::-;2289:87;-1:-1:-1;2408:66:121;;;;2428:30;513:2:181;2457:1:121;2428:30;:::i;2408:66::-;2386:89;-1:-1:-1;2486:20:121;2509:26;2386:89;2509:11;:26;:::i;:::-;2486:49;;;-1:-1:-1;2545:30:121;637:2:21;2590:56:121;2618:28;2941:1:30;2590:56:121;:::i;:::-;:94;;;;;;:::i;:::-;2545:139;-1:-1:-1;2694:38:121;2735:61;696:1:21;2545:139:121;2735:61;:::i;:::-;2694:102;;2826:30;2811:12;:45;2807:217;;;2967:45;3000:12;2967:30;:45;:::i;:::-;2944:69;;;;:::i;:::-;;;2807:217;3394:189;;;;3448:1;3528:40;3567:1;696::21;3528:40:121;:::i;:::-;3502:67;;:22;:67;:::i;:::-;3467:102;;3482:15;3467:102;:::i;:::-;3394:5;:189::i;:::-;3358:235;-1:-1:-1;3604:227:121;;;;;;3358:235;;3604:227;;2941:1:30;3754:67:121;3387:3:30;3754:28:121;:67;:::i;:::-;3604:19;:227::i;:::-;2058:1780;;;;1852:1986;;;;:::o;2459:141:100:-;2508:34;2575:18;2554:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1243:204:96;1302:7;;1282:4;;1302:7;;1298:143;;;-1:-1:-1;1332:7:96;;;;;1243:204::o;1298:143::-;1377:39;;-1:-1:-1;;;1377:39:96;;:7;:39;;;8416:51:193;;;-1:-1:-1;;;8483:18:193;;;8476:34;1428:1:96;;1377:7;;8389:18:193;;1377:39:96;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:53;;1370:60;;1243:204;:::o;2606:142:100:-;2655:35;2723:18;2702:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;2702:39:100;;;;;;;;;;;;;;;;;;;;;;2606:142;:::o;2815:199:106:-;2898:14;2933:19;2940:1;2943:3;2948;2933:6;:19::i;:::-;2924:28;;2962:45;;;;;;;;;;;;;;-1:-1:-1;;;2962:45:106;;;3000:6;2962:21;:45::i;:::-;2815:199;;;;;:::o;3844:635:121:-;4136:21;4159:28;464:2:181;4107:101:121;;;;;:::i;:::-;8934:8:193;8922:21;;;8904:40;;8980:21;;;;8975:2;8960:18;;8953:49;9049:1;9038:21;9033:2;9018:18;;9011:49;8892:2;8877:18;4107:101:121;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;4097:7:121;:111;;-1:-1:-1;;;;;;4097:111:121;;-1:-1:-1;;;;;4097:111:121;;;;;;;;;;;;;4219:67;;-1:-1:-1;;;4219:67:121;;;;;9241:25:193;;;464:2:181;9282:18:193;;;9275:49;4219:7:121;;;;;;:28;;9214:18:193;;4219:67:121;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;4296:7:121;;:48;;-1:-1:-1;;;4296:48:121;;9510:8:193;9498:21;;4296:48:121;;;9480:40:193;4296:7:121;;;;-1:-1:-1;;;;;4296:7:121;;-1:-1:-1;4296:17:121;;-1:-1:-1;9453:18:193;;4296:48:121;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;4354:7:121;;:68;;-1:-1:-1;;;4354:68:121;;;;;9241:25:193;;;464:2:181;9282:18:193;;;9275:49;4354:7:121;;;;-1:-1:-1;;;;;4354:7:121;;-1:-1:-1;4354:28:121;;-1:-1:-1;9214:18:193;;4354:68:121;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;4433:7:121;;:39;;-1:-1:-1;;;4433:39:121;;464:2:181;4433:39:121;;;9673:40:193;4433:7:121;;;;-1:-1:-1;;;;;4433:7:121;;-1:-1:-1;4433:19:121;;-1:-1:-1;9646:18:193;;4433:39:121;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3844:635;;;;;:::o;1546:1263:106:-;1630:14;1671:3;1664;:10;;1656:85;;;;-1:-1:-1;;;1656:85:106;;9926:2:193;1656:85:106;;;9908:21:193;9965:2;9945:18;;;9938:30;10004:34;9984:18;;;9977:62;10075:32;10055:18;;;10048:60;10125:19;;1656:85:106;;;;;;;;1975:3;1970:1;:8;;:20;;;;;1987:3;1982:1;:8;;1970:20;1966:34;;;-1:-1:-1;1999:1:106;1992:8;;1966:34;2011:12;2026:9;2032:3;2026;:9;:::i;:::-;:13;;2038:1;2026:13;:::i;:::-;2011:28;;2234:1;2229;:6;;:18;;;;;2246:1;2239:4;:8;2229:18;2225:38;;;2256:7;2262:1;2256:3;:7;:::i;:::-;2249:14;;;;;2225:38;2282:15;2296:1;-1:-1:-1;;2282:15:106;:::i;:::-;2277:1;:20;;:46;;;;-1:-1:-1;2308:15:106;2322:1;-1:-1:-1;;2308:15:106;:::i;:::-;2301:4;:22;2277:46;2273:82;;;2339:15;2353:1;-1:-1:-1;;2339:15:106;:::i;:::-;2332:23;;:3;:23;:::i;2273:82::-;2459:3;2455:1;:7;2451:352;;;2478:12;2493:7;2497:3;2493:1;:7;:::i;:::-;2478:22;-1:-1:-1;2514:11:106;2528;2535:4;2478:22;2528:11;:::i;:::-;2514:25;;2557:3;2564:1;2557:8;2553:24;;2574:3;2567:10;;;;;;;2553:24;2612:1;2600:9;2606:3;2600;:9;:::i;:::-;:13;;;;:::i;:::-;2591:22;;2464:160;;2451:352;;;2638:3;2634:1;:7;2630:173;;;2657:12;2672:7;2678:1;2672:3;:7;:::i;:::-;2657:22;-1:-1:-1;2693:11:106;2707;2714:4;2657:22;2707:11;:::i;:::-;2693:25;;2736:3;2743:1;2736:8;2732:24;;2753:3;2746:10;;;;;;;2732:24;2779:9;2785:3;2779;:9;:::i;:::-;:13;;2791:1;2779:13;:::i;:::-;2770:22;;2643:160;;2630:173;1646:1163;1546:1263;;;;;:::o;9686:162::-;9770:71;9833:2;9837;9786:54;;;;;;;;;:::i;:::-;;;;-1:-1:-1;;9786:54:106;;;;;;;;;;;;;;-1:-1:-1;;;;;9786:54:106;-1:-1:-1;;;9786:54:106;;;9770:15;:71::i;:::-;9686:162;;:::o;9016:133::-;9087:55;9134:7;9113:19;9087:55::i;:::-;9016:133;:::o;9155:381::-;9253:14;;679:42;9427:2;9414:16;;9229:21;;9253:14;9414:16;679:42;9463:5;9452:68;9443:77;;9380:150;;9155:381;:::o;-1:-1:-1:-;;;;;;;;:::o;14:637:193:-;204:2;216:21;;;286:13;;189:18;;;308:22;;;156:4;;387:15;;;361:2;346:18;;;156:4;430:195;444:6;441:1;438:13;430:195;;;509:13;;-1:-1:-1;;;;;505:39:193;493:52;;574:2;600:15;;;;565:12;;;;541:1;459:9;430:195;;;-1:-1:-1;642:3:193;;14:637;-1:-1:-1;;;;;14:637:193:o;656:289::-;698:3;736:5;730:12;763:6;758:3;751:19;819:6;812:4;805:5;801:16;794:4;789:3;785:14;779:47;871:1;864:4;855:6;850:3;846:16;842:27;835:38;934:4;927:2;923:7;918:2;910:6;906:15;902:29;897:3;893:39;889:50;882:57;;;656:289;;;;:::o;950:1628::-;1156:4;1204:2;1193:9;1189:18;1234:2;1223:9;1216:21;1257:6;1292;1286:13;1323:6;1315;1308:22;1361:2;1350:9;1346:18;1339:25;;1423:2;1413:6;1410:1;1406:14;1395:9;1391:30;1387:39;1373:53;;1461:2;1453:6;1449:15;1482:1;1492:1057;1506:6;1503:1;1500:13;1492:1057;;;-1:-1:-1;;1571:22:193;;;1567:36;1555:49;;1627:13;;1714:9;;-1:-1:-1;;;;;1710:35:193;1695:51;;1793:2;1785:11;;;1779:18;1679:2;1817:15;;;1810:27;;;1898:19;;1667:15;;;1930:24;;;2085:21;;;1988:2;2038:1;2034:16;;;2022:29;;2018:38;;;1976:15;;;;-1:-1:-1;2144:296:193;2160:8;2155:3;2152:17;2144:296;;;2266:2;2262:7;2253:6;2245;2241:19;2237:33;2230:5;2223:48;2298:42;2333:6;2322:8;2316:15;2298:42;:::i;:::-;2383:2;2369:17;;;;2288:52;;-1:-1:-1;2412:14:193;;;;;2188:1;2179:11;2144:296;;;-1:-1:-1;2463:6:193;;-1:-1:-1;;;2504:2:193;2527:12;;;;2492:15;;;;;-1:-1:-1;1528:1:193;1521:9;1492:1057;;;-1:-1:-1;2566:6:193;;950:1628;-1:-1:-1;;;;;;950:1628:193:o;2583:446::-;2635:3;2673:5;2667:12;2700:6;2695:3;2688:19;2732:4;2727:3;2723:14;2716:21;;2771:4;2764:5;2760:16;2794:1;2804:200;2818:6;2815:1;2812:13;2804:200;;;2883:13;;-1:-1:-1;;;;;;2879:40:193;2867:53;;2949:4;2940:14;;;;2977:17;;;;2840:1;2833:9;2804:200;;;-1:-1:-1;3020:3:193;;2583:446;-1:-1:-1;;;;2583:446:193:o;3034:1145::-;3254:4;3302:2;3291:9;3287:18;3332:2;3321:9;3314:21;3355:6;3390;3384:13;3421:6;3413;3406:22;3459:2;3448:9;3444:18;3437:25;;3521:2;3511:6;3508:1;3504:14;3493:9;3489:30;3485:39;3471:53;;3559:2;3551:6;3547:15;3580:1;3590:560;3604:6;3601:1;3598:13;3590:560;;;3697:2;3693:7;3681:9;3673:6;3669:22;3665:36;3660:3;3653:49;3731:6;3725:13;3777:2;3771:9;3808:2;3800:6;3793:18;3838:48;3882:2;3874:6;3870:15;3856:12;3838:48;:::i;:::-;3824:62;;3935:2;3931;3927:11;3921:18;3899:40;;3988:6;3980;3976:19;3971:2;3963:6;3959:15;3952:44;4019:51;4063:6;4047:14;4019:51;:::i;:::-;4009:61;-1:-1:-1;;;4105:2:193;4128:12;;;;4093:15;;;;;3626:1;3619:9;3590:560;;4184:782;4346:4;4394:2;4383:9;4379:18;4424:2;4413:9;4406:21;4447:6;4482;4476:13;4513:6;4505;4498:22;4551:2;4540:9;4536:18;4529:25;;4613:2;4603:6;4600:1;4596:14;4585:9;4581:30;4577:39;4563:53;;4651:2;4643:6;4639:15;4672:1;4682:255;4696:6;4693:1;4690:13;4682:255;;;4789:2;4785:7;4773:9;4765:6;4761:22;4757:36;4752:3;4745:49;4817:40;4850:6;4841;4835:13;4817:40;:::i;:::-;4807:50;-1:-1:-1;4892:2:193;4915:12;;;;4880:15;;;;;4718:1;4711:9;4682:255;;4971:1033;5175:4;5223:2;5212:9;5208:18;5253:2;5242:9;5235:21;5276:6;5311;5305:13;5342:6;5334;5327:22;5380:2;5369:9;5365:18;5358:25;;5442:2;5432:6;5429:1;5425:14;5414:9;5410:30;5406:39;5392:53;;5480:2;5472:6;5468:15;5501:1;5511:464;5525:6;5522:1;5519:13;5511:464;;;5590:22;;;-1:-1:-1;;5586:36:193;5574:49;;5646:13;;5691:9;;-1:-1:-1;;;;;5687:35:193;5672:51;;5770:2;5762:11;;;5756:18;5811:2;5794:15;;;5787:27;;;5756:18;5837:58;;5879:15;;5756:18;5837:58;:::i;:::-;5827:68;-1:-1:-1;;5930:2:193;5953:12;;;;5918:15;;;;;5547:1;5540:9;5511:464;;6009:161;6076:20;;6136:8;6125:20;;6115:31;;6105:59;;6160:1;6157;6150:12;6105:59;6009:161;;;:::o;6175:401::-;6257:6;6265;6273;6281;6334:3;6322:9;6313:7;6309:23;6305:33;6302:53;;;6351:1;6348;6341:12;6302:53;6374:28;6392:9;6374:28;:::i;:::-;6364:38;;6421:37;6454:2;6443:9;6439:18;6421:37;:::i;:::-;6411:47;;6477:37;6510:2;6499:9;6495:18;6477:37;:::i;:::-;6467:47;;6533:37;6566:2;6555:9;6551:18;6533:37;:::i;:::-;6523:47;;6175:401;;;;;;;:::o;6773:380::-;6852:1;6848:12;;;;6895;;;6916:61;;6970:4;6962:6;6958:17;6948:27;;6916:61;7023:2;7015:6;7012:14;6992:18;6989:38;6986:161;;7069:10;7064:3;7060:20;7057:1;7050:31;7104:4;7101:1;7094:15;7132:4;7129:1;7122:15;6986:161;;6773:380;;;:::o;7158:127::-;7219:10;7214:3;7210:20;7207:1;7200:31;7250:4;7247:1;7240:15;7274:4;7271:1;7264:15;7290:232;7397:6;7374:14;;;7390;;;7370:35;7425:24;;;;7468;;;7458:58;;7496:18;;:::i;:::-;7458:58;7290:232;;;;:::o;7527:161::-;7620:8;7595:16;;;7613;;;7591:39;;7642:17;;7639:43;;;7662:18;;:::i;:::-;7527:161;;;;:::o;7693:238::-;7802:8;7777:16;;;7795;;;7773:39;7832:26;;;;7877:24;;;7867:58;;7905:18;;:::i;7936:168::-;8009:9;;;8040;;8057:15;;;8051:22;;8037:37;8027:71;;8078:18;;:::i;8109:128::-;8176:9;;;8197:11;;;8194:37;;;8211:18;;:::i;8521:184::-;8591:6;8644:2;8632:9;8623:7;8619:23;8615:32;8612:52;;;8660:1;8657;8650:12;8612:52;-1:-1:-1;8683:16:193;;8521:184;-1:-1:-1;8521:184:193:o;10155:125::-;10220:9;;;10241:10;;;10238:36;;;10254:18;;:::i;10285:209::-;10317:1;10343;10333:132;;10387:10;10382:3;10378:20;10375:1;10368:31;10422:4;10419:1;10412:15;10450:4;10447:1;10440:15;10333:132;-1:-1:-1;10479:9:193;;10285:209::o;10499:291::-;10676:2;10665:9;10658:21;10639:4;10696:45;10737:2;10726:9;10722:18;10714:6;10696:45;:::i;:::-;10688:53;;10777:6;10772:2;10761:9;10757:18;10750:34;10499:291;;;;;:::o", "linkReferences": {}}, "methodIdentifiers": {"IS_TEST()": "fa7626d4", "excludeArtifacts()": "b5508aa9", "excludeContracts()": "e20c9f71", "excludeSelectors()": "b0464fdc", "excludeSenders()": "1ed7831c", "failed()": "ba414fa6", "targetArtifactSelectors()": "66d9a9a0", "targetArtifacts()": "85226c81", "targetContracts()": "3f7286f4", "targetInterfaces()": "2ade3880", "targetSelectors()": "916a17c6", "targetSenders()": "3e5e3c23", "testGeometricTWAPFuzz_ObserveIndexWithMissingBlocks(uint24,uint24,uint24,uint24)": "b1cc31c6"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"name\":\"log\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"log_address\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256[]\",\"name\":\"val\",\"type\":\"uint256[]\"}],\"name\":\"log_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"int256[]\",\"name\":\"val\",\"type\":\"int256[]\"}],\"name\":\"log_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address[]\",\"name\":\"val\",\"type\":\"address[]\"}],\"name\":\"log_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"name\":\"log_bytes\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"name\":\"log_bytes32\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"\",\"type\":\"int256\"}],\"name\":\"log_int\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"val\",\"type\":\"address\"}],\"name\":\"log_named_address\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"uint256[]\",\"name\":\"val\",\"type\":\"uint256[]\"}],\"name\":\"log_named_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"int256[]\",\"name\":\"val\",\"type\":\"int256[]\"}],\"name\":\"log_named_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"address[]\",\"name\":\"val\",\"type\":\"address[]\"}],\"name\":\"log_named_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"val\",\"type\":\"bytes\"}],\"name\":\"log_named_bytes\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"val\",\"type\":\"bytes32\"}],\"name\":\"log_named_bytes32\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"val\",\"type\":\"int256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"}],\"name\":\"log_named_decimal_int\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"val\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"}],\"name\":\"log_named_decimal_uint\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"val\",\"type\":\"int256\"}],\"name\":\"log_named_int\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"val\",\"type\":\"string\"}],\"name\":\"log_named_string\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"val\",\"type\":\"uint256\"}],\"name\":\"log_named_uint\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"name\":\"log_string\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"log_uint\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"name\":\"logs\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"IS_TEST\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeArtifacts\",\"outputs\":[{\"internalType\":\"string[]\",\"name\":\"excludedArtifacts_\",\"type\":\"string[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeContracts\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"excludedContracts_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeSelectors\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"bytes4[]\",\"name\":\"selectors\",\"type\":\"bytes4[]\"}],\"internalType\":\"struct StdInvariant.FuzzSelector[]\",\"name\":\"excludedSelectors_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeSenders\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"excludedSenders_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"failed\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetArtifactSelectors\",\"outputs\":[{\"components\":[{\"internalType\":\"string\",\"name\":\"artifact\",\"type\":\"string\"},{\"internalType\":\"bytes4[]\",\"name\":\"selectors\",\"type\":\"bytes4[]\"}],\"internalType\":\"struct StdInvariant.FuzzArtifactSelector[]\",\"name\":\"targetedArtifactSelectors_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetArtifacts\",\"outputs\":[{\"internalType\":\"string[]\",\"name\":\"targetedArtifacts_\",\"type\":\"string[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetContracts\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"targetedContracts_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetInterfaces\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"string[]\",\"name\":\"artifacts\",\"type\":\"string[]\"}],\"internalType\":\"struct StdInvariant.FuzzInterface[]\",\"name\":\"targetedInterfaces_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetSelectors\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"bytes4[]\",\"name\":\"selectors\",\"type\":\"bytes4[]\"}],\"internalType\":\"struct StdInvariant.FuzzSelector[]\",\"name\":\"targetedSelectors_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetSenders\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"targetedSenders_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint24\",\"name\":\"firstBlocks\",\"type\":\"uint24\"},{\"internalType\":\"uint24\",\"name\":\"missingBlocks\",\"type\":\"uint24\"},{\"internalType\":\"uint24\",\"name\":\"secondBlocks\",\"type\":\"uint24\"},{\"internalType\":\"uint24\",\"name\":\"longTermIntervalConfigFactor\",\"type\":\"uint24\"}],\"name\":\"testGeometricTWAPFuzz_ObserveIndexWithMissingBlocks\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"details\":\"This contract contains two fuzz tests for GeometricTWAPSpec.          1. The first test is to test the observation index when there are missing blocks.          2. The second test is to test the ticks when there is a new different tick at the last block.\",\"kind\":\"dev\",\"methods\":{\"testGeometricTWAPFuzz_ObserveIndexWithMissingBlocks(uint24,uint24,uint24,uint24)\":{\"details\":\"The purpose of this fuzz test is to verify the long and mid-term tick range with a wide random range for any patterns of          blocks, blocks to miss, the longTermIntervalConfig and with any number of past blocks.\",\"params\":{\"firstBlocks\":\"The first portion of active blocks being observed.\",\"longTermIntervalConfigFactor\":\"The initial configuration value for long-term intervals.\",\"missingBlocks\":\"The number of blocks to be intentionally missed for testing.\",\"secondBlocks\":\"The second portion of active blocks being observed after the missing blocks.\"}}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"testGeometricTWAPFuzz_ObserveIndexWithMissingBlocks(uint24,uint24,uint24,uint24)\":{\"notice\":\"Foundry In-line fuzz test configuration. Place this directly above the function for the local config to take effect.          forge-config: default.fuzz.runs = 50\"}},\"notice\":\"Fuzz tests for GeometricTWAPSpec.\",\"version\":1}},\"settings\":{\"compilationTarget\":{\"test/GeometricTWAPSpecFuzzTests.sol\":\"GeometricTWAPSpecFuzzTests\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":1inch/=lib/1inch/\",\":@1inch/=lib/1inch/\",\":@mangrovedao/mangrove-core/=lib/mangrove-core/\",\":@mgv/forge-std/=lib/mangrove-core/lib/forge-std/src/\",\":@mgv/lib/=lib/mangrove-core/lib/\",\":@mgv/script/=lib/mangrove-core/script/\",\":@mgv/src/=lib/mangrove-core/src/\",\":@mgv/test/=lib/mangrove-core/test/\",\":@morpho-org/morpho-blue/=lib/morpho-blue/\",\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ExcessivelySafeCall/=lib/ExcessivelySafeCall/src/\",\":core/=lib/mangrove-core/lib/core/\",\":ds-test/=lib/ExcessivelySafeCall/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/openzeppelin-contracts/lib/forge-std/src/\",\":halmos-cheatcodes/=lib/morpho-blue/lib/halmos-cheatcodes/src/\",\":mangrove-core/=lib/mangrove-core/\",\":morpho-blue/=lib/morpho-blue/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":preprocessed/=lib/mangrove-core/lib/preprocessed/\"]},\"sources\":{\"contracts/AmmalgamPair.sol\":{\"keccak256\":\"0xe8f98844a55a216605e6c6dd6837977fafda458a6b5d0cfa1f3a18d25e9432e4\",\"urls\":[\"bzz-raw://65dda1a1de8dd64e31c666b13de3d0583b4b0da923c67065cadcddefe47562a2\",\"dweb:/ipfs/Qmaev9WFa4yyL8fXVoWkXwNsTTY8wY7jTBGDoKJbdwSCzS\"]},\"contracts/SaturationAndGeometricTWAPState.sol\":{\"keccak256\":\"0x5e293a35668bb216a99379ea2176894314cc0f1ac68644fcf4c07017da1a4419\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://00349bb86f1b657010919b4bc3f616ad56ef4883b99ab0eead36815dae93dc76\",\"dweb:/ipfs/QmbEd9GD2JxuDntX35YcfbSCcpRstDU9GDPUkBKGzsxvqE\"]},\"contracts/factories/AmmalgamFactory.sol\":{\"keccak256\":\"0xe0d9baf63d9538a7ecb8bd24ea61a8cdf6fc9c1e9eb028f343548adeb8b93e4e\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://bfca24db47fbbaeef8bc36996cbfed78eb48771ac71d2800f081fb58a8e8c92b\",\"dweb:/ipfs/QmUfYEwfmrjSmchqXi58SnpSina4qKQvD2Jbk5RqYsaoCa\"]},\"contracts/interfaces/IAmmalgamPair.sol\":{\"keccak256\":\"0xa17e45b2348d8920d9970c5d50b300fc0a1e8d03350cdd0d1a624494baa70337\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://8d252e89e5d49d1c15a0c0c0a495a325b9f8d608714b29279a7bacb1e4bf8795\",\"dweb:/ipfs/QmRkZ7a8JJQYEw6HQMJjjkuAK8b5Th1X1ET6BG1R8mx4qw\"]},\"contracts/interfaces/ISaturationAndGeometricTWAPState.sol\":{\"keccak256\":\"0xc9add2ad41f8edd9d360ced8d2cd7bd18dd500304794434fb2e309fa0f5af83c\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://8ecc810c544ac734ef26a2f6bebea3f3bd12d773965d297991e0e0e72892fa20\",\"dweb:/ipfs/QmarXc1Ut4FZzPRRZs2M2udbJjuZUJQHQ8fvmSr3bpHErR\"]},\"contracts/interfaces/callbacks/IAmmalgamCallee.sol\":{\"keccak256\":\"0x904b858859d460a61c9e644ca87009d8e32ba20482ef218801c89c7fb1ece339\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://1a7cedebbacc453e3e4e339fcc76fd3268247c13982de82b4930d59a44256c1d\",\"dweb:/ipfs/QmdjdvYabbwAYcV72xjiXyq278xQivFtiqD3eQ5P9Gk4f1\"]},\"contracts/interfaces/callbacks/ITransferValidator.sol\":{\"keccak256\":\"0x6d9028fc4ad1914e6b2091e6ba46a9f836f9e67ea435c4a8fef41363f2ceaf56\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://7ecaade4884d460168f6978edf35706f7b9e363de6002942b1d171a338dca6a4\",\"dweb:/ipfs/QmS5wgfDt5Pn68rpCytpzhiy57LcmivVFQ5XLGXUUP5Tt8\"]},\"contracts/interfaces/factories/IAmmalgamFactory.sol\":{\"keccak256\":\"0x1c80089901e8d7d7451775b5eaa92092eb2b65319cb92fa7884281bae49f52b8\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://bf1201399bb9d5046e0e788ff88394b2468771096a2a0d3500af542923e84628\",\"dweb:/ipfs/QmeUaPyQpbPbP5fyPUT2FfzeDgHcdyQAn1DaNg9uCuGoj9\"]},\"contracts/interfaces/factories/IFactoryCallback.sol\":{\"keccak256\":\"0x33250cf8351adb4846a3d133a9bc06568288e4c680bcf5b1085e3bca40a35e52\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://5663a39af4ed3040a58beaa5641425b9adca83c2683dd220e0c11e644fefe52b\",\"dweb:/ipfs/QmYB8Vf37WDzQfSpMDjv8hVicuaF1wMBzf7xjHRjGCy3wT\"]},\"contracts/interfaces/factories/INewTokensFactory.sol\":{\"keccak256\":\"0x3b2f1ee34106d2694a9ebbe600be692bed645f4247f4a24da3d5ec46025ab3e9\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://73143452a06db52deb593585fea6f2ef7c46e9ef6d649562dc39e79e4e5dca2b\",\"dweb:/ipfs/QmYQEy7BZWnfWKnuac8GB4QPhG5qJpaHQAfkTBoUDEuX1E\"]},\"contracts/interfaces/tokens/IAmmalgamERC20.sol\":{\"keccak256\":\"0x44a376269170b4270ec221ce3cb31a609b394e216cc4d2e27b818361b4369829\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://c48bc7586631f27ede73d3d0b4c1d7a29b1653e6c501c8b7fc9877c125f8f57e\",\"dweb:/ipfs/QmTSLtqnsxr7h7ct524rqYssHUo4qursmCZ7g5q3J1qQPK\"]},\"contracts/interfaces/tokens/ITokenController.sol\":{\"keccak256\":\"0x7778001aaf582fe10005240eb6023b2b6cee3f100b6c2222bf6b9ade93732624\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://91e5c4519207d6a450be1e0a8649157e86d20f8ef6a91ff6512a31cf5561a570\",\"dweb:/ipfs/QmUqZLW27JJZHFPf2fgLDYSWWj5gM158DdaxTTmDVukRAg\"]},\"contracts/libraries/Convert.sol\":{\"keccak256\":\"0x944776d31291de1a9cdc6a52154c23c22b43a01c3edebe7a4140e267edbba975\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://36c03749859077ba47a3acfd574f8c30f34f97def4ce81d7f4feac9a7b62794c\",\"dweb:/ipfs/QmdycZay5X2WrbS8qS7RycLpZbMQx7yKszWQzGU3rqidpH\"]},\"contracts/libraries/GeometricTWAP.sol\":{\"keccak256\":\"0x3860409daa0fdb5d96f0bfb8b49cbca058b9fe32c8e32457f85d4ee2c5cdcb1e\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://81fe70a80f4005a6529c7e93f1a92547ce0bf74c357c280a91e8778b378b18aa\",\"dweb:/ipfs/QmdRQ1DqsCu11zfbLAbrrzJ9Ups3oKgTGimYo3Zm3ALiCz\"]},\"contracts/libraries/Interest.sol\":{\"keccak256\":\"0xbc8bfa20d7295dd70e3c716fd3dbeb5b45d313e3c609d063d186042cbf000646\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://b015e8d4976d3b6d7eaca07dfcc487aeed3a7d8b4c41c8369a7476dcfb211194\",\"dweb:/ipfs/QmecH84UnZYxDZ2aL6rQtnrEExLEAfo7q4Y47yuBXdymeX\"]},\"contracts/libraries/Liquidation.sol\":{\"keccak256\":\"0x842bc44bc3cff80360ab82c5920070b12680edefe9267bdffc2d6c3c3a692d63\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://85ecd75568a0729aec06741d0575ed07dad8b7daebd7ba3114a93f6019250877\",\"dweb:/ipfs/QmQMvWdsPWsQ4t1yv6eyZy5TM7h1EZpSJdt5b8fDLcumCW\"]},\"contracts/libraries/QuadraticSwapFees.sol\":{\"keccak256\":\"0x00f6b7909be4fa1fc1ba426dd8ae659d1c5cb20c79665148898c973f55cfdccb\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://c64da0826a9b0ffc08319709f6db03339d22d24deda902a6540393251da0aecb\",\"dweb:/ipfs/QmSNwBbn2VAS8HPY4hNZusEc4DoKKZAZHtpPdjL9Gz3gs3\"]},\"contracts/libraries/Saturation.sol\":{\"keccak256\":\"0xf44bc610ece4bc7ebdb0730aa6ad69ea47647e19d4c1944c663d2d2eb4f10860\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://421fdf8d0b27132bc324a42ede9aaf23b476e5134e1073f84e824620a2a44f20\",\"dweb:/ipfs/QmbvSfMuMzDmrfPkCAEp7ydtRDWu5EUiXq4MyrGGjFErzE\"]},\"contracts/libraries/TickMath.sol\":{\"keccak256\":\"0x753813c7ed638d22edb71f48f8eb8b4283b3db2ba5b136b5c8909bd37ffa3f12\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://04dd5085b72f6d73e1b17f58148e4d03639f654bdc4fdbc173b7c92ff102fc20\",\"dweb:/ipfs/QmSg4xTQPkngjNxs84428FZdSwH4AUQpwLXaASx7Qev6oG\"]},\"contracts/libraries/Uint16Set.sol\":{\"keccak256\":\"0x26a714430fe1618d78386e953153b4bd2bf024baee54453ec9a7a0cc60e1534f\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://8667dd78541d656a09678e5f9cce4d49adc805955604ccaaec414e9b241f5e06\",\"dweb:/ipfs/QmZVWU2CzyDQfGit32HjJxDphBJMKG3d6JRuxbC682Z1gy\"]},\"contracts/libraries/Validation.sol\":{\"keccak256\":\"0x294848b2af973dbcd8b83732a57b67f14fd15e4af0668de05a2928b8eca5a463\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://fab25c941e87f6924b31e3f20742ca6b5ec1b7e4251543f4a61567a04ef4d778\",\"dweb:/ipfs/Qmf4ChH8afdHc3SfXkFPpNGp3e1hscyvnujPAMza3yuXeA\"]},\"contracts/libraries/constants.sol\":{\"keccak256\":\"0x0dfb294985a8f48287ff13e8476718ddb5334b1d8bf6bfa59a5db1dbcf6ca7c4\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://4bedcfdb2850cfb22b5daa768ab8125b4ccab97c90068d1d0ad4495bf942b362\",\"dweb:/ipfs/Qmf9p88yQN2JYRBR5D7q9BLmwhDJWpFk47ZuayrKqCyHat\"]},\"contracts/tokens/TokenController.sol\":{\"keccak256\":\"0x8b76b9ebb9385f0c4b7c0b8210fb96b11a49a8c9a3a6e855752c32a5c12d54e6\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://de87bdae81940f397136f665d68907d4e4c32f35bf2cd0c9e9305a9fe190d159\",\"dweb:/ipfs/Qmce4hM6xofBYxzAXesHX4hkiHBexoGeQpCzpeCARctnCn\"]},\"lib/mangrove-core/lib/core/BitLib.sol\":{\"keccak256\":\"0x80f6885268986b9e976b424993aa875cf7aab8464403ed675a86ade9e9be5ee3\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5a31b4e1e0dc95de9a1dbb114c40c44814de5db3e2d857c93c2524a61454f6c8\",\"dweb:/ipfs/QmRkgE9ue5rGwE6XDnszF2e2meWqAC9nnKM97xKHjHphQr\"]},\"lib/morpho-blue/src/libraries/MathLib.sol\":{\"keccak256\":\"0xa7354cbbcecef7bc0c94b61061c4e5da75515056b8e2db65e826b00d7369744a\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://d7419c59bb906fcfa49320b68f265c3200090e5c30b194766256aee70b012e08\",\"dweb:/ipfs/Qmbo4uaW6XYnudya4bb6RU6riWXFk5M3CWJge5XzTTaEfd\"]},\"lib/openzeppelin-contracts/contracts/access/AccessControl.sol\":{\"keccak256\":\"0xb64ecf1154f183412bcde47168f3af245e4120846346a0b3872c631e361156d2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://85331049a60659bc4489733ccd3cbeb177b65691122a8cb637bf9267ab25e23d\",\"dweb:/ipfs/QmbGpDcdwKTirzSCoZfE4rHG7jBSWsE4K2iSb6UCYXtLJv\"]},\"lib/openzeppelin-contracts/contracts/access/IAccessControl.sol\":{\"keccak256\":\"0x5643a5cadd1278581308b20becb48a50946c159fc31c29fc407ea9a61fc865d1\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c7d79f305a239207a24fa2174897c8ea8ff1e81cb790d440fd54c89a0e85f63e\",\"dweb:/ipfs/QmT847eeAMnRN3DaG1zsKNMn7qipNAidqv1REnKexPkrfA\"]},\"lib/openzeppelin-contracts/contracts/access/Ownable.sol\":{\"keccak256\":\"0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6\",\"dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a\"]},\"lib/openzeppelin-contracts/contracts/governance/utils/IVotes.sol\":{\"keccak256\":\"0xb541b133f2d85eb37ae866cb21123be93bd3671b6840c47f951da52d3a704548\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://22443f43ece107eaf453aa9a41a59410cece5429c2779c794fbd2c8b5aa29d38\",\"dweb:/ipfs/Qmbum2jwLYuT9aZ2fr9NMLwWFsVavonrGm2VnbAL9hP2jn\"]},\"lib/openzeppelin-contracts/contracts/governance/utils/Votes.sol\":{\"keccak256\":\"0x3f91c79d6f55db9e4fc36e1cfe6a483a7b0f5be60fecbd979555071673746d47\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9b1e3c64cbeb2757a2a1a45c69f7f3984a93b0eadd1016341b64f9d94f89d7c4\",\"dweb:/ipfs/QmP1Mj14U4vMTFa2rv2nodMbWSCov2ac9Md8W2aUcgYdKX\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC1363.sol\":{\"keccak256\":\"0xc15298eb2b9ba5e18a8c9d12f93ad17a3e162a5c1d9b85f54c8adb5827b0d4da\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1f3c3d8f81d2daf1231890a6a2f897be365d6a479b53dcd52ec2527b5d3faf41\",\"dweb:/ipfs/QmeNdkd6u4at9pd2GAyyqxzrVGGvxfLpGmAKnFoYM5ya2e\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC165.sol\":{\"keccak256\":\"0x81b022028c39007cce9920c394b9cddd1cb9f3a1c0398f254b4a6492df92ad2b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e0b61b8a5c69b4df993c3d6f94c174ab293aa8698d149bce7be2d88f82929beb\",\"dweb:/ipfs/QmbtacmB1k8ginfrHvAJpjVeqnjYGfXYrkXmMPYEb83z4t\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC20.sol\":{\"keccak256\":\"0xb5d81383d40f4006d1ce4bbad0064e7a930e17302cbe2a745e09cb403f042733\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3fc4a5681c2f00f41f49260a36ae6bbe1121dd93d470ea24d51d556eff2980be\",\"dweb:/ipfs/QmUBW6TwVWtGP96ka9TfuGivd27kH8CtkXD8RQAAecSFiR\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC3156FlashBorrower.sol\":{\"keccak256\":\"0xad94c8d7246a50210f7bcb54e5b91fc9f1c6e137263ac972ca5dd0f7f6d4d49d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6938e96fbb0cf3d961788b6c3522400e255d8057d1b9f7e08a50e0b48486b007\",\"dweb:/ipfs/QmNXG3MPzDXjHJ9iWDYCz4vi9RBTgVBnZjndnfBwMfhkyD\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC3156FlashLender.sol\":{\"keccak256\":\"0xd92910b862581523ad4e9b99f0bf738f4e62700a5e305953c7fda7db2cfd0f73\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://799f3f0f30822ac806800bbe7fe63b9991490c4e1d9edb75f5993d9350320819\",\"dweb:/ipfs/QmT8T4SokW6YxpDJQiafpeYNRGtjC5gFHxRqKTRXRyP6zB\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC5267.sol\":{\"keccak256\":\"0xf29e9088951d8a2074d872a733674618fe5c164df21b8b5cf4a6295f523ba7ad\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://562a1abc7ea505582827ce0c9a2f778360a1a8242742683af179930640020215\",\"dweb:/ipfs/QmPjx5f6KKaPfsDi1uV3ovQN9gHTAcNkMAFJZxE1Adw6VT\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC5805.sol\":{\"keccak256\":\"0xc8960b7d3e504e98883de33856a917a473c05034cd61880df2a60b5c47c214fe\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://80542373fa695b68d65b4d7222e852df5bda035a82e86ee559336c93b2bf7051\",\"dweb:/ipfs/QmZgH14DPTnKfA5gMSTMiUa6ExuqFfAozmEtLXiWc1iDiw\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC6372.sol\":{\"keccak256\":\"0xa602c8beaae2d9e2ab1ce585a54547a6d4da32d32e4d002d20ccba55b19258d8\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ac6553b5b07788a0bb67cc53596837d795280233a9a5cb3a9b3e1fde56822f78\",\"dweb:/ipfs/QmVoHXoma4ZbPKVRJJRosvhipa4rtCMU9QQvWHWKiRUxvi\"]},\"lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol\":{\"keccak256\":\"0x85cf779582f7860b00bba1d259be00e754bfbac3c2339e324d0113d682d9e9f9\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://2ddf369affd106e2a9e2b8a22a6ce3da8a6ccda14c6ba5b8c87f6b08169e6318\",\"dweb:/ipfs/QmNadAttd47ycHShxhk33JUJhrbzmyZQ7mHs7WEyG4Qkmp\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol\":{\"keccak256\":\"0x5aad1745bddba11752c3962464a3b12e0af079310cc22d1f43f0388ae1aaf8db\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://577fad916bfdfe89aadf2685322fec7562cb0ed87722923085213cd9f85d7b79\",\"dweb:/ipfs/QmSM3J6PjrAUyEoNbdhq1ECZLXczKdCTzZTBUieKHsBYEL\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0xfe37358f223eddd65d61bb62b0b7bdb69d7101b5ec8d484292b8c1583a153b8a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://28dd43f30af3c12ae0fc08dd031b1250e906ef3c95f63f30fac6fd15aee2a662\",\"dweb:/ipfs/QmUkSyWsSRx36w1ti7U6qnGnQgJq16wpMhjeJrnyn9AXwG\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Burnable.sol\":{\"keccak256\":\"0x2659248df25e34000ed214b3dc8da2160bc39874c992b477d9e2b1b3283dc073\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c345af1b0e7ea28d1216d6a04ab28f5534a5229b9edf9ca3cd0e84950ae58d26\",\"dweb:/ipfs/QmY63jtSrYpLRe8Gj1ep2vMDCKxGNNG3hnNVKBVnrs2nmA\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20FlashMint.sol\":{\"keccak256\":\"0x4d43ed4b9ff9e4c671274976d59a58dbcc7b69bd7ac11b1710f5b7607cf15b74\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0b47b42998f675cb6a51f2e74ef5906a6fa63ec6718f3fd56ee035d6f77143f9\",\"dweb:/ipfs/QmREnAXqPJBvAwfWfDzaFhNfSRWF4Jdy9ZrpHLw1KdQweY\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Permit.sol\":{\"keccak256\":\"0x6485b101d7335f0fd25abc996c5e2fc965e72e5fbd0a7ad1a465bd3f012b5fd8\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1f8d296121044e697bcd34c4cd394cebd46fc30b04ce94fccff37200872e6834\",\"dweb:/ipfs/QmTNdmLdoHgMzoCDZ8Txk9aYvwtuyeJYHf5mjLWgzGTZAu\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Votes.sol\":{\"keccak256\":\"0x62dc9346044aabf22d78541bd495aa6ca05a7f5100aed26196ba35d40b59fcb5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5221df4501c74cd4493fee1a0f0788e02c4dc78c3c601e9f557f557c5a53ea92\",\"dweb:/ipfs/QmZpzyYY9dKLrgvYhXSHT93jwqb1UGvtGNMQk5dpECY5pa\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol\":{\"keccak256\":\"0xbaffa0bcc92adf28a53cc3b68551fc3632cb8f849a0028cb8d5c06e4677715e9\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://32e6f8f6b2e883c85e6a602c0882d9962ce2f92406961244e86cd974df815912\",\"dweb:/ipfs/Qmahvx6fPpecicq1aUE1JihCxV5ep1bfuPukzrxa8Ub5PS\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Permit.sol\":{\"keccak256\":\"0x093f32ab700c2b05373387263915a75f5455cdb0f09a7630cc621e27b7b50d04\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d163e6ef21df143969df5557305e8c643a135c7660a678d0c65dca91772114a0\",\"dweb:/ipfs/QmTZUgiwEro5oLRhbJ2iSWyCqu1JTDekoFHALVUn4eHqYK\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol\":{\"keccak256\":\"0x1183b415155c1a7bf56d45edad5b17caf0da70935ac420698cbe8afb6750cbb2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://21d9edaeb3e5e8f93eb0fdab41530654e8169b1990b3bbfcf5e4527c52aa03f5\",\"dweb:/ipfs/QmWrqpNW3x5k3pTjvrT8XU1hauHnXTjqaPL2tfzMuWYosj\"]},\"lib/openzeppelin-contracts/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]},\"lib/openzeppelin-contracts/contracts/utils/Nonces.sol\":{\"keccak256\":\"0x0082767004fca261c332e9ad100868327a863a88ef724e844857128845ab350f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://132dce9686a54e025eb5ba5d2e48208f847a1ec3e60a3e527766d7bf53fb7f9e\",\"dweb:/ipfs/QmXn1a2nUZMpu2z6S88UoTfMVtY2YNh86iGrzJDYmMkKeZ\"]},\"lib/openzeppelin-contracts/contracts/utils/Panic.sol\":{\"keccak256\":\"0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a\",\"dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG\"]},\"lib/openzeppelin-contracts/contracts/utils/Pausable.sol\":{\"keccak256\":\"0xdb484371dfbb848cb6f5d70464e9ac9b2900e4164ead76bbce4fef0b44bcc68f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f9d6f6f6600a2bec622f699081b58350873b5e63ce05464d17d674a290bb8a7c\",\"dweb:/ipfs/QmQKVzSQY1PM3Bid4QhgVVZyx6B4Jx7XgaQzLKHj38vJz8\"]},\"lib/openzeppelin-contracts/contracts/utils/ShortStrings.sol\":{\"keccak256\":\"0x1fcf8cceb1a67e6c8512267e780933c4a3f63ef44756e6c818fda79be51c8402\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://617d7d57f6f9cd449068b4d23daf485676d083aae648e038d05eb3a13291de35\",\"dweb:/ipfs/QmPADWPiGaSzZDFNpFEUx4ZPqhzPkYncBpHyTfAGcfsqzy\"]},\"lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol\":{\"keccak256\":\"0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b\",\"dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM\"]},\"lib/openzeppelin-contracts/contracts/utils/Strings.sol\":{\"keccak256\":\"0x1402d9ac66fbca0a2b282cd938f01f3cd5fb1e4c696ed28b37839401674aef52\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d3e6c46b6d1ea36bd73e0ac443a53504089167b98baa24923d702a865a38d211\",\"dweb:/ipfs/QmdutUpr5KktmvgtqG2v96Bo8nVKLJ3PgPedxbsRD42CuQ\"]},\"lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol\":{\"keccak256\":\"0x6c29257484c0595ca5af8844fafe99cc5eace7447c9f5bced71d6b3a19a6a2a5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://cce7ac0bdb05f73c0918e362dea2e52426e00ddf0a1018f14accdcf78c6eb6e4\",\"dweb:/ipfs/QmbkNq5dDxww27FzFFiKgW3S7C5VoZpjdZGpSCtsb9hP32\"]},\"lib/openzeppelin-contracts/contracts/utils/cryptography/EIP712.sol\":{\"keccak256\":\"0xda8013da608bda3c9eaa9e59053d38d7888e64bb40aa557e5929cd702f8de87e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3ea13234c6b00ae79dc1a98e7e7f2faf38d37e76a687ccd0c95ad84b03ea570f\",\"dweb:/ipfs/QmWtdefDm5jiEzAjmfPMZ5B1NKVxFoMiD5ZoD68hcNTHun\"]},\"lib/openzeppelin-contracts/contracts/utils/cryptography/MessageHashUtils.sol\":{\"keccak256\":\"0x26670fef37d4adf55570ba78815eec5f31cb017e708f61886add4fc4da665631\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b16d45febff462bafd8a5669f904796a835baf607df58a8461916d3bf4f08c59\",\"dweb:/ipfs/QmU2eJFpjmT4vxeJWJyLeQb8Xht1kdB8Y6MKLDPFA9WPux\"]},\"lib/openzeppelin-contracts/contracts/utils/introspection/ERC165.sol\":{\"keccak256\":\"0x41ddfafe0d00dc22e35119d41cb0ca93673960689d35710fd12875139e64bd9f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://49d90142e15cdc4ca00de16e1882fa0a0daad8b46403628beb90c67a3efe4fc4\",\"dweb:/ipfs/QmNizYnFNcGixHxsknEccr2cQWyyQBqFF7h2bXLmefQz6M\"]},\"lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol\":{\"keccak256\":\"0x4ee0e04cc52827588793a141d5efb9830f179a17e80867cc332b3a30ceb30fd9\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://17d8f47fce493b34099ed9005c5aee3012488f063cfe1c34ed8f9e6fc3d576e5\",\"dweb:/ipfs/QmZco2GbZZhEMvG3BovyoGMAFKvfi2LhfNGQLn283LPrXf\"]},\"lib/openzeppelin-contracts/contracts/utils/math/Math.sol\":{\"keccak256\":\"0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3\",\"dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB\"]},\"lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol\":{\"keccak256\":\"0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8\",\"dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy\"]},\"lib/openzeppelin-contracts/contracts/utils/math/SignedMath.sol\":{\"keccak256\":\"0xb1970fac7b64e6c09611e6691791e848d5e3fe410fa5899e7df2e0afd77a99e3\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://db5fbb3dddd8b7047465b62575d96231ba8a2774d37fb4737fbf23340fabbb03\",\"dweb:/ipfs/QmVUSvooZKEdEdap619tcJjTLcAuH6QBdZqAzWwnAXZAWJ\"]},\"lib/openzeppelin-contracts/contracts/utils/structs/Checkpoints.sol\":{\"keccak256\":\"0x743aa2d21f6c26885e0aa6a1c84f7f7bc58fbd6df6bab32bed23f1a41f50454a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://a651d38b4261840d3744e571edf2b59455352a8c7dac5d35b019afefa343ea3b\",\"dweb:/ipfs/QmSy3UkTCQDYTjKtGwtqPRrXaofcqtVZxaF6j1dV44wqvr\"]},\"lib/openzeppelin-contracts/contracts/utils/types/Time.sol\":{\"keccak256\":\"0x36776530f012618bc7526ceb28e77b85e582cb12d9b9466a71d4bd6bf952e4cc\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9f867d046908497287d8a67643dd5d7e38c4027af4ab0a74ffbe1d6790c383c6\",\"dweb:/ipfs/QmQ7s9gMP1nkwThFmoDifnGgpUMsMe5q5ZrAxGDsNnRGza\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/Base.sol\":{\"keccak256\":\"0x4ff1a785311017d1eedb1b4737956fa383067ad34eb439abfec1d989754dde1c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f553622969b9fdb930246704a4c10dfaee6b1a4468c142fa7eb9dc292a438224\",\"dweb:/ipfs/QmcxqHnqdQsMVtgsfH9VNLmZ3g7GhgNagfq7yvNCDcCHFK\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdAssertions.sol\":{\"keccak256\":\"0x4584f551c3a875c26423c1e50a77179bc2f9c9c1ee6d0f0c6be0f89ca5ee8270\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ef118876a862b60ba8a6612b8b3f3a6c226e11a0071a2e9695f021586d1bfdbe\",\"dweb:/ipfs/QmXd2vD91PkvoFWasFkgbDi89PctmgsHVxGvGNyhjmRK2b\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdChains.sol\":{\"keccak256\":\"0xcd3e64ec9ffa19a2c0715bbdaf7ddf28887cc418e079bec4373fd6a3f9961a7b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e981a2ab738590928e9efa5f3d95a408c718eb12d73a113d7675f3ed55a026a1\",\"dweb:/ipfs/QmTgSEkWWsBRy32goRCaUkraSgpZHtgbZoKC3iEFNz5RDc\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdCheats.sol\":{\"keccak256\":\"0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41\",\"dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdError.sol\":{\"keccak256\":\"0xbf477b11a42d7611696956546bcfaa29317d1166bf65e402344599c05943fc77\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://bc2e117d1135e030862b96a6526a43feb38d396cc79857f1fb696d4eff0e5fd6\",\"dweb:/ipfs/QmdSuQ5RrQudTLsNmWXGEeVJX8gR5U9XPm6m4dwwuQnJrj\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdInvariant.sol\":{\"keccak256\":\"0x4dab3021edfa9511bbdd80c48f060ef62eaf457f99eaf841f561fc2557d9a08d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://07668628673174cf8f27f8f4e1f862bab775013ec247eb34f698c5005f229391\",\"dweb:/ipfs/QmPJsiiYwmCZXMsHmQv5tg8VF3CAhNdat7WnKLTWZZH2v5\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdJson.sol\":{\"keccak256\":\"0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974\",\"dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdMath.sol\":{\"keccak256\":\"0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92\",\"dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdStorage.sol\":{\"keccak256\":\"0xb91ab24383a5872b894fc93325eef1add6cbbf981628f18e860068bf88bb7dcc\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://2651e33b9ac8fc5803ed0a43078c1bf4fa90a0b0347aafd95776b02daccdcc57\",\"dweb:/ipfs/QmbdRyPuGJdZgnCuMnp7c1WsBo1Spf7j1KMmws1Z5rd4BQ\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdStyle.sol\":{\"keccak256\":\"0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8\",\"dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdToml.sol\":{\"keccak256\":\"0x58a72c765ed3f7ff6b105509689658795b8a3739b8931772a497155878381861\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b4a3746f4fabaeb980bd77d9e091d3904ee38a6c0e191bfa8ba6874c6f8558a3\",\"dweb:/ipfs/QmUfFDMEn461FgGEXt5HicyGD54sc28sLaQ9JRWDMBKed8\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdUtils.sol\":{\"keccak256\":\"0x7274081e11c05164fd8eadde4de8305c033e58a43008dea58065f3170ccf0737\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e9801614b6c9d3e472982e6cc68f5f1ad03682f84eafb686be65633c7132d138\",\"dweb:/ipfs/QmcQSUcrm2A7XuektnxJjvYGmZtBeA6LKPxCXRqdXBX776\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/Test.sol\":{\"keccak256\":\"0x3b4bb409a156dee9ce261458117fe9f81080ca844a8a26c07c857c46d155effe\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5792c69fe24bdc063a14e08fe68275007fdb1e5e7e343840a77938cb7e95a64e\",\"dweb:/ipfs/QmcAMhaurUwzhytJFYix4vRNeZeV8g27b8LnV3t7dvYtiK\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/Vm.sol\":{\"keccak256\":\"0x44bfadcf5a89b8058f80258f2259585c740f9cc45669a0579f4f2753ff2c6354\",\"license\":\"MIT OR Apache-2.0\",\"urls\":[\"bzz-raw://bbc366c8b3499d5030e3b2e45bac23770531f2f5243a0e80e3d5a66b6f9a312c\",\"dweb:/ipfs/QmNxDEB3BaVnKzNaWedtdMshhvCEddB1AsdJZcsQx6jdtC\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/console.sol\":{\"keccak256\":\"0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57\",\"dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/console2.sol\":{\"keccak256\":\"0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d\",\"dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/interfaces/IMulticall3.sol\":{\"keccak256\":\"0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0\",\"dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/safeconsole.sol\":{\"keccak256\":\"0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab\",\"dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3\"]},\"test/GeometricTWAPSpecFuzzTests.sol\":{\"keccak256\":\"0x8c542893376c99baaeec08cd389a12378caec36e552bd92a2d79506a4f882946\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://18888248642694123c1f99a7a1b767ba8fa35399067865c0dad12c843ad23a94\",\"dweb:/ipfs/QmQTyZK3eupL8rFxmLRGBKRrGJAWtsZVPY6rgPdkT8zMVe\"]},\"test/shared/GeometricTWAPTestFixture.sol\":{\"keccak256\":\"0x66a316a13c74698cbb99bd7f5681b6879eb300c7c2331095458be9d6b39bc386\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://cb294f187ac85de17d052e9cc9e91bfbf13c1d9d50fcaa8231fd0e3c9e4be280\",\"dweb:/ipfs/QmbZyjygUHe6Ss5sZrHjUm5jRBLSCLqCFRURjkAsGWaupu\"]},\"test/shared/StubErc20.sol\":{\"keccak256\":\"0xf3508dc98ae444d142d9993c52cebd856aba40c3e53d64bfeb63e71d190b12ee\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0cc01d254b6d5569d1cb426250db9df1b01afde9dd7b52e1efa0691112fcd918\",\"dweb:/ipfs/QmPnL9wFpSKXprrEFS9kkC2WzK2kAgWSH1snom1wiorCxn\"]},\"test/shared/utilities.sol\":{\"keccak256\":\"0xc64b147bbe73bf59fdec4202c5b7c5dbcadd7550f4b2ea2390ea689e194d7cb8\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://ab03a14b75d4b9df7795eeefd7e6d4a1d7af7b58ce948741cdd5d056a2c30416\",\"dweb:/ipfs/QmShemddxGaLyTGtC3yLdMtdHf9Gj3H8rjf2umzbFmP6aG\"]},\"test/utils/DepletedAssetUtils.sol\":{\"keccak256\":\"0x2273187d5eb782fb341d44265bd6e8afcef18ab3cfabcb4a0b77a75f15298c42\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://04d0a060b217603f6c7e38efc49be20932f08c56e46b1f9cea54eb722595458e\",\"dweb:/ipfs/QmdJHgaFbbtGDHPpHEFucTvrj4p4LT1piMPjtbrWBMXzAR\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "string", "name": "", "type": "string", "indexed": false}], "type": "event", "name": "log", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "", "type": "address", "indexed": false}], "type": "event", "name": "log_address", "anonymous": false}, {"inputs": [{"internalType": "uint256[]", "name": "val", "type": "uint256[]", "indexed": false}], "type": "event", "name": "log_array", "anonymous": false}, {"inputs": [{"internalType": "int256[]", "name": "val", "type": "int256[]", "indexed": false}], "type": "event", "name": "log_array", "anonymous": false}, {"inputs": [{"internalType": "address[]", "name": "val", "type": "address[]", "indexed": false}], "type": "event", "name": "log_array", "anonymous": false}, {"inputs": [{"internalType": "bytes", "name": "", "type": "bytes", "indexed": false}], "type": "event", "name": "log_bytes", "anonymous": false}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32", "indexed": false}], "type": "event", "name": "log_bytes32", "anonymous": false}, {"inputs": [{"internalType": "int256", "name": "", "type": "int256", "indexed": false}], "type": "event", "name": "log_int", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "address", "name": "val", "type": "address", "indexed": false}], "type": "event", "name": "log_named_address", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "uint256[]", "name": "val", "type": "uint256[]", "indexed": false}], "type": "event", "name": "log_named_array", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "int256[]", "name": "val", "type": "int256[]", "indexed": false}], "type": "event", "name": "log_named_array", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "address[]", "name": "val", "type": "address[]", "indexed": false}], "type": "event", "name": "log_named_array", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "bytes", "name": "val", "type": "bytes", "indexed": false}], "type": "event", "name": "log_named_bytes", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "bytes32", "name": "val", "type": "bytes32", "indexed": false}], "type": "event", "name": "log_named_bytes32", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "int256", "name": "val", "type": "int256", "indexed": false}, {"internalType": "uint256", "name": "decimals", "type": "uint256", "indexed": false}], "type": "event", "name": "log_named_decimal_int", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "uint256", "name": "val", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "decimals", "type": "uint256", "indexed": false}], "type": "event", "name": "log_named_decimal_uint", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "int256", "name": "val", "type": "int256", "indexed": false}], "type": "event", "name": "log_named_int", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "string", "name": "val", "type": "string", "indexed": false}], "type": "event", "name": "log_named_string", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "uint256", "name": "val", "type": "uint256", "indexed": false}], "type": "event", "name": "log_named_uint", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "", "type": "string", "indexed": false}], "type": "event", "name": "log_string", "anonymous": false}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256", "indexed": false}], "type": "event", "name": "log_uint", "anonymous": false}, {"inputs": [{"internalType": "bytes", "name": "", "type": "bytes", "indexed": false}], "type": "event", "name": "logs", "anonymous": false}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "IS_TEST", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeArtifacts", "outputs": [{"internalType": "string[]", "name": "excludedArtifacts_", "type": "string[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeContracts", "outputs": [{"internalType": "address[]", "name": "excludedContracts_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeSelectors", "outputs": [{"internalType": "struct StdInvariant.FuzzSelector[]", "name": "excludedSelectors_", "type": "tuple[]", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "bytes4[]", "name": "selectors", "type": "bytes4[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeSenders", "outputs": [{"internalType": "address[]", "name": "excludedSenders_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "failed", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetArtifactSelectors", "outputs": [{"internalType": "struct StdInvariant.FuzzArtifactSelector[]", "name": "targetedArtifactSelectors_", "type": "tuple[]", "components": [{"internalType": "string", "name": "artifact", "type": "string"}, {"internalType": "bytes4[]", "name": "selectors", "type": "bytes4[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetArtifacts", "outputs": [{"internalType": "string[]", "name": "targetedArtifacts_", "type": "string[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetContracts", "outputs": [{"internalType": "address[]", "name": "targetedContracts_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetInterfaces", "outputs": [{"internalType": "struct StdInvariant.FuzzInterface[]", "name": "targetedInterfaces_", "type": "tuple[]", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "string[]", "name": "artifacts", "type": "string[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetSelectors", "outputs": [{"internalType": "struct StdInvariant.FuzzSelector[]", "name": "targetedSelectors_", "type": "tuple[]", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "bytes4[]", "name": "selectors", "type": "bytes4[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetSenders", "outputs": [{"internalType": "address[]", "name": "targetedSenders_", "type": "address[]"}]}, {"inputs": [{"internalType": "uint24", "name": "firstBlocks", "type": "uint24"}, {"internalType": "uint24", "name": "missingBlocks", "type": "uint24"}, {"internalType": "uint24", "name": "secondBlocks", "type": "uint24"}, {"internalType": "uint24", "name": "longTermIntervalConfigFactor", "type": "uint24"}], "stateMutability": "nonpayable", "type": "function", "name": "testGeometricTWAPFuzz_ObserveIndexWithMissingBlocks"}], "devdoc": {"kind": "dev", "methods": {"testGeometricTWAPFuzz_ObserveIndexWithMissingBlocks(uint24,uint24,uint24,uint24)": {"details": "The purpose of this fuzz test is to verify the long and mid-term tick range with a wide random range for any patterns of          blocks, blocks to miss, the longTermIntervalConfig and with any number of past blocks.", "params": {"firstBlocks": "The first portion of active blocks being observed.", "longTermIntervalConfigFactor": "The initial configuration value for long-term intervals.", "missingBlocks": "The number of blocks to be intentionally missed for testing.", "secondBlocks": "The second portion of active blocks being observed after the missing blocks."}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"testGeometricTWAPFuzz_ObserveIndexWithMissingBlocks(uint24,uint24,uint24,uint24)": {"notice": "Foundry In-line fuzz test configuration. Place this directly above the function for the local config to take effect.          forge-config: default.fuzz.runs = 50"}}, "version": 1}}, "settings": {"remappings": ["1inch/=lib/1inch/", "@1inch/=lib/1inch/", "@mangrovedao/mangrove-core/=lib/mangrove-core/", "@mgv/forge-std/=lib/mangrove-core/lib/forge-std/src/", "@mgv/lib/=lib/mangrove-core/lib/", "@mgv/script/=lib/mangrove-core/script/", "@mgv/src/=lib/mangrove-core/src/", "@mgv/test/=lib/mangrove-core/test/", "@morpho-org/morpho-blue/=lib/morpho-blue/", "@openzeppelin/=lib/openzeppelin-contracts/", "ExcessivelySafeCall/=lib/ExcessivelySafeCall/src/", "core/=lib/mangrove-core/lib/core/", "ds-test/=lib/ExcessivelySafeCall/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/openzeppelin-contracts/lib/forge-std/src/", "halmos-cheatcodes/=lib/morpho-blue/lib/halmos-cheatcodes/src/", "mangrove-core/=lib/mangrove-core/", "morpho-blue/=lib/morpho-blue/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "preprocessed/=lib/mangrove-core/lib/preprocessed/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"test/GeometricTWAPSpecFuzzTests.sol": "GeometricTWAPSpecFuzzTests"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"contracts/AmmalgamPair.sol": {"keccak256": "0xe8f98844a55a216605e6c6dd6837977fafda458a6b5d0cfa1f3a18d25e9432e4", "urls": ["bzz-raw://65dda1a1de8dd64e31c666b13de3d0583b4b0da923c67065cadcddefe47562a2", "dweb:/ipfs/Qmaev9WFa4yyL8fXVoWkXwNsTTY8wY7jTBGDoKJbdwSCzS"], "license": null}, "contracts/SaturationAndGeometricTWAPState.sol": {"keccak256": "0x5e293a35668bb216a99379ea2176894314cc0f1ac68644fcf4c07017da1a4419", "urls": ["bzz-raw://00349bb86f1b657010919b4bc3f616ad56ef4883b99ab0eead36815dae93dc76", "dweb:/ipfs/QmbEd9GD2JxuDntX35YcfbSCcpRstDU9GDPUkBKGzsxvqE"], "license": "GPL-3.0-only"}, "contracts/factories/AmmalgamFactory.sol": {"keccak256": "0xe0d9baf63d9538a7ecb8bd24ea61a8cdf6fc9c1e9eb028f343548adeb8b93e4e", "urls": ["bzz-raw://bfca24db47fbbaeef8bc36996cbfed78eb48771ac71d2800f081fb58a8e8c92b", "dweb:/ipfs/QmUfYEwfmrjSmchqXi58SnpSina4qKQvD2Jbk5RqYsaoCa"], "license": "GPL-3.0-only"}, "contracts/interfaces/IAmmalgamPair.sol": {"keccak256": "0xa17e45b2348d8920d9970c5d50b300fc0a1e8d03350cdd0d1a624494baa70337", "urls": ["bzz-raw://8d252e89e5d49d1c15a0c0c0a495a325b9f8d608714b29279a7bacb1e4bf8795", "dweb:/ipfs/QmRkZ7a8JJQYEw6HQMJjjkuAK8b5Th1X1ET6BG1R8mx4qw"], "license": "GPL-3.0-only"}, "contracts/interfaces/ISaturationAndGeometricTWAPState.sol": {"keccak256": "0xc9add2ad41f8edd9d360ced8d2cd7bd18dd500304794434fb2e309fa0f5af83c", "urls": ["bzz-raw://8ecc810c544ac734ef26a2f6bebea3f3bd12d773965d297991e0e0e72892fa20", "dweb:/ipfs/QmarXc1Ut4FZzPRRZs2M2udbJjuZUJQHQ8fvmSr3bpHErR"], "license": "GPL-3.0-only"}, "contracts/interfaces/callbacks/IAmmalgamCallee.sol": {"keccak256": "0x904b858859d460a61c9e644ca87009d8e32ba20482ef218801c89c7fb1ece339", "urls": ["bzz-raw://1a7cedebbacc453e3e4e339fcc76fd3268247c13982de82b4930d59a44256c1d", "dweb:/ipfs/QmdjdvYabbwAYcV72xjiXyq278xQivFtiqD3eQ5P9Gk4f1"], "license": "GPL-3.0-only"}, "contracts/interfaces/callbacks/ITransferValidator.sol": {"keccak256": "0x6d9028fc4ad1914e6b2091e6ba46a9f836f9e67ea435c4a8fef41363f2ceaf56", "urls": ["bzz-raw://7ecaade4884d460168f6978edf35706f7b9e363de6002942b1d171a338dca6a4", "dweb:/ipfs/QmS5wgfDt5Pn68rpCytpzhiy57LcmivVFQ5XLGXUUP5Tt8"], "license": "GPL-3.0-only"}, "contracts/interfaces/factories/IAmmalgamFactory.sol": {"keccak256": "0x1c80089901e8d7d7451775b5eaa92092eb2b65319cb92fa7884281bae49f52b8", "urls": ["bzz-raw://bf1201399bb9d5046e0e788ff88394b2468771096a2a0d3500af542923e84628", "dweb:/ipfs/QmeUaPyQpbPbP5fyPUT2FfzeDgHcdyQAn1DaNg9uCuGoj9"], "license": "GPL-3.0-only"}, "contracts/interfaces/factories/IFactoryCallback.sol": {"keccak256": "0x33250cf8351adb4846a3d133a9bc06568288e4c680bcf5b1085e3bca40a35e52", "urls": ["bzz-raw://5663a39af4ed3040a58beaa5641425b9adca83c2683dd220e0c11e644fefe52b", "dweb:/ipfs/QmYB8Vf37WDzQfSpMDjv8hVicuaF1wMBzf7xjHRjGCy3wT"], "license": "GPL-3.0-only"}, "contracts/interfaces/factories/INewTokensFactory.sol": {"keccak256": "0x3b2f1ee34106d2694a9ebbe600be692bed645f4247f4a24da3d5ec46025ab3e9", "urls": ["bzz-raw://73143452a06db52deb593585fea6f2ef7c46e9ef6d649562dc39e79e4e5dca2b", "dweb:/ipfs/QmYQEy7BZWnfWKnuac8GB4QPhG5qJpaHQAfkTBoUDEuX1E"], "license": "GPL-3.0-only"}, "contracts/interfaces/tokens/IAmmalgamERC20.sol": {"keccak256": "0x44a376269170b4270ec221ce3cb31a609b394e216cc4d2e27b818361b4369829", "urls": ["bzz-raw://c48bc7586631f27ede73d3d0b4c1d7a29b1653e6c501c8b7fc9877c125f8f57e", "dweb:/ipfs/QmTSLtqnsxr7h7ct524rqYssHUo4qursmCZ7g5q3J1qQPK"], "license": "GPL-3.0-only"}, "contracts/interfaces/tokens/ITokenController.sol": {"keccak256": "0x7778001aaf582fe10005240eb6023b2b6cee3f100b6c2222bf6b9ade93732624", "urls": ["bzz-raw://91e5c4519207d6a450be1e0a8649157e86d20f8ef6a91ff6512a31cf5561a570", "dweb:/ipfs/QmUqZLW27JJZHFPf2fgLDYSWWj5gM158DdaxTTmDVukRAg"], "license": "GPL-3.0-only"}, "contracts/libraries/Convert.sol": {"keccak256": "0x944776d31291de1a9cdc6a52154c23c22b43a01c3edebe7a4140e267edbba975", "urls": ["bzz-raw://36c03749859077ba47a3acfd574f8c30f34f97def4ce81d7f4feac9a7b62794c", "dweb:/ipfs/QmdycZay5X2WrbS8qS7RycLpZbMQx7yKszWQzGU3rqidpH"], "license": "GPL-3.0-only"}, "contracts/libraries/GeometricTWAP.sol": {"keccak256": "0x3860409daa0fdb5d96f0bfb8b49cbca058b9fe32c8e32457f85d4ee2c5cdcb1e", "urls": ["bzz-raw://81fe70a80f4005a6529c7e93f1a92547ce0bf74c357c280a91e8778b378b18aa", "dweb:/ipfs/QmdRQ1DqsCu11zfbLAbrrzJ9Ups3oKgTGimYo3Zm3ALiCz"], "license": "GPL-3.0-only"}, "contracts/libraries/Interest.sol": {"keccak256": "0xbc8bfa20d7295dd70e3c716fd3dbeb5b45d313e3c609d063d186042cbf000646", "urls": ["bzz-raw://b015e8d4976d3b6d7eaca07dfcc487aeed3a7d8b4c41c8369a7476dcfb211194", "dweb:/ipfs/QmecH84UnZYxDZ2aL6rQtnrEExLEAfo7q4Y47yuBXdymeX"], "license": "GPL-3.0-only"}, "contracts/libraries/Liquidation.sol": {"keccak256": "0x842bc44bc3cff80360ab82c5920070b12680edefe9267bdffc2d6c3c3a692d63", "urls": ["bzz-raw://85ecd75568a0729aec06741d0575ed07dad8b7daebd7ba3114a93f6019250877", "dweb:/ipfs/QmQMvWdsPWsQ4t1yv6eyZy5TM7h1EZpSJdt5b8fDLcumCW"], "license": "GPL-3.0-only"}, "contracts/libraries/QuadraticSwapFees.sol": {"keccak256": "0x00f6b7909be4fa1fc1ba426dd8ae659d1c5cb20c79665148898c973f55cfdccb", "urls": ["bzz-raw://c64da0826a9b0ffc08319709f6db03339d22d24deda902a6540393251da0aecb", "dweb:/ipfs/QmSNwBbn2VAS8HPY4hNZusEc4DoKKZAZHtpPdjL9Gz3gs3"], "license": "GPL-3.0-only"}, "contracts/libraries/Saturation.sol": {"keccak256": "0xf44bc610ece4bc7ebdb0730aa6ad69ea47647e19d4c1944c663d2d2eb4f10860", "urls": ["bzz-raw://421fdf8d0b27132bc324a42ede9aaf23b476e5134e1073f84e824620a2a44f20", "dweb:/ipfs/QmbvSfMuMzDmrfPkCAEp7ydtRDWu5EUiXq4MyrGGjFErzE"], "license": "GPL-3.0-only"}, "contracts/libraries/TickMath.sol": {"keccak256": "0x753813c7ed638d22edb71f48f8eb8b4283b3db2ba5b136b5c8909bd37ffa3f12", "urls": ["bzz-raw://04dd5085b72f6d73e1b17f58148e4d03639f654bdc4fdbc173b7c92ff102fc20", "dweb:/ipfs/QmSg4xTQPkngjNxs84428FZdSwH4AUQpwLXaASx7Qev6oG"], "license": "GPL-2.0-or-later"}, "contracts/libraries/Uint16Set.sol": {"keccak256": "0x26a714430fe1618d78386e953153b4bd2bf024baee54453ec9a7a0cc60e1534f", "urls": ["bzz-raw://8667dd78541d656a09678e5f9cce4d49adc805955604ccaaec414e9b241f5e06", "dweb:/ipfs/QmZVWU2CzyDQfGit32HjJxDphBJMKG3d6JRuxbC682Z1gy"], "license": "GPL-3.0-only"}, "contracts/libraries/Validation.sol": {"keccak256": "0x294848b2af973dbcd8b83732a57b67f14fd15e4af0668de05a2928b8eca5a463", "urls": ["bzz-raw://fab25c941e87f6924b31e3f20742ca6b5ec1b7e4251543f4a61567a04ef4d778", "dweb:/ipfs/Qmf4ChH8afdHc3SfXkFPpNGp3e1hscyvnujPAMza3yuXeA"], "license": "GPL-3.0-only"}, "contracts/libraries/constants.sol": {"keccak256": "0x0dfb294985a8f48287ff13e8476718ddb5334b1d8bf6bfa59a5db1dbcf6ca7c4", "urls": ["bzz-raw://4bedcfdb2850cfb22b5daa768ab8125b4ccab97c90068d1d0ad4495bf942b362", "dweb:/ipfs/Qmf9p88yQN2JYRBR5D7q9BLmwhDJWpFk47ZuayrKqCyHat"], "license": "GPL-3.0-only"}, "contracts/tokens/TokenController.sol": {"keccak256": "0x8b76b9ebb9385f0c4b7c0b8210fb96b11a49a8c9a3a6e855752c32a5c12d54e6", "urls": ["bzz-raw://de87bdae81940f397136f665d68907d4e4c32f35bf2cd0c9e9305a9fe190d159", "dweb:/ipfs/Qmce4hM6xofBYxzAXesHX4hkiHBexoGeQpCzpeCARctnCn"], "license": "GPL-3.0-only"}, "lib/mangrove-core/lib/core/BitLib.sol": {"keccak256": "0x80f6885268986b9e976b424993aa875cf7aab8464403ed675a86ade9e9be5ee3", "urls": ["bzz-raw://5a31b4e1e0dc95de9a1dbb114c40c44814de5db3e2d857c93c2524a61454f6c8", "dweb:/ipfs/QmRkgE9ue5rGwE6XDnszF2e2meWqAC9nnKM97xKHjHphQr"], "license": "MIT"}, "lib/morpho-blue/src/libraries/MathLib.sol": {"keccak256": "0xa7354cbbcecef7bc0c94b61061c4e5da75515056b8e2db65e826b00d7369744a", "urls": ["bzz-raw://d7419c59bb906fcfa49320b68f265c3200090e5c30b194766256aee70b012e08", "dweb:/ipfs/Qmbo4uaW6XYnudya4bb6RU6riWXFk5M3CWJge5XzTTaEfd"], "license": "GPL-2.0-or-later"}, "lib/openzeppelin-contracts/contracts/access/AccessControl.sol": {"keccak256": "0xb64ecf1154f183412bcde47168f3af245e4120846346a0b3872c631e361156d2", "urls": ["bzz-raw://85331049a60659bc4489733ccd3cbeb177b65691122a8cb637bf9267ab25e23d", "dweb:/ipfs/QmbGpDcdwKTirzSCoZfE4rHG7jBSWsE4K2iSb6UCYXtLJv"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/access/IAccessControl.sol": {"keccak256": "0x5643a5cadd1278581308b20becb48a50946c159fc31c29fc407ea9a61fc865d1", "urls": ["bzz-raw://c7d79f305a239207a24fa2174897c8ea8ff1e81cb790d440fd54c89a0e85f63e", "dweb:/ipfs/QmT847eeAMnRN3DaG1zsKNMn7qipNAidqv1REnKexPkrfA"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/access/Ownable.sol": {"keccak256": "0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb", "urls": ["bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6", "dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/governance/utils/IVotes.sol": {"keccak256": "0xb541b133f2d85eb37ae866cb21123be93bd3671b6840c47f951da52d3a704548", "urls": ["bzz-raw://22443f43ece107eaf453aa9a41a59410cece5429c2779c794fbd2c8b5aa29d38", "dweb:/ipfs/Qmbum2jwLYuT9aZ2fr9NMLwWFsVavonrGm2VnbAL9hP2jn"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/governance/utils/Votes.sol": {"keccak256": "0x3f91c79d6f55db9e4fc36e1cfe6a483a7b0f5be60fecbd979555071673746d47", "urls": ["bzz-raw://9b1e3c64cbeb2757a2a1a45c69f7f3984a93b0eadd1016341b64f9d94f89d7c4", "dweb:/ipfs/QmP1Mj14U4vMTFa2rv2nodMbWSCov2ac9Md8W2aUcgYdKX"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC1363.sol": {"keccak256": "0xc15298eb2b9ba5e18a8c9d12f93ad17a3e162a5c1d9b85f54c8adb5827b0d4da", "urls": ["bzz-raw://1f3c3d8f81d2daf1231890a6a2f897be365d6a479b53dcd52ec2527b5d3faf41", "dweb:/ipfs/QmeNdkd6u4at9pd2GAyyqxzrVGGvxfLpGmAKnFoYM5ya2e"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC165.sol": {"keccak256": "0x81b022028c39007cce9920c394b9cddd1cb9f3a1c0398f254b4a6492df92ad2b", "urls": ["bzz-raw://e0b61b8a5c69b4df993c3d6f94c174ab293aa8698d149bce7be2d88f82929beb", "dweb:/ipfs/QmbtacmB1k8ginfrHvAJpjVeqnjYGfXYrkXmMPYEb83z4t"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC20.sol": {"keccak256": "0xb5d81383d40f4006d1ce4bbad0064e7a930e17302cbe2a745e09cb403f042733", "urls": ["bzz-raw://3fc4a5681c2f00f41f49260a36ae6bbe1121dd93d470ea24d51d556eff2980be", "dweb:/ipfs/QmUBW6TwVWtGP96ka9TfuGivd27kH8CtkXD8RQAAecSFiR"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC3156FlashBorrower.sol": {"keccak256": "0xad94c8d7246a50210f7bcb54e5b91fc9f1c6e137263ac972ca5dd0f7f6d4d49d", "urls": ["bzz-raw://6938e96fbb0cf3d961788b6c3522400e255d8057d1b9f7e08a50e0b48486b007", "dweb:/ipfs/QmNXG3MPzDXjHJ9iWDYCz4vi9RBTgVBnZjndnfBwMfhkyD"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC3156FlashLender.sol": {"keccak256": "0xd92910b862581523ad4e9b99f0bf738f4e62700a5e305953c7fda7db2cfd0f73", "urls": ["bzz-raw://799f3f0f30822ac806800bbe7fe63b9991490c4e1d9edb75f5993d9350320819", "dweb:/ipfs/QmT8T4SokW6YxpDJQiafpeYNRGtjC5gFHxRqKTRXRyP6zB"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC5267.sol": {"keccak256": "0xf29e9088951d8a2074d872a733674618fe5c164df21b8b5cf4a6295f523ba7ad", "urls": ["bzz-raw://562a1abc7ea505582827ce0c9a2f778360a1a8242742683af179930640020215", "dweb:/ipfs/QmPjx5f6KKaPfsDi1uV3ovQN9gHTAcNkMAFJZxE1Adw6VT"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC5805.sol": {"keccak256": "0xc8960b7d3e504e98883de33856a917a473c05034cd61880df2a60b5c47c214fe", "urls": ["bzz-raw://80542373fa695b68d65b4d7222e852df5bda035a82e86ee559336c93b2bf7051", "dweb:/ipfs/QmZgH14DPTnKfA5gMSTMiUa6ExuqFfAozmEtLXiWc1iDiw"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC6372.sol": {"keccak256": "0xa602c8beaae2d9e2ab1ce585a54547a6d4da32d32e4d002d20ccba55b19258d8", "urls": ["bzz-raw://ac6553b5b07788a0bb67cc53596837d795280233a9a5cb3a9b3e1fde56822f78", "dweb:/ipfs/QmVoHXoma4ZbPKVRJJRosvhipa4rtCMU9QQvWHWKiRUxvi"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol": {"keccak256": "0x85cf779582f7860b00bba1d259be00e754bfbac3c2339e324d0113d682d9e9f9", "urls": ["bzz-raw://2ddf369affd106e2a9e2b8a22a6ce3da8a6ccda14c6ba5b8c87f6b08169e6318", "dweb:/ipfs/QmNadAttd47ycHShxhk33JUJhrbzmyZQ7mHs7WEyG4Qkmp"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol": {"keccak256": "0x5aad1745bddba11752c3962464a3b12e0af079310cc22d1f43f0388ae1aaf8db", "urls": ["bzz-raw://577fad916bfdfe89aadf2685322fec7562cb0ed87722923085213cd9f85d7b79", "dweb:/ipfs/QmSM3J6PjrAUyEoNbdhq1ECZLXczKdCTzZTBUieKHsBYEL"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol": {"keccak256": "0xfe37358f223eddd65d61bb62b0b7bdb69d7101b5ec8d484292b8c1583a153b8a", "urls": ["bzz-raw://28dd43f30af3c12ae0fc08dd031b1250e906ef3c95f63f30fac6fd15aee2a662", "dweb:/ipfs/QmUkSyWsSRx36w1ti7U6qnGnQgJq16wpMhjeJrnyn9AXwG"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Burnable.sol": {"keccak256": "0x2659248df25e34000ed214b3dc8da2160bc39874c992b477d9e2b1b3283dc073", "urls": ["bzz-raw://c345af1b0e7ea28d1216d6a04ab28f5534a5229b9edf9ca3cd0e84950ae58d26", "dweb:/ipfs/QmY63jtSrYpLRe8Gj1ep2vMDCKxGNNG3hnNVKBVnrs2nmA"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20FlashMint.sol": {"keccak256": "0x4d43ed4b9ff9e4c671274976d59a58dbcc7b69bd7ac11b1710f5b7607cf15b74", "urls": ["bzz-raw://0b47b42998f675cb6a51f2e74ef5906a6fa63ec6718f3fd56ee035d6f77143f9", "dweb:/ipfs/QmREnAXqPJBvAwfWfDzaFhNfSRWF4Jdy9ZrpHLw1KdQweY"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Permit.sol": {"keccak256": "0x6485b101d7335f0fd25abc996c5e2fc965e72e5fbd0a7ad1a465bd3f012b5fd8", "urls": ["bzz-raw://1f8d296121044e697bcd34c4cd394cebd46fc30b04ce94fccff37200872e6834", "dweb:/ipfs/QmTNdmLdoHgMzoCDZ8Txk9aYvwtuyeJYHf5mjLWgzGTZAu"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Votes.sol": {"keccak256": "0x62dc9346044aabf22d78541bd495aa6ca05a7f5100aed26196ba35d40b59fcb5", "urls": ["bzz-raw://5221df4501c74cd4493fee1a0f0788e02c4dc78c3c601e9f557f557c5a53ea92", "dweb:/ipfs/QmZpzyYY9dKLrgvYhXSHT93jwqb1UGvtGNMQk5dpECY5pa"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"keccak256": "0xbaffa0bcc92adf28a53cc3b68551fc3632cb8f849a0028cb8d5c06e4677715e9", "urls": ["bzz-raw://32e6f8f6b2e883c85e6a602c0882d9962ce2f92406961244e86cd974df815912", "dweb:/ipfs/Qmahvx6fPpecicq1aUE1JihCxV5ep1bfuPukzrxa8Ub5PS"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Permit.sol": {"keccak256": "0x093f32ab700c2b05373387263915a75f5455cdb0f09a7630cc621e27b7b50d04", "urls": ["bzz-raw://d163e6ef21df143969df5557305e8c643a135c7660a678d0c65dca91772114a0", "dweb:/ipfs/QmTZUgiwEro5oLRhbJ2iSWyCqu1JTDekoFHALVUn4eHqYK"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol": {"keccak256": "0x1183b415155c1a7bf56d45edad5b17caf0da70935ac420698cbe8afb6750cbb2", "urls": ["bzz-raw://21d9edaeb3e5e8f93eb0fdab41530654e8169b1990b3bbfcf5e4527c52aa03f5", "dweb:/ipfs/QmWrqpNW3x5k3pTjvrT8XU1hauHnXTjqaPL2tfzMuWYosj"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Context.sol": {"keccak256": "0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2", "urls": ["bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12", "dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Nonces.sol": {"keccak256": "0x0082767004fca261c332e9ad100868327a863a88ef724e844857128845ab350f", "urls": ["bzz-raw://132dce9686a54e025eb5ba5d2e48208f847a1ec3e60a3e527766d7bf53fb7f9e", "dweb:/ipfs/QmXn1a2nUZMpu2z6S88UoTfMVtY2YNh86iGrzJDYmMkKeZ"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Panic.sol": {"keccak256": "0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a", "urls": ["bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a", "dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Pausable.sol": {"keccak256": "0xdb484371dfbb848cb6f5d70464e9ac9b2900e4164ead76bbce4fef0b44bcc68f", "urls": ["bzz-raw://f9d6f6f6600a2bec622f699081b58350873b5e63ce05464d17d674a290bb8a7c", "dweb:/ipfs/QmQKVzSQY1PM3Bid4QhgVVZyx6B4Jx7XgaQzLKHj38vJz8"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/ShortStrings.sol": {"keccak256": "0x1fcf8cceb1a67e6c8512267e780933c4a3f63ef44756e6c818fda79be51c8402", "urls": ["bzz-raw://617d7d57f6f9cd449068b4d23daf485676d083aae648e038d05eb3a13291de35", "dweb:/ipfs/QmPADWPiGaSzZDFNpFEUx4ZPqhzPkYncBpHyTfAGcfsqzy"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol": {"keccak256": "0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97", "urls": ["bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b", "dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Strings.sol": {"keccak256": "0x1402d9ac66fbca0a2b282cd938f01f3cd5fb1e4c696ed28b37839401674aef52", "urls": ["bzz-raw://d3e6c46b6d1ea36bd73e0ac443a53504089167b98baa24923d702a865a38d211", "dweb:/ipfs/QmdutUpr5KktmvgtqG2v96Bo8nVKLJ3PgPedxbsRD42CuQ"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol": {"keccak256": "0x6c29257484c0595ca5af8844fafe99cc5eace7447c9f5bced71d6b3a19a6a2a5", "urls": ["bzz-raw://cce7ac0bdb05f73c0918e362dea2e52426e00ddf0a1018f14accdcf78c6eb6e4", "dweb:/ipfs/QmbkNq5dDxww27FzFFiKgW3S7C5VoZpjdZGpSCtsb9hP32"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/cryptography/EIP712.sol": {"keccak256": "0xda8013da608bda3c9eaa9e59053d38d7888e64bb40aa557e5929cd702f8de87e", "urls": ["bzz-raw://3ea13234c6b00ae79dc1a98e7e7f2faf38d37e76a687ccd0c95ad84b03ea570f", "dweb:/ipfs/QmWtdefDm5jiEzAjmfPMZ5B1NKVxFoMiD5ZoD68hcNTHun"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/cryptography/MessageHashUtils.sol": {"keccak256": "0x26670fef37d4adf55570ba78815eec5f31cb017e708f61886add4fc4da665631", "urls": ["bzz-raw://b16d45febff462bafd8a5669f904796a835baf607df58a8461916d3bf4f08c59", "dweb:/ipfs/QmU2eJFpjmT4vxeJWJyLeQb8Xht1kdB8Y6MKLDPFA9WPux"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/introspection/ERC165.sol": {"keccak256": "0x41ddfafe0d00dc22e35119d41cb0ca93673960689d35710fd12875139e64bd9f", "urls": ["bzz-raw://49d90142e15cdc4ca00de16e1882fa0a0daad8b46403628beb90c67a3efe4fc4", "dweb:/ipfs/QmNizYnFNcGixHxsknEccr2cQWyyQBqFF7h2bXLmefQz6M"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol": {"keccak256": "0x4ee0e04cc52827588793a141d5efb9830f179a17e80867cc332b3a30ceb30fd9", "urls": ["bzz-raw://17d8f47fce493b34099ed9005c5aee3012488f063cfe1c34ed8f9e6fc3d576e5", "dweb:/ipfs/QmZco2GbZZhEMvG3BovyoGMAFKvfi2LhfNGQLn283LPrXf"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/math/Math.sol": {"keccak256": "0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6", "urls": ["bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3", "dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol": {"keccak256": "0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54", "urls": ["bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8", "dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/math/SignedMath.sol": {"keccak256": "0xb1970fac7b64e6c09611e6691791e848d5e3fe410fa5899e7df2e0afd77a99e3", "urls": ["bzz-raw://db5fbb3dddd8b7047465b62575d96231ba8a2774d37fb4737fbf23340fabbb03", "dweb:/ipfs/QmVUSvooZKEdEdap619tcJjTLcAuH6QBdZqAzWwnAXZAWJ"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/structs/Checkpoints.sol": {"keccak256": "0x743aa2d21f6c26885e0aa6a1c84f7f7bc58fbd6df6bab32bed23f1a41f50454a", "urls": ["bzz-raw://a651d38b4261840d3744e571edf2b59455352a8c7dac5d35b019afefa343ea3b", "dweb:/ipfs/QmSy3UkTCQDYTjKtGwtqPRrXaofcqtVZxaF6j1dV44wqvr"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/types/Time.sol": {"keccak256": "0x36776530f012618bc7526ceb28e77b85e582cb12d9b9466a71d4bd6bf952e4cc", "urls": ["bzz-raw://9f867d046908497287d8a67643dd5d7e38c4027af4ab0a74ffbe1d6790c383c6", "dweb:/ipfs/QmQ7s9gMP1nkwThFmoDifnGgpUMsMe5q5ZrAxGDsNnRGza"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/Base.sol": {"keccak256": "0x4ff1a785311017d1eedb1b4737956fa383067ad34eb439abfec1d989754dde1c", "urls": ["bzz-raw://f553622969b9fdb930246704a4c10dfaee6b1a4468c142fa7eb9dc292a438224", "dweb:/ipfs/QmcxqHnqdQsMVtgsfH9VNLmZ3g7GhgNagfq7yvNCDcCHFK"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdAssertions.sol": {"keccak256": "0x4584f551c3a875c26423c1e50a77179bc2f9c9c1ee6d0f0c6be0f89ca5ee8270", "urls": ["bzz-raw://ef118876a862b60ba8a6612b8b3f3a6c226e11a0071a2e9695f021586d1bfdbe", "dweb:/ipfs/QmXd2vD91PkvoFWasFkgbDi89PctmgsHVxGvGNyhjmRK2b"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdChains.sol": {"keccak256": "0xcd3e64ec9ffa19a2c0715bbdaf7ddf28887cc418e079bec4373fd6a3f9961a7b", "urls": ["bzz-raw://e981a2ab738590928e9efa5f3d95a408c718eb12d73a113d7675f3ed55a026a1", "dweb:/ipfs/QmTgSEkWWsBRy32goRCaUkraSgpZHtgbZoKC3iEFNz5RDc"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdCheats.sol": {"keccak256": "0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746", "urls": ["bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41", "dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdError.sol": {"keccak256": "0xbf477b11a42d7611696956546bcfaa29317d1166bf65e402344599c05943fc77", "urls": ["bzz-raw://bc2e117d1135e030862b96a6526a43feb38d396cc79857f1fb696d4eff0e5fd6", "dweb:/ipfs/QmdSuQ5RrQudTLsNmWXGEeVJX8gR5U9XPm6m4dwwuQnJrj"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdInvariant.sol": {"keccak256": "0x4dab3021edfa9511bbdd80c48f060ef62eaf457f99eaf841f561fc2557d9a08d", "urls": ["bzz-raw://07668628673174cf8f27f8f4e1f862bab775013ec247eb34f698c5005f229391", "dweb:/ipfs/QmPJsiiYwmCZXMsHmQv5tg8VF3CAhNdat7WnKLTWZZH2v5"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdJson.sol": {"keccak256": "0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500", "urls": ["bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974", "dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdMath.sol": {"keccak256": "0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2", "urls": ["bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92", "dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdStorage.sol": {"keccak256": "0xb91ab24383a5872b894fc93325eef1add6cbbf981628f18e860068bf88bb7dcc", "urls": ["bzz-raw://2651e33b9ac8fc5803ed0a43078c1bf4fa90a0b0347aafd95776b02daccdcc57", "dweb:/ipfs/QmbdRyPuGJdZgnCuMnp7c1WsBo1Spf7j1KMmws1Z5rd4BQ"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdStyle.sol": {"keccak256": "0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d", "urls": ["bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8", "dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdToml.sol": {"keccak256": "0x58a72c765ed3f7ff6b105509689658795b8a3739b8931772a497155878381861", "urls": ["bzz-raw://b4a3746f4fabaeb980bd77d9e091d3904ee38a6c0e191bfa8ba6874c6f8558a3", "dweb:/ipfs/QmUfFDMEn461FgGEXt5HicyGD54sc28sLaQ9JRWDMBKed8"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdUtils.sol": {"keccak256": "0x7274081e11c05164fd8eadde4de8305c033e58a43008dea58065f3170ccf0737", "urls": ["bzz-raw://e9801614b6c9d3e472982e6cc68f5f1ad03682f84eafb686be65633c7132d138", "dweb:/ipfs/QmcQSUcrm2A7XuektnxJjvYGmZtBeA6LKPxCXRqdXBX776"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/Test.sol": {"keccak256": "0x3b4bb409a156dee9ce261458117fe9f81080ca844a8a26c07c857c46d155effe", "urls": ["bzz-raw://5792c69fe24bdc063a14e08fe68275007fdb1e5e7e343840a77938cb7e95a64e", "dweb:/ipfs/QmcAMhaurUwzhytJFYix4vRNeZeV8g27b8LnV3t7dvYtiK"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/Vm.sol": {"keccak256": "0x44bfadcf5a89b8058f80258f2259585c740f9cc45669a0579f4f2753ff2c6354", "urls": ["bzz-raw://bbc366c8b3499d5030e3b2e45bac23770531f2f5243a0e80e3d5a66b6f9a312c", "dweb:/ipfs/QmNxDEB3BaVnKzNaWedtdMshhvCEddB1AsdJZcsQx6jdtC"], "license": "MIT OR Apache-2.0"}, "lib/openzeppelin-contracts/lib/forge-std/src/console.sol": {"keccak256": "0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5", "urls": ["bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57", "dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/console2.sol": {"keccak256": "0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f", "urls": ["bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d", "dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/interfaces/IMulticall3.sol": {"keccak256": "0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a", "urls": ["bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0", "dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/safeconsole.sol": {"keccak256": "0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11", "urls": ["bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab", "dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3"], "license": "MIT"}, "test/GeometricTWAPSpecFuzzTests.sol": {"keccak256": "0x8c542893376c99baaeec08cd389a12378caec36e552bd92a2d79506a4f882946", "urls": ["bzz-raw://18888248642694123c1f99a7a1b767ba8fa35399067865c0dad12c843ad23a94", "dweb:/ipfs/QmQTyZK3eupL8rFxmLRGBKRrGJAWtsZVPY6rgPdkT8zMVe"], "license": "GPL-3.0-only"}, "test/shared/GeometricTWAPTestFixture.sol": {"keccak256": "0x66a316a13c74698cbb99bd7f5681b6879eb300c7c2331095458be9d6b39bc386", "urls": ["bzz-raw://cb294f187ac85de17d052e9cc9e91bfbf13c1d9d50fcaa8231fd0e3c9e4be280", "dweb:/ipfs/QmbZyjygUHe6Ss5sZrHjUm5jRBLSCLqCFRURjkAsGWaupu"], "license": "GPL-3.0-only"}, "test/shared/StubErc20.sol": {"keccak256": "0xf3508dc98ae444d142d9993c52cebd856aba40c3e53d64bfeb63e71d190b12ee", "urls": ["bzz-raw://0cc01d254b6d5569d1cb426250db9df1b01afde9dd7b52e1efa0691112fcd918", "dweb:/ipfs/QmPnL9wFpSKXprrEFS9kkC2WzK2kAgWSH1snom1wiorCxn"], "license": "MIT"}, "test/shared/utilities.sol": {"keccak256": "0xc64b147bbe73bf59fdec4202c5b7c5dbcadd7550f4b2ea2390ea689e194d7cb8", "urls": ["bzz-raw://ab03a14b75d4b9df7795eeefd7e6d4a1d7af7b58ce948741cdd5d056a2c30416", "dweb:/ipfs/QmShemddxGaLyTGtC3yLdMtdHf9Gj3H8rjf2umzbFmP6aG"], "license": "GPL-3.0-only"}, "test/utils/DepletedAssetUtils.sol": {"keccak256": "0x2273187d5eb782fb341d44265bd6e8afcef18ab3cfabcb4a0b77a75f15298c42", "urls": ["bzz-raw://04d0a060b217603f6c7e38efc49be20932f08c56e46b1f9cea54eb722595458e", "dweb:/ipfs/QmdJHgaFbbtGDHPpHEFucTvrj4p4LT1piMPjtbrWBMXzAR"], "license": "GPL-3.0-only"}}, "version": 1}, "id": 121}